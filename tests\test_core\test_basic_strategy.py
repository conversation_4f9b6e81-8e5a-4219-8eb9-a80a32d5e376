"""
Unit tests for Basic Strategy charts.

Tests P1_T4 implementation: Basic Strategy lookup tables for 6-deck H17 DAS rules.
"""

import unittest
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from core.card import Card, Suit, Rank
from core.hand import Hand
from utils.basic_strategy_charts import BasicStrategyCharts, BasicStrategyAction


class TestBasicStrategyCharts(unittest.TestCase):
    """Test cases for the BasicStrategyCharts class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.charts = BasicStrategyCharts()
    
    def test_hard_totals_basic(self):
        """Test basic hard total decisions."""
        # Test hard 12 vs dealer 6 (should stand)
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.TWO))
        
        action = self.charts.get_action(hand, dealer_upcard=6)
        self.assertEqual(action, BasicStrategyAction.STAND)
        
        # Test hard 12 vs dealer 7 (should hit)
        action = self.charts.get_action(hand, dealer_upcard=7)
        self.assertEqual(action, BasicStrategyAction.HIT)
    
    def test_hard_totals_doubling(self):
        """Test hard total doubling decisions."""
        # Test hard 11 vs dealer 6 (should double)
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.FIVE))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        action = self.charts.get_action(hand, dealer_upcard=6, can_double=True)
        self.assertEqual(action, BasicStrategyAction.DOUBLE)
        
        # Same hand but can't double (should hit)
        action = self.charts.get_action(hand, dealer_upcard=6, can_double=False)
        self.assertEqual(action, BasicStrategyAction.HIT)
    
    def test_soft_totals(self):
        """Test soft hand decisions."""
        # Test soft 18 (A,7) vs dealer 6 (should double)
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.ACE))
        hand.add_card(Card(Suit.SPADES, Rank.SEVEN))
        
        action = self.charts.get_action(hand, dealer_upcard=6, can_double=True)
        self.assertEqual(action, BasicStrategyAction.DOUBLE)
        
        # Test soft 18 vs dealer 9 (should hit)
        action = self.charts.get_action(hand, dealer_upcard=9)
        self.assertEqual(action, BasicStrategyAction.HIT)
        
        # Test soft 19 (A,8) vs dealer 6 (should stand)
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.ACE))
        hand.add_card(Card(Suit.SPADES, Rank.EIGHT))
        
        action = self.charts.get_action(hand, dealer_upcard=6)
        self.assertEqual(action, BasicStrategyAction.STAND)
    
    def test_pairs_splitting(self):
        """Test pair splitting decisions."""
        # Test 8,8 vs dealer 10 (should split)
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.EIGHT))
        hand.add_card(Card(Suit.SPADES, Rank.EIGHT))
        
        action = self.charts.get_action(hand, dealer_upcard=10, can_split=True)
        self.assertEqual(action, BasicStrategyAction.SPLIT)
        
        # Test A,A vs dealer 10 (should split)
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.ACE))
        hand.add_card(Card(Suit.SPADES, Rank.ACE))
        
        action = self.charts.get_action(hand, dealer_upcard=10, can_split=True)
        self.assertEqual(action, BasicStrategyAction.SPLIT)
        
        # Test 5,5 vs dealer 6 (should double, not split)
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.FIVE))
        hand.add_card(Card(Suit.SPADES, Rank.FIVE))
        
        action = self.charts.get_action(hand, dealer_upcard=6, can_split=True, can_double=True)
        self.assertEqual(action, BasicStrategyAction.DOUBLE)
    
    def test_face_card_pairs(self):
        """Test face card pair handling."""
        # Test K,Q (should not be considered a pair for splitting)
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.KING))
        hand.add_card(Card(Suit.SPADES, Rank.QUEEN))
        
        # Should not split (different ranks), should stand (hard 20)
        action = self.charts.get_action(hand, dealer_upcard=6, can_split=True)
        self.assertEqual(action, BasicStrategyAction.STAND)
        
        # Test 10,J (should not be considered a pair for splitting)
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.JACK))
        
        action = self.charts.get_action(hand, dealer_upcard=6, can_split=True)
        self.assertEqual(action, BasicStrategyAction.STAND)
    
    def test_ace_handling(self):
        """Test Ace value handling in lookups."""
        # Test vs dealer Ace (should be converted to 11)
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        # Hard 16 vs dealer A (should hit)
        action = self.charts.get_action(hand, dealer_upcard=1)  # Ace as 1
        self.assertEqual(action, BasicStrategyAction.HIT)
        
        action = self.charts.get_action(hand, dealer_upcard=11)  # Ace as 11
        self.assertEqual(action, BasicStrategyAction.HIT)
    
    def test_edge_cases(self):
        """Test edge cases and boundary conditions."""
        # Test very low totals
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TWO))
        hand.add_card(Card(Suit.SPADES, Rank.TWO))
        
        action = self.charts.get_action(hand, dealer_upcard=6)
        # Should split 2,2 vs 6
        self.assertEqual(action, BasicStrategyAction.SPLIT)
        
        # Test high totals
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.NINE))
        
        action = self.charts.get_action(hand, dealer_upcard=10)
        self.assertEqual(action, BasicStrategyAction.STAND)
    
    def test_save_and_load(self):
        """Test saving and loading charts to/from file."""
        import tempfile

        # Save charts to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_path = f.name

        try:
            self.charts.save_to_file(temp_path)

            # Load charts from file
            loaded_charts = BasicStrategyCharts.load_from_file(temp_path)

            # Test that loaded charts work the same
            hand = Hand()
            hand.add_card(Card(Suit.HEARTS, Rank.TEN))
            hand.add_card(Card(Suit.SPADES, Rank.ACE))

            # Both should give same result
            original_action = self.charts.get_action(hand, dealer_upcard=6)
            loaded_action = loaded_charts.get_action(hand, dealer_upcard=6)
            self.assertEqual(original_action, loaded_action)

        finally:
            # Clean up
            if os.path.exists(temp_path):
                os.unlink(temp_path)
    
    def test_chart_completeness(self):
        """Test that charts cover all expected scenarios."""
        # Test a few key scenarios to ensure charts are working
        test_cases = [
            # (hand_cards, dealer_up, expected_action_type)
            ([Rank.TEN, Rank.SIX], 6, BasicStrategyAction.STAND),  # Hard 16 vs 6
            ([Rank.ACE, Rank.SIX], 6, BasicStrategyAction.DOUBLE),  # Soft 17 vs 6
            ([Rank.EIGHT, Rank.EIGHT], 10, BasicStrategyAction.SPLIT),  # 8,8 vs 10
        ]

        for cards, dealer_up, expected in test_cases:
            hand = Hand()
            for rank in cards:
                hand.add_card(Card(Suit.HEARTS, rank))

            action = self.charts.get_action(hand, dealer_up, can_split=True, can_double=True)
            self.assertEqual(action, expected)


if __name__ == '__main__':
    unittest.main()
