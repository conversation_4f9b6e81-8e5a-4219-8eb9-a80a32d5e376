"""
Unit tests for Hand class.

Tests P1_T2 implementation: Player/Hand representation with accurate Ace handling.
"""

import unittest
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from core.card import Card, Suit, Rank
from core.hand import Hand


class TestHand(unittest.TestCase):
    """Test cases for the Hand class."""
    
    def test_empty_hand(self):
        """Test empty hand creation and properties."""
        hand = Hand()
        self.assertEqual(len(hand), 0)
        self.assertEqual(hand.get_value(), 0)
        self.assertEqual(hand.get_values(), (0, 0))
        self.assertFalse(hand.is_blackjack())
        self.assertFalse(hand.is_bust())
        self.assertFalse(hand.is_pair())
    
    def test_simple_hand_values(self):
        """Test hand values with non-Ace cards."""
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.FIVE))
        hand.add_card(Card(Suit.SPADES, Rank.SEVEN))
        
        self.assertEqual(hand.get_value(), 12)
        self.assertEqual(hand.get_values(), (12, 12))
        self.assertTrue(hand.is_hard())
        self.assertFalse(hand.is_soft())
    
    def test_ace_handling_soft_hand(self):
        """Test Ace handling in soft hands."""
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.ACE))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        # Ace + 6 should be soft 17 (Ace as 11)
        self.assertEqual(hand.get_value(), 17)
        self.assertEqual(hand.get_values(), (7, 17))
        self.assertTrue(hand.is_soft())
        self.assertFalse(hand.is_hard())
    
    def test_ace_handling_hard_hand(self):
        """Test Ace handling when forced to be hard."""
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.ACE))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        hand.add_card(Card(Suit.CLUBS, Rank.EIGHT))
        
        # Ace + 6 + 8 = 15 (Ace as 1, would be 25 as 11)
        self.assertEqual(hand.get_value(), 15)
        self.assertEqual(hand.get_values(), (15, 15))
        self.assertTrue(hand.is_hard())
        self.assertFalse(hand.is_soft())
    
    def test_multiple_aces(self):
        """Test handling multiple Aces."""
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.ACE))
        hand.add_card(Card(Suit.SPADES, Rank.ACE))
        hand.add_card(Card(Suit.CLUBS, Rank.NINE))
        
        # Ace + Ace + 9 = 21 (one Ace as 11, one as 1)
        self.assertEqual(hand.get_value(), 21)
        self.assertEqual(hand.get_values(), (11, 21))
        self.assertTrue(hand.is_soft())
    
    def test_blackjack_detection(self):
        """Test natural blackjack detection."""
        # Natural blackjack
        hand1 = Hand()
        hand1.add_card(Card(Suit.HEARTS, Rank.ACE))
        hand1.add_card(Card(Suit.SPADES, Rank.KING))
        self.assertTrue(hand1.is_blackjack())
        self.assertEqual(hand1.get_value(), 21)
        
        # 21 but not blackjack (3 cards)
        hand2 = Hand()
        hand2.add_card(Card(Suit.HEARTS, Rank.SEVEN))
        hand2.add_card(Card(Suit.SPADES, Rank.SEVEN))
        hand2.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        self.assertFalse(hand2.is_blackjack())
        self.assertEqual(hand2.get_value(), 21)
    
    def test_bust_detection(self):
        """Test bust detection."""
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.KING))
        hand.add_card(Card(Suit.SPADES, Rank.QUEEN))
        hand.add_card(Card(Suit.CLUBS, Rank.FIVE))
        
        self.assertTrue(hand.is_bust())
        self.assertEqual(hand.get_value(), 25)
    
    def test_pair_detection(self):
        """Test pair detection for splitting."""
        # Same rank pair
        hand1 = Hand()
        hand1.add_card(Card(Suit.HEARTS, Rank.EIGHT))
        hand1.add_card(Card(Suit.SPADES, Rank.EIGHT))
        self.assertTrue(hand1.is_pair())
        self.assertTrue(hand1.can_split())
        
        # Different ranks
        hand2 = Hand()
        hand2.add_card(Card(Suit.HEARTS, Rank.EIGHT))
        hand2.add_card(Card(Suit.SPADES, Rank.NINE))
        self.assertFalse(hand2.is_pair())
        self.assertFalse(hand2.can_split())
        
        # Face cards of different types (should be pair for splitting)
        hand3 = Hand()
        hand3.add_card(Card(Suit.HEARTS, Rank.KING))
        hand3.add_card(Card(Suit.SPADES, Rank.QUEEN))
        self.assertFalse(hand3.is_pair())  # Different ranks
    
    def test_splitting(self):
        """Test hand splitting functionality."""
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.EIGHT))
        hand.add_card(Card(Suit.SPADES, Rank.EIGHT))
        
        # Split the hand
        new_hand = hand.split()
        
        # Original hand should have one card
        self.assertEqual(len(hand), 1)
        self.assertEqual(hand.get_value(), 8)
        
        # New hand should have one card
        self.assertEqual(len(new_hand), 1)
        self.assertEqual(new_hand.get_value(), 8)
        
        # Neither should be splittable anymore
        self.assertFalse(hand.can_split())
        self.assertFalse(new_hand.can_split())
    
    def test_doubling_rules(self):
        """Test doubling down rules."""
        # Can double with 2 cards
        hand1 = Hand()
        hand1.add_card(Card(Suit.HEARTS, Rank.FIVE))
        hand1.add_card(Card(Suit.SPADES, Rank.SIX))
        self.assertTrue(hand1.can_double())
        
        # Cannot double with 3 cards
        hand1.add_card(Card(Suit.CLUBS, Rank.TWO))
        self.assertFalse(hand1.can_double())
    
    def test_hand_string_representation(self):
        """Test hand string representation."""
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.ACE))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        hand_str = str(hand)
        self.assertIn("A♥", hand_str)
        self.assertIn("6♠", hand_str)
        self.assertIn("soft 17", hand_str)
    
    def test_edge_cases(self):
        """Test edge cases and boundary conditions."""
        # Four Aces
        hand = Hand()
        for suit in [Suit.HEARTS, Suit.SPADES, Suit.CLUBS, Suit.DIAMONDS]:
            hand.add_card(Card(suit, Rank.ACE))
        
        # Should be 14 (one Ace as 11, three as 1)
        self.assertEqual(hand.get_value(), 14)
        self.assertTrue(hand.is_soft())
        
        # Ace + Ten cards totaling exactly 21
        hand2 = Hand()
        hand2.add_card(Card(Suit.HEARTS, Rank.ACE))
        hand2.add_card(Card(Suit.SPADES, Rank.FIVE))
        hand2.add_card(Card(Suit.CLUBS, Rank.FIVE))
        
        self.assertEqual(hand2.get_value(), 21)
        self.assertTrue(hand2.is_soft())


if __name__ == '__main__':
    unittest.main()
