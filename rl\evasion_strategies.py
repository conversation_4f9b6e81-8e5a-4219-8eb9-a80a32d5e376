"""
Evasion Strategies for BlackJack Bot ML.

This module implements advanced evasion techniques that integrate with the
persona system to avoid detection while learning optimal strategies.
"""

import random
import time
import numpy as np
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Tuple, Union
from enum import Enum

from .base_rl_agent import BaseRLAgent
from personas.base_persona import Base<PERSON>ersona, DecisionContext
from personas.persona_switcher import <PERSON>a<PERSON>witcher, SwitchConfig
from core.game_logic import GameAction, GameState


class EvasionTechnique(Enum):
    """Types of evasion techniques."""
    PERSONA_SWITCHING = "persona_switching"
    BEHAVIORAL_NOISE = "behavioral_noise"
    TIMING_VARIATION = "timing_variation"
    DECISION_MASKING = "decision_masking"
    PATTERN_DISRUPTION = "pattern_disruption"
    ADAPTIVE_CONSISTENCY = "adaptive_consistency"


@dataclass
class EvasionConfig:
    """Configuration for evasion strategies."""
    
    # Detection thresholds
    consistency_threshold: float = 0.95
    pattern_detection_window: int = 100
    risk_assessment_frequency: int = 50
    
    # Evasion technique weights
    technique_weights: Dict[EvasionTechnique, float] = field(default_factory=lambda: {
        EvasionTechnique.PERSONA_SWITCHING: 0.3,
        EvasionTechnique.BEHAVIORAL_NOISE: 0.2,
        EvasionTechnique.TIMING_VARIATION: 0.15,
        EvasionTechnique.DECISION_MASKING: 0.15,
        EvasionTechnique.PATTERN_DISRUPTION: 0.1,
        EvasionTechnique.ADAPTIVE_CONSISTENCY: 0.1
    })
    
    # Behavioral noise parameters
    noise_intensity: float = 0.1
    noise_decay: float = 0.99
    min_noise: float = 0.01
    max_noise: float = 0.3
    
    # Timing variation parameters
    timing_noise_factor: float = 0.2
    min_timing_multiplier: float = 0.5
    max_timing_multiplier: float = 2.0
    
    # Pattern disruption parameters
    disruption_probability: float = 0.05
    disruption_intensity: float = 0.3
    
    # Adaptive consistency parameters
    target_consistency_range: Tuple[float, float] = (0.7, 0.85)
    consistency_adjustment_rate: float = 0.1


class BaseEvasionStrategy(ABC):
    """Abstract base class for evasion strategies."""
    
    def __init__(self, config: EvasionConfig):
        """Initialize evasion strategy."""
        self.config = config
        self.activation_count = 0
        self.last_activation = 0.0
        
    @abstractmethod
    def should_activate(self, agent: BaseRLAgent, detection_risk: float) -> bool:
        """Determine if this evasion strategy should be activated."""
        pass
    
    @abstractmethod
    def apply_evasion(self, agent: BaseRLAgent, game_state: GameState, 
                     rl_action: GameAction) -> Tuple[GameAction, Dict[str, Any]]:
        """Apply evasion technique and return modified action and metadata."""
        pass
    
    def get_activation_stats(self) -> Dict[str, Any]:
        """Get statistics about strategy activation."""
        return {
            "activation_count": self.activation_count,
            "last_activation": self.last_activation,
            "strategy_type": self.__class__.__name__
        }


class PersonaSwitchingStrategy(BaseEvasionStrategy):
    """Evasion strategy using dynamic persona switching."""
    
    def __init__(self, config: EvasionConfig, persona_switcher: PersonaSwitcher):
        """Initialize persona switching strategy."""
        super().__init__(config)
        self.persona_switcher = persona_switcher
        self.switch_cooldown = 50  # Minimum hands between switches
        
    def should_activate(self, agent: BaseRLAgent, detection_risk: float) -> bool:
        """Activate when detection risk is high and cooldown has passed."""
        hands_since_switch = agent.training_step - self.last_activation
        
        return (detection_risk > 0.8 and 
                hands_since_switch >= self.switch_cooldown)
    
    def apply_evasion(self, agent: BaseRLAgent, game_state: GameState, 
                     rl_action: GameAction) -> Tuple[GameAction, Dict[str, Any]]:
        """Apply persona switching evasion."""
        # Force persona switch
        old_persona = self.persona_switcher.get_current_persona_name()
        
        # Switch to different persona
        from personas.persona_switcher import SwitchTrigger
        self.persona_switcher._switch_persona(SwitchTrigger.DETECTION_AVOIDANCE)
        
        new_persona = self.persona_switcher.get_current_persona_name()
        
        # Get action from new persona
        persona_action = self.persona_switcher.get_action(game_state, 0)
        
        self.activation_count += 1
        self.last_activation = agent.training_step
        
        metadata = {
            "technique": EvasionTechnique.PERSONA_SWITCHING,
            "old_persona": old_persona,
            "new_persona": new_persona,
            "rl_action": rl_action.value,
            "final_action": persona_action.value
        }
        
        return persona_action, metadata


class BehavioralNoiseStrategy(BaseEvasionStrategy):
    """Evasion strategy using behavioral noise injection."""
    
    def __init__(self, config: EvasionConfig):
        """Initialize behavioral noise strategy."""
        super().__init__(config)
        self.current_noise_level = config.noise_intensity
        
    def should_activate(self, agent: BaseRLAgent, detection_risk: float) -> bool:
        """Activate based on detection risk and random chance."""
        base_probability = detection_risk * 0.2  # Scale with risk
        noise_probability = self.current_noise_level
        
        return random.random() < (base_probability + noise_probability)
    
    def apply_evasion(self, agent: BaseRLAgent, game_state: GameState, 
                     rl_action: GameAction) -> Tuple[GameAction, Dict[str, Any]]:
        """Apply behavioral noise to decision."""
        available_actions = self._get_available_actions(game_state)
        
        # Apply noise with current noise level
        if random.random() < self.current_noise_level:
            # Random action selection
            noisy_action = random.choice(available_actions)
        else:
            # Slight bias toward different action
            if len(available_actions) > 1:
                other_actions = [a for a in available_actions if a != rl_action]
                if other_actions and random.random() < 0.3:
                    noisy_action = random.choice(other_actions)
                else:
                    noisy_action = rl_action
            else:
                noisy_action = rl_action
        
        # Decay noise level
        self.current_noise_level *= self.config.noise_decay
        self.current_noise_level = max(self.config.min_noise, self.current_noise_level)
        
        self.activation_count += 1
        self.last_activation = agent.training_step
        
        metadata = {
            "technique": EvasionTechnique.BEHAVIORAL_NOISE,
            "noise_level": self.current_noise_level,
            "rl_action": rl_action.value,
            "final_action": noisy_action.value,
            "was_modified": rl_action != noisy_action
        }
        
        return noisy_action, metadata
    
    def _get_available_actions(self, game_state: GameState) -> List[GameAction]:
        """Get available actions for current game state."""
        # Simplified - in practice would check game rules
        return [GameAction.HIT, GameAction.STAND, GameAction.DOUBLE, GameAction.SPLIT]


class TimingVariationStrategy(BaseEvasionStrategy):
    """Evasion strategy using decision timing variation."""
    
    def __init__(self, config: EvasionConfig):
        """Initialize timing variation strategy."""
        super().__init__(config)
        self.timing_history = []
        
    def should_activate(self, agent: BaseRLAgent, detection_risk: float) -> bool:
        """Always activate to provide timing variation."""
        return True
    
    def apply_evasion(self, agent: BaseRLAgent, game_state: GameState, 
                     rl_action: GameAction) -> Tuple[GameAction, Dict[str, Any]]:
        """Apply timing variation (doesn't change action, just records timing)."""
        # Calculate varied timing
        base_timing = 1.0  # Base decision time
        
        # Add noise based on configuration
        noise_factor = random.uniform(-self.config.timing_noise_factor, 
                                    self.config.timing_noise_factor)
        timing_multiplier = 1.0 + noise_factor
        
        # Clamp to reasonable bounds
        timing_multiplier = max(self.config.min_timing_multiplier,
                              min(self.config.max_timing_multiplier, timing_multiplier))
        
        varied_timing = base_timing * timing_multiplier
        
        # Record timing
        self.timing_history.append(varied_timing)
        if len(self.timing_history) > 100:
            self.timing_history.pop(0)
        
        self.activation_count += 1
        self.last_activation = agent.training_step
        
        metadata = {
            "technique": EvasionTechnique.TIMING_VARIATION,
            "base_timing": base_timing,
            "timing_multiplier": timing_multiplier,
            "final_timing": varied_timing,
            "avg_timing": sum(self.timing_history) / len(self.timing_history)
        }
        
        return rl_action, metadata


class DecisionMaskingStrategy(BaseEvasionStrategy):
    """Evasion strategy using decision masking with persona alignment."""
    
    def __init__(self, config: EvasionConfig):
        """Initialize decision masking strategy."""
        super().__init__(config)
        self.masking_probability = 0.1
        
    def should_activate(self, agent: BaseRLAgent, detection_risk: float) -> bool:
        """Activate based on detection risk."""
        return detection_risk > 0.7 and random.random() < self.masking_probability
    
    def apply_evasion(self, agent: BaseRLAgent, game_state: GameState, 
                     rl_action: GameAction) -> Tuple[GameAction, Dict[str, Any]]:
        """Apply decision masking using persona behavior."""
        masked_action = rl_action
        
        # If agent has persona, use persona decision for masking
        if hasattr(agent, 'persona') and agent.persona:
            try:
                persona_action = agent.persona.get_action(game_state, 0)
                
                # Use persona action with some probability
                if random.random() < 0.7:
                    masked_action = persona_action
                    
            except Exception:
                # Fallback to original action if persona fails
                pass
        
        self.activation_count += 1
        self.last_activation = agent.training_step
        
        metadata = {
            "technique": EvasionTechnique.DECISION_MASKING,
            "rl_action": rl_action.value,
            "final_action": masked_action.value,
            "was_masked": rl_action != masked_action,
            "has_persona": hasattr(agent, 'persona') and agent.persona is not None
        }
        
        return masked_action, metadata


class PatternDisruptionStrategy(BaseEvasionStrategy):
    """Evasion strategy using pattern disruption."""
    
    def __init__(self, config: EvasionConfig):
        """Initialize pattern disruption strategy."""
        super().__init__(config)
        self.disruption_counter = 0
        
    def should_activate(self, agent: BaseRLAgent, detection_risk: float) -> bool:
        """Activate randomly to disrupt patterns."""
        return random.random() < self.config.disruption_probability
    
    def apply_evasion(self, agent: BaseRLAgent, game_state: GameState, 
                     rl_action: GameAction) -> Tuple[GameAction, Dict[str, Any]]:
        """Apply pattern disruption."""
        available_actions = self._get_available_actions(game_state)
        
        # Disrupt pattern by choosing different action
        if len(available_actions) > 1:
            other_actions = [a for a in available_actions if a != rl_action]
            if other_actions:
                disrupted_action = random.choice(other_actions)
            else:
                disrupted_action = rl_action
        else:
            disrupted_action = rl_action
        
        self.disruption_counter += 1
        self.activation_count += 1
        self.last_activation = agent.training_step
        
        metadata = {
            "technique": EvasionTechnique.PATTERN_DISRUPTION,
            "disruption_count": self.disruption_counter,
            "rl_action": rl_action.value,
            "final_action": disrupted_action.value,
            "was_disrupted": rl_action != disrupted_action
        }
        
        return disrupted_action, metadata
    
    def _get_available_actions(self, game_state: GameState) -> List[GameAction]:
        """Get available actions for current game state."""
        return [GameAction.HIT, GameAction.STAND, GameAction.DOUBLE, GameAction.SPLIT]


class AdaptiveConsistencyStrategy(BaseEvasionStrategy):
    """Evasion strategy using adaptive consistency management."""
    
    def __init__(self, config: EvasionConfig):
        """Initialize adaptive consistency strategy."""
        super().__init__(config)
        self.target_consistency = random.uniform(*config.target_consistency_range)
        self.consistency_history = []
        
    def should_activate(self, agent: BaseRLAgent, detection_risk: float) -> bool:
        """Activate when consistency is outside target range."""
        if len(agent.consistency_scores) == 0:
            return False
        
        current_consistency = agent.consistency_scores[-1]
        return abs(current_consistency - self.target_consistency) > 0.1
    
    def apply_evasion(self, agent: BaseRLAgent, game_state: GameState, 
                     rl_action: GameAction) -> Tuple[GameAction, Dict[str, Any]]:
        """Apply adaptive consistency adjustment."""
        if len(agent.consistency_scores) == 0:
            return rl_action, {"technique": EvasionTechnique.ADAPTIVE_CONSISTENCY}
        
        current_consistency = agent.consistency_scores[-1]
        available_actions = self._get_available_actions(game_state)
        
        adjusted_action = rl_action
        
        # If consistency is too high, inject some randomness
        if current_consistency > self.target_consistency:
            if len(available_actions) > 1 and random.random() < 0.3:
                other_actions = [a for a in available_actions if a != rl_action]
                if other_actions:
                    adjusted_action = random.choice(other_actions)
        
        # If consistency is too low, be more consistent
        elif current_consistency < self.target_consistency:
            # Stick with RL action (more consistent)
            pass
        
        # Occasionally adjust target consistency
        if random.random() < 0.01:
            self.target_consistency = random.uniform(*self.config.target_consistency_range)
        
        self.activation_count += 1
        self.last_activation = agent.training_step
        
        metadata = {
            "technique": EvasionTechnique.ADAPTIVE_CONSISTENCY,
            "current_consistency": current_consistency,
            "target_consistency": self.target_consistency,
            "rl_action": rl_action.value,
            "final_action": adjusted_action.value,
            "was_adjusted": rl_action != adjusted_action
        }
        
        return adjusted_action, metadata
    
    def _get_available_actions(self, game_state: GameState) -> List[GameAction]:
        """Get available actions for current game state."""
        return [GameAction.HIT, GameAction.STAND, GameAction.DOUBLE, GameAction.SPLIT]
