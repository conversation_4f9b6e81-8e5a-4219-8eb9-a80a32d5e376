"""
Deep Q-Network (DQN) Agent for BlackJack Bot ML.

This module implements a DQN agent that can learn optimal blackjack strategies
while maintaining human-like behavior patterns through persona integration.
"""

import random
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from dataclasses import dataclass
from typing import List, Optional, Tuple
import os

from .base_rl_agent import BaseRLAgent, RLConfig, Experience
from .rl_environment import RLState
from core.game_logic import GameAction, GameState
from personas.base_persona import BasePersona


@dataclass
class DQNConfig(RLConfig):
    """Configuration specific to DQN agent."""
    
    # Network architecture
    hidden_layers: List[int] = None
    dropout_rate: float = 0.1
    activation: str = "relu"
    
    # DQN specific parameters
    double_dqn: bool = True
    dueling_dqn: bool = True
    prioritized_replay: bool = False
    
    # Training parameters
    gradient_clipping: float = 1.0
    weight_decay: float = 1e-4
    
    def __post_init__(self):
        """Initialize default values."""
        if self.hidden_layers is None:
            self.hidden_layers = [128, 64, 32]


class DQNNetwork(nn.Module):
    """Deep Q-Network for blackjack strategy learning."""
    
    def __init__(self, input_size: int, output_size: int, config: DQNConfig):
        """
        Initialize DQN network.
        
        Args:
            input_size: Size of input state vector
            output_size: Number of possible actions
            config: DQN configuration
        """
        super(DQNNetwork, self).__init__()
        self.config = config
        self.input_size = input_size
        self.output_size = output_size
        
        # Build network layers
        layers = []
        prev_size = input_size
        
        for hidden_size in config.hidden_layers:
            layers.append(nn.Linear(prev_size, hidden_size))
            layers.append(self._get_activation())
            layers.append(nn.Dropout(config.dropout_rate))
            prev_size = hidden_size
        
        self.feature_layers = nn.Sequential(*layers)
        
        if config.dueling_dqn:
            # Dueling DQN architecture
            self.value_stream = nn.Linear(prev_size, 1)
            self.advantage_stream = nn.Linear(prev_size, output_size)
        else:
            # Standard DQN
            self.output_layer = nn.Linear(prev_size, output_size)
    
    def _get_activation(self) -> nn.Module:
        """Get activation function."""
        if self.config.activation.lower() == "relu":
            return nn.ReLU()
        elif self.config.activation.lower() == "tanh":
            return nn.Tanh()
        elif self.config.activation.lower() == "leaky_relu":
            return nn.LeakyReLU()
        else:
            return nn.ReLU()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through network."""
        features = self.feature_layers(x)
        
        if self.config.dueling_dqn:
            # Dueling DQN: Q(s,a) = V(s) + A(s,a) - mean(A(s,a))
            value = self.value_stream(features)
            advantage = self.advantage_stream(features)
            
            # Subtract mean advantage for identifiability
            q_values = value + advantage - advantage.mean(dim=1, keepdim=True)
            return q_values
        else:
            # Standard DQN
            return self.output_layer(features)


class DQNAgent(BaseRLAgent):
    """
    Deep Q-Network agent for blackjack with persona integration.
    
    Implements DQN with optional enhancements like Double DQN and Dueling DQN,
    while maintaining human-like behavior through persona integration.
    """
    
    def __init__(self, name: str, config: DQNConfig, persona: Optional[BasePersona] = None):
        """
        Initialize DQN agent.
        
        Args:
            name: Agent name
            config: DQN configuration
            persona: Optional persona for human-like behavior
        """
        super().__init__(name, config, persona)
        self.config = config
        
        # Network setup
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.state_size = 18  # From RLState.feature_size
        self.action_size = 4  # HIT, STAND, DOUBLE, SPLIT
        
        # Create networks
        self.q_network = DQNNetwork(self.state_size, self.action_size, config).to(self.device)
        self.target_network = DQNNetwork(self.state_size, self.action_size, config).to(self.device)
        
        # Initialize target network with same weights
        self.target_network.load_state_dict(self.q_network.state_dict())
        
        # Optimizer
        self.optimizer = optim.Adam(
            self.q_network.parameters(),
            lr=config.learning_rate,
            weight_decay=config.weight_decay
        )
        
        # Training state
        self.update_count = 0
    
    def _get_state_representation(self, game_state: GameState, hand_index: int = 0) -> np.ndarray:
        """
        Convert game state to neural network input.
        
        Args:
            game_state: Current game state
            hand_index: Index of hand to process
            
        Returns:
            State representation as numpy array
        """
        # Create RLState and convert to array
        rl_state = self._create_rl_state(game_state, hand_index)
        return rl_state.to_array()
    
    def _create_rl_state(self, game_state: GameState, hand_index: int) -> RLState:
        """Create RLState from game state."""
        if not game_state.player_hands or hand_index >= len(game_state.player_hands):
            # Default state
            return RLState(
                player_value=0,
                player_soft=False,
                player_pairs=False,
                player_can_double=False,
                player_can_split=False,
                dealer_upcard=0,
                dealer_soft=False,
                deck_penetration=0.0,
                bet_amount=10.0
            )
        
        player_hand = game_state.player_hands[hand_index]
        
        # Get dealer information
        dealer_upcard = 0
        dealer_soft = False
        if game_state.dealer_hand.cards:
            dealer_upcard = game_state.dealer_hand.cards[0].get_value()
            if game_state.dealer_hand.cards[0].is_ace:
                dealer_upcard = 11
            dealer_soft = game_state.dealer_hand.is_soft()
        
        # Get persona context
        persona_context = None
        decision_context = None
        if self.persona:
            persona_context = self.persona.config.name.lower().split()[0]
            decision_context = self.persona.current_context.value
        
        # Calculate consistency score
        consistency_score = self._calculate_consistency() if len(self.decision_history) >= 10 else 0.0
        
        return RLState(
            player_value=player_hand.get_value(),
            player_soft=player_hand.is_soft(),
            player_pairs=player_hand.is_pair(),
            player_can_double=hand_index < len(game_state.can_double) and game_state.can_double[hand_index],
            player_can_split=hand_index < len(game_state.can_split) and game_state.can_split[hand_index],
            dealer_upcard=dealer_upcard,
            dealer_soft=dealer_soft,
            deck_penetration=0.5,  # Placeholder
            bet_amount=game_state.bet_amount,
            persona_context=persona_context,
            decision_context=decision_context,
            consistency_score=consistency_score,
            hands_since_switch=self.last_persona_switch
        )
    
    def _select_action(self, state: np.ndarray, available_actions: List[GameAction], 
                      training: bool = True) -> GameAction:
        """
        Select action using epsilon-greedy policy with DQN.
        
        Args:
            state: Current state representation
            available_actions: Available actions
            training: Whether in training mode
            
        Returns:
            Selected action
        """
        # Convert state to tensor
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        
        # Epsilon-greedy action selection
        if training and random.random() < self.epsilon:
            # Random action
            action_index = random.randint(0, self.action_size - 1)
        else:
            # Greedy action
            with torch.no_grad():
                q_values = self.q_network(state_tensor)
                action_index = q_values.argmax().item()
        
        # Convert index to action
        action = self._index_to_action(action_index)
        
        # Ensure action is available
        if action not in available_actions:
            # Fallback to random available action
            action = random.choice(available_actions)
        
        return action
    
    def _update_model(self, batch: List[Experience]) -> float:
        """
        Update DQN model with batch of experiences.
        
        Args:
            batch: Batch of experiences
            
        Returns:
            Training loss
        """
        if len(batch) == 0:
            return 0.0
        
        # Convert batch to tensors
        states = torch.FloatTensor([exp.state for exp in batch]).to(self.device)
        actions = torch.LongTensor([exp.action for exp in batch]).to(self.device)
        rewards = torch.FloatTensor([exp.reward for exp in batch]).to(self.device)
        next_states = torch.FloatTensor([exp.next_state for exp in batch]).to(self.device)
        dones = torch.BoolTensor([exp.done for exp in batch]).to(self.device)
        
        # Current Q values
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        
        # Next Q values
        with torch.no_grad():
            if self.config.double_dqn:
                # Double DQN: use main network to select actions, target network to evaluate
                next_actions = self.q_network(next_states).argmax(1, keepdim=True)
                next_q_values = self.target_network(next_states).gather(1, next_actions)
            else:
                # Standard DQN
                next_q_values = self.target_network(next_states).max(1)[0].unsqueeze(1)
            
            # Target Q values
            target_q_values = rewards.unsqueeze(1) + (self.config.discount_factor * next_q_values * ~dones.unsqueeze(1))
        
        # Compute loss
        loss = F.mse_loss(current_q_values, target_q_values)
        
        # Optimize
        self.optimizer.zero_grad()
        loss.backward()
        
        # Gradient clipping
        if self.config.gradient_clipping > 0:
            torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), self.config.gradient_clipping)
        
        self.optimizer.step()
        
        # Update target network
        self.update_count += 1
        if self.update_count % self.config.target_update_frequency == 0:
            self.target_network.load_state_dict(self.q_network.state_dict())
        
        return loss.item()
    
    def get_q_values(self, state: np.ndarray) -> np.ndarray:
        """Get Q-values for a state."""
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        with torch.no_grad():
            q_values = self.q_network(state_tensor)
        return q_values.cpu().numpy().flatten()
    
    def save_model(self, filepath: str) -> None:
        """Save model to file."""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        checkpoint = {
            'q_network_state_dict': self.q_network.state_dict(),
            'target_network_state_dict': self.target_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'config': self.config,
            'training_metrics': self.training_metrics,
            'episode': self.episode,
            'training_step': self.training_step,
            'epsilon': self.epsilon
        }
        
        torch.save(checkpoint, filepath)
    
    def load_model(self, filepath: str) -> None:
        """Load model from file."""
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Model file not found: {filepath}")
        
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
        self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        # Restore training state
        self.training_metrics = checkpoint.get('training_metrics', self.training_metrics)
        self.episode = checkpoint.get('episode', 0)
        self.training_step = checkpoint.get('training_step', 0)
        self.epsilon = checkpoint.get('epsilon', self.config.epsilon_start)
    
    def set_training_mode(self, training: bool) -> None:
        """Set training mode."""
        if training:
            self.q_network.train()
        else:
            self.q_network.eval()
    
    def get_network_info(self) -> dict:
        """Get information about the neural network."""
        total_params = sum(p.numel() for p in self.q_network.parameters())
        trainable_params = sum(p.numel() for p in self.q_network.parameters() if p.requires_grad)

        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'network_architecture': str(self.q_network),
            'device': str(self.device),
            'double_dqn': self.config.double_dqn,
            'dueling_dqn': self.config.dueling_dqn
        }

    def state_dict(self) -> dict:
        """Get agent state dictionary for saving."""
        return {
            'q_network_state_dict': self.q_network.state_dict(),
            'target_network_state_dict': self.target_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'config': self.config,
            'training_metrics': self.training_metrics,
            'episode': self.episode,
            'training_step': self.training_step,
            'epsilon': self.epsilon,
            'update_count': self.update_count
        }

    def load_state_dict(self, state_dict: dict) -> None:
        """Load agent state from dictionary."""
        self.q_network.load_state_dict(state_dict['q_network_state_dict'])
        self.target_network.load_state_dict(state_dict['target_network_state_dict'])
        self.optimizer.load_state_dict(state_dict['optimizer_state_dict'])

        # Restore training state
        self.training_metrics = state_dict.get('training_metrics', self.training_metrics)
        self.episode = state_dict.get('episode', 0)
        self.training_step = state_dict.get('training_step', 0)
        self.epsilon = state_dict.get('epsilon', self.config.epsilon_start)
        self.update_count = state_dict.get('update_count', 0)
