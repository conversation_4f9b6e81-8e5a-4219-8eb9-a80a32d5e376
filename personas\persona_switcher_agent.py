"""
Persona Switcher Agent for BlackJack Bot ML.

This module provides an agent that uses the PersonaSwitcher to dynamically
change between different personas for detection avoidance.
"""

import time
from typing import Dict, Any, Optional

from agents.base_agent import BaseAgent
from core.game_logic import GameAction, GameState
from .persona_switcher import <PERSON><PERSON><PERSON><PERSON><PERSON>, SwitchConfig


class PersonaSwitcherAgent(BaseAgent):
    """
    Agent that uses dynamic persona switching for detection avoidance.
    
    This agent wraps a PersonaSwitcher and provides the standard agent
    interface while managing multiple personas internally.
    """
    
    def __init__(self, name: str, switch_config: SwitchConfig = None, bet_amount: float = 1.0):
        """
        Initialize the persona switcher agent.
        
        Args:
            name: Agent name
            switch_config: Configuration for persona switching
            bet_amount: Base bet amount
        """
        super().__init__(name)
        self.switcher = PersonaSwitcher(switch_config)
        self.bet_amount = bet_amount
        
        # Track switcher-specific statistics
        self.switcher_stats = {
            'total_decision_time': 0.0,
            'decision_count': 0,
            'switches_observed': 0,
            'last_switch_count': 0
        }
    
    def get_action(self, game_state: GameState, hand_index: int = 0) -> GameAction:
        """
        Get action using current persona from switcher.
        
        Args:
            game_state: Current game state
            hand_index: Index of hand to decide for
            
        Returns:
            Action selected by current persona
        """
        # Record decision timing
        start_time = time.time()
        
        # Track switches
        switches_before = len(self.switcher.get_switch_history())
        
        # Get action from switcher (which may trigger persona switch)
        action = self.switcher.get_action(game_state, hand_index)
        
        # Record timing statistics
        decision_time = time.time() - start_time
        self.switcher_stats['total_decision_time'] += decision_time
        self.switcher_stats['decision_count'] += 1
        
        # Track switches
        switches_after = len(self.switcher.get_switch_history())
        if switches_after > switches_before:
            self.switcher_stats['switches_observed'] += 1
        self.switcher_stats['last_switch_count'] = switches_after
        
        return action
    
    def get_bet_amount(self, min_bet: float = 1.0, max_bet: float = 100.0) -> float:
        """
        Get bet amount (currently flat betting, could be enhanced with persona-based betting).
        
        Args:
            min_bet: Minimum allowed bet
            max_bet: Maximum allowed bet
            
        Returns:
            Bet amount
        """
        return max(min_bet, min(self.bet_amount, max_bet))
    
    def set_bet_amount(self, amount: float) -> None:
        """Set the base bet amount."""
        self.bet_amount = amount
    
    def update_stats(self, game_result: str, bet_amount: float, winnings: float) -> None:
        """
        Update both agent and switcher statistics.
        
        Args:
            game_result: Result of the hand
            bet_amount: Amount bet
            winnings: Amount won/lost
        """
        # Update base agent stats
        super().update_stats(game_result, bet_amount, winnings)
        
        # Update switcher with result
        self.switcher.update_result(game_result)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive statistics including switcher behavior.
        
        Returns:
            Combined agent and switcher statistics
        """
        # Get base agent stats
        base_stats = super().get_stats()
        
        # Get current persona behavioral stats
        current_persona = self.switcher.get_current_persona()
        behavioral_stats = current_persona.get_behavioral_stats()
        
        # Get switcher statistics
        switching_stats = self.switcher.get_switching_stats()
        
        # Calculate switcher-specific metrics
        switcher_metrics = self.switcher_stats.copy()
        if switcher_metrics['decision_count'] > 0:
            switcher_metrics['avg_decision_time'] = (
                switcher_metrics['total_decision_time'] / switcher_metrics['decision_count']
            )
        else:
            switcher_metrics['avg_decision_time'] = 0.0
        
        # Combine all statistics
        combined_stats = {
            **base_stats,
            'current_persona': self.switcher.get_current_persona_name(),
            'current_persona_config': {
                'name': current_persona.config.name,
                'description': current_persona.config.description,
                'base_accuracy': current_persona.config.decision_pattern.base_accuracy,
                'decision_speed': current_persona.config.decision_pattern.decision_speed,
                'aggression_bias': current_persona.config.decision_pattern.aggression_bias
            },
            'switcher_metrics': switcher_metrics,
            'switching_stats': switching_stats,
            'behavioral_stats': behavioral_stats,
            'current_context': current_persona.current_context.value,
            'current_accuracy': current_persona.current_accuracy,
            'hands_played_persona': current_persona.hands_played,
            'consecutive_wins': current_persona.consecutive_wins,
            'consecutive_losses': current_persona.consecutive_losses
        }
        
        return combined_stats
    
    def get_decision_time(self) -> float:
        """
        Get the last decision time from the current persona.
        
        Returns:
            Last decision time in seconds
        """
        return self.switcher.get_current_persona().last_decision_time
    
    def get_strategy_accuracy(self, game_state: GameState = None, hand_index: int = 0) -> float:
        """
        Get current strategy accuracy from the current persona.
        
        Args:
            game_state: Current game state (unused)
            hand_index: Hand index (unused)
            
        Returns:
            Current effective accuracy of active persona
        """
        return self.switcher.get_current_persona()._get_effective_accuracy()
    
    def reset_stats(self) -> None:
        """Reset both agent and switcher statistics."""
        super().reset_stats()
        self.switcher.reset_session()
        
        # Reset switcher-specific stats
        self.switcher_stats = {
            'total_decision_time': 0.0,
            'decision_count': 0,
            'switches_observed': 0,
            'last_switch_count': 0
        }
    
    def get_current_persona_name(self) -> str:
        """Get the name of the currently active persona."""
        return self.switcher.get_current_persona_name()
    
    def get_current_persona(self):
        """Get the currently active persona object."""
        return self.switcher.get_current_persona()
    
    def get_switch_history(self) -> list:
        """Get the history of persona switches."""
        return self.switcher.get_switch_history()
    
    def get_switching_config(self) -> SwitchConfig:
        """Get the switching configuration."""
        return self.switcher.config
    
    def force_persona_switch(self, target_persona: str = None) -> bool:
        """
        Force a persona switch (for testing or manual control).
        
        Args:
            target_persona: Specific persona to switch to, or None for random
            
        Returns:
            True if switch was successful, False otherwise
        """
        if target_persona and target_persona not in self.switcher.personas:
            return False
        
        from .persona_switcher import SwitchTrigger
        
        if target_persona:
            # Temporarily modify switcher to force specific persona
            old_persona = self.switcher.current_persona_name
            if old_persona != target_persona:
                self.switcher.current_persona_name = target_persona
                self.switcher.current_persona = self.switcher.personas[target_persona]
                self.switcher._switch_persona(SwitchTrigger.RANDOM)  # Record the switch
                return True
        else:
            # Force random switch
            self.switcher._switch_persona(SwitchTrigger.RANDOM)
            return True
        
        return False
    
    def get_detection_risk_assessment(self) -> Dict[str, Any]:
        """
        Assess current detection risk based on behavior patterns.
        
        Returns:
            Risk assessment metrics
        """
        switching_stats = self.switcher.get_switching_stats()
        
        # Calculate risk factors
        consistency_risk = switching_stats.get('recent_consistency', 0.0)
        switch_frequency = len(self.switcher.get_switch_history()) / max(1, self.stats['hands_played'])
        
        # Assess overall risk
        risk_level = "LOW"
        if consistency_risk > 0.9:
            risk_level = "HIGH"
        elif consistency_risk > 0.8:
            risk_level = "MEDIUM"
        
        if switch_frequency < 0.01:  # Less than 1% switch rate
            risk_level = "HIGH"  # Too predictable
        
        return {
            "overall_risk": risk_level,
            "consistency_risk": consistency_risk,
            "switch_frequency": switch_frequency,
            "hands_since_last_switch": switching_stats.get('hands_with_current', 0),
            "current_persona": self.switcher.get_current_persona_name(),
            "recommendations": self._get_risk_recommendations(risk_level, consistency_risk, switch_frequency)
        }
    
    def _get_risk_recommendations(self, risk_level: str, consistency_risk: float, 
                                switch_frequency: float) -> list:
        """Get recommendations for reducing detection risk."""
        recommendations = []
        
        if risk_level == "HIGH":
            if consistency_risk > 0.9:
                recommendations.append("Increase decision variability")
                recommendations.append("Consider switching to more volatile persona")
            
            if switch_frequency < 0.01:
                recommendations.append("Increase persona switching frequency")
                recommendations.append("Reduce minimum time/hands between switches")
        
        elif risk_level == "MEDIUM":
            recommendations.append("Monitor behavior patterns closely")
            recommendations.append("Consider occasional random switches")
        
        else:  # LOW risk
            recommendations.append("Current behavior appears natural")
            recommendations.append("Continue current switching pattern")
        
        return recommendations
    
    def export_switching_data(self) -> Dict[str, Any]:
        """
        Export comprehensive switching behavior data for analysis.
        
        Returns:
            Complete switching dataset
        """
        return {
            'agent_stats': self.get_stats(),
            'switch_history': self.get_switch_history(),
            'switching_config': {
                'min_switch_time': self.switcher.config.min_switch_time,
                'max_switch_time': self.switcher.config.max_switch_time,
                'min_hands_per_persona': self.switcher.config.min_hands_per_persona,
                'max_hands_per_persona': self.switcher.config.max_hands_per_persona,
                'persona_weights': self.switcher.config.persona_weights
            },
            'detection_risk': self.get_detection_risk_assessment(),
            'decision_patterns': self.switcher.decision_patterns.copy(),
            'persona_configs': {
                name: {
                    'name': persona.config.name,
                    'base_accuracy': persona.config.decision_pattern.base_accuracy,
                    'decision_speed': persona.config.decision_pattern.decision_speed,
                    'aggression_bias': persona.config.decision_pattern.aggression_bias
                }
                for name, persona in self.switcher.personas.items()
            }
        }
    
    def __str__(self) -> str:
        """String representation of the agent."""
        current_persona = self.switcher.get_current_persona_name()
        return f"PersonaSwitcherAgent(name='{self.name}', current_persona='{current_persona}')"
    
    def __repr__(self) -> str:
        """Detailed string representation for debugging."""
        stats = self.get_stats()
        switching_stats = stats.get('switching_stats', {})
        return (f"PersonaSwitcherAgent(name='{self.name}', "
                f"current_persona='{stats.get('current_persona', 'unknown')}', "
                f"hands_played={stats.get('hands_played', 0)}, "
                f"total_switches={switching_stats.get('total_switches', 0)}, "
                f"accuracy={stats.get('behavioral_stats', {}).get('accuracy', 0.0):.3f})")
