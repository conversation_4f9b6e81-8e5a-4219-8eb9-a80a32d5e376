"""
Unit tests for simulation environment.

Tests P1_T6 implementation: Simulation environment for testing.
"""

import unittest
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from utils.simulation import BlackjackSimulator, SimulationConfig
from agents.basic_strategy_agent import BasicStrategyAgent


class TestBlackjackSimulator(unittest.TestCase):
    """Test cases for the BlackjackSimulator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = SimulationConfig(
            num_hands=100,  # Small number for fast testing
            verbose=False,
            random_seed=42  # For reproducible results
        )
        self.simulator = BlackjackSimulator(self.config)
        self.agent = BasicStrategyAgent("Test Agent")
    
    def test_simulator_initialization(self):
        """Test simulator initialization."""
        self.assertIsNotNone(self.simulator.game)
        self.assertEqual(self.simulator.config.num_hands, 100)
        self.assertEqual(self.simulator.config.random_seed, 42)
    
    def test_basic_simulation_run(self):
        """Test running a basic simulation."""
        results = self.simulator.run_simulation(self.agent)
        
        # Check basic result structure
        self.assertIn('agent_name', results)
        self.assertIn('agent_stats', results)
        self.assertIn('simulation_time', results)
        self.assertIn('hands_per_second', results)
        
        # Check agent stats
        stats = results['agent_stats']
        # Note: hands_played might be slightly more than 100 due to splits
        self.assertGreaterEqual(stats['hands_played'], 100)
        self.assertGreater(stats['hands_played'], 0)
        self.assertGreaterEqual(stats['win_rate'], 0.0)
        self.assertLessEqual(stats['win_rate'], 1.0)
    
    def test_detailed_analysis(self):
        """Test detailed analysis collection."""
        config = SimulationConfig(
            num_hands=50,
            collect_detailed_stats=True,
            random_seed=42
        )
        simulator = BlackjackSimulator(config)
        
        results = simulator.run_simulation(self.agent)
        
        # Check detailed analysis
        self.assertIn('detailed_analysis', results)
        self.assertIn('hand_results', results)
        
        analysis = results['detailed_analysis']
        self.assertIn('total_hands', analysis)
        self.assertIn('action_frequency', analysis)
        self.assertIn('result_frequency', analysis)
        self.assertIn('betting_patterns', analysis)
        
        # Check hand results
        hand_results = results['hand_results']
        self.assertEqual(len(hand_results), 50)
        
        # Check first hand result structure
        if hand_results:
            first_hand = hand_results[0]
            self.assertIsNotNone(first_hand.hand_number)
            self.assertIsNotNone(first_hand.bet_amount)
            self.assertIsNotNone(first_hand.player_hands)
            self.assertIsNotNone(first_hand.dealer_hand)
    
    def test_performance_metrics(self):
        """Test performance metrics calculation."""
        results = self.simulator.run_simulation(self.agent)
        
        # Check timing metrics
        self.assertGreater(results['simulation_time'], 0)
        self.assertGreater(results['hands_per_second'], 0)
        
        # For 100 hands, should be reasonably fast
        self.assertLess(results['simulation_time'], 10.0)  # Should complete in under 10 seconds
    
    def test_reproducible_results(self):
        """Test that results are reproducible with same seed."""
        config1 = SimulationConfig(num_hands=50, random_seed=123)
        config2 = SimulationConfig(num_hands=50, random_seed=123)
        
        sim1 = BlackjackSimulator(config1)
        sim2 = BlackjackSimulator(config2)
        
        agent1 = BasicStrategyAgent("Agent 1")
        agent2 = BasicStrategyAgent("Agent 2")
        
        results1 = sim1.run_simulation(agent1)
        results2 = sim2.run_simulation(agent2)
        
        # Should have same number of hands
        self.assertEqual(
            results1['agent_stats']['hands_played'],
            results2['agent_stats']['hands_played']
        )
        
        # Win rates should be very close (might have tiny differences due to floating point)
        win_rate_diff = abs(
            results1['agent_stats']['win_rate'] - 
            results2['agent_stats']['win_rate']
        )
        self.assertLess(win_rate_diff, 0.01)  # Within 1%
    
    def test_betting_constraints(self):
        """Test that betting constraints are respected."""
        config = SimulationConfig(
            num_hands=20,
            min_bet=5.0,
            max_bet=25.0,
            collect_detailed_stats=True
        )
        simulator = BlackjackSimulator(config)
        
        results = simulator.run_simulation(self.agent)
        
        # Check that all bets are within constraints
        hand_results = results['hand_results']
        for hand_result in hand_results:
            self.assertGreaterEqual(hand_result.bet_amount, 5.0)
            self.assertLessEqual(hand_result.bet_amount, 25.0)
    
    def test_action_frequency_analysis(self):
        """Test action frequency analysis."""
        config = SimulationConfig(
            num_hands=100,
            collect_detailed_stats=True,
            random_seed=42
        )
        simulator = BlackjackSimulator(config)
        
        results = simulator.run_simulation(self.agent)
        
        action_freq = results['detailed_analysis']['action_frequency']
        
        # Should have recorded some hits and stands at minimum
        self.assertIn('hit', action_freq)
        self.assertIn('stand', action_freq)
        
        # Total actions should be reasonable
        total_actions = sum(action_freq.values())
        self.assertGreater(total_actions, 80)  # Most hands should have at least one action
    
    def test_result_frequency_analysis(self):
        """Test result frequency analysis."""
        config = SimulationConfig(
            num_hands=100,
            collect_detailed_stats=True,
            random_seed=42
        )
        simulator = BlackjackSimulator(config)
        
        results = simulator.run_simulation(self.agent)
        
        result_freq = results['detailed_analysis']['result_frequency']
        
        # Should have various results
        total_results = sum(result_freq.values())
        self.assertGreaterEqual(total_results, 100)  # At least one result per hand
        
        # Should have some wins and losses
        has_wins = any('win' in key.lower() for key in result_freq.keys())
        has_losses = any('loss' in key.lower() or 'bust' in key.lower() for key in result_freq.keys())
        
        self.assertTrue(has_wins or has_losses)  # Should have some definitive outcomes
    
    def test_basic_strategy_performance(self):
        """Test that Basic Strategy agent performs reasonably well."""
        config = SimulationConfig(
            num_hands=1000,  # Larger sample for better statistics
            random_seed=42
        )
        simulator = BlackjackSimulator(config)
        
        results = simulator.run_simulation(self.agent)
        stats = results['agent_stats']
        
        # Basic Strategy should have reasonable performance
        # Win rate should be between 40-50% (typical for blackjack)
        self.assertGreater(stats['win_rate'], 0.35)
        self.assertLess(stats['win_rate'], 0.55)
        
        # Return on investment should be close to theoretical (-0.5% to -1%)
        # but with variance, allow wider range
        self.assertGreater(stats['return_on_investment'], -0.05)
        self.assertLess(stats['return_on_investment'], 0.05)
        
        # Should have some blackjacks
        self.assertGreater(stats['blackjack_rate'], 0.02)  # At least 2%
        self.assertLess(stats['blackjack_rate'], 0.08)     # Less than 8%
    
    def test_export_functionality(self):
        """Test result export functionality."""
        import tempfile
        import json
        
        results = self.simulator.run_simulation(self.agent)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_path = f.name
        
        try:
            # Export results
            self.simulator.export_results(temp_path, results)
            
            # Verify file was created and is valid JSON
            self.assertTrue(os.path.exists(temp_path))
            
            with open(temp_path, 'r') as f:
                loaded_results = json.load(f)
            
            # Check that key data is preserved
            self.assertEqual(loaded_results['agent_name'], results['agent_name'])
            self.assertEqual(
                loaded_results['agent_stats']['hands_played'],
                results['agent_stats']['hands_played']
            )
            
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)


if __name__ == '__main__':
    unittest.main()
