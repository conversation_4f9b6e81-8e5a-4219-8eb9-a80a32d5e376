"""
Progress management for BlackJack Bot ML.

This module provides comprehensive progress tracking and persistence
for training sessions, simulations, and model development.
"""

import os
import json
import time
import pickle
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict


@dataclass
class TrainingSession:
    """Represents a training session."""
    session_id: str
    agent_type: str
    start_time: str
    end_time: Optional[str] = None
    total_hands: int = 0
    current_hand: int = 0
    parameters: Dict[str, Any] = None
    metrics: Dict[str, float] = None
    status: str = "running"  # running, completed, paused, failed
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}
        if self.metrics is None:
            self.metrics = {}


@dataclass
class ModelCheckpoint:
    """Represents a model checkpoint."""
    checkpoint_id: str
    session_id: str
    timestamp: str
    hand_number: int
    model_path: str
    metrics: Dict[str, float]
    notes: str = ""


class ProgressManager:
    """
    Comprehensive progress management system.
    
    Handles saving and loading of:
    - Training sessions and checkpoints
    - Agent states and statistics
    - Simulation results
    - Model weights and parameters
    """
    
    def __init__(self, base_dir: str = "data"):
        """
        Initialize the progress manager.
        
        Args:
            base_dir: Base directory for all progress data
        """
        self.base_dir = base_dir
        self.sessions_dir = os.path.join(base_dir, "training_sessions")
        self.checkpoints_dir = os.path.join(base_dir, "checkpoints")
        self.models_dir = os.path.join(base_dir, "trained_models")
        self.logs_dir = os.path.join(base_dir, "logs")
        
        # Create directories
        for directory in [self.sessions_dir, self.checkpoints_dir, 
                         self.models_dir, self.logs_dir]:
            os.makedirs(directory, exist_ok=True)
        
        self.current_session: Optional[TrainingSession] = None
    
    def start_session(self, agent_type: str, parameters: Dict[str, Any] = None) -> str:
        """
        Start a new training session.
        
        Args:
            agent_type: Type of agent being trained
            parameters: Training parameters
            
        Returns:
            Session ID
        """
        session_id = f"{agent_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.current_session = TrainingSession(
            session_id=session_id,
            agent_type=agent_type,
            start_time=datetime.now().isoformat(),
            parameters=parameters or {}
        )
        
        self._save_session()
        return session_id
    
    def update_session(self, current_hand: int = None, metrics: Dict[str, float] = None,
                      status: str = None) -> None:
        """
        Update the current training session.
        
        Args:
            current_hand: Current hand number
            metrics: Updated metrics
            status: Session status
        """
        if not self.current_session:
            raise ValueError("No active session")
        
        if current_hand is not None:
            self.current_session.current_hand = current_hand
        
        if metrics:
            self.current_session.metrics.update(metrics)
        
        if status:
            self.current_session.status = status
            if status in ["completed", "failed"]:
                self.current_session.end_time = datetime.now().isoformat()
        
        self._save_session()
    
    def end_session(self, total_hands: int = None, final_metrics: Dict[str, float] = None) -> None:
        """
        End the current training session.
        
        Args:
            total_hands: Total hands played
            final_metrics: Final metrics
        """
        if not self.current_session:
            raise ValueError("No active session")
        
        if total_hands is not None:
            self.current_session.total_hands = total_hands
        
        if final_metrics:
            self.current_session.metrics.update(final_metrics)
        
        self.current_session.status = "completed"
        self.current_session.end_time = datetime.now().isoformat()
        
        self._save_session()
        self.current_session = None
    
    def save_checkpoint(self, hand_number: int, model_data: Any, 
                       metrics: Dict[str, float], notes: str = "") -> str:
        """
        Save a model checkpoint.
        
        Args:
            hand_number: Current hand number
            model_data: Model data to save (weights, state, etc.)
            metrics: Current metrics
            notes: Optional notes
            
        Returns:
            Checkpoint ID
        """
        if not self.current_session:
            raise ValueError("No active session")
        
        checkpoint_id = f"{self.current_session.session_id}_hand_{hand_number}"
        timestamp = datetime.now().isoformat()
        
        # Save model data
        model_filename = f"{checkpoint_id}.pkl"
        model_path = os.path.join(self.checkpoints_dir, model_filename)
        
        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)
        
        # Create checkpoint record
        checkpoint = ModelCheckpoint(
            checkpoint_id=checkpoint_id,
            session_id=self.current_session.session_id,
            timestamp=timestamp,
            hand_number=hand_number,
            model_path=model_path,
            metrics=metrics.copy(),
            notes=notes
        )
        
        # Save checkpoint metadata
        checkpoint_file = os.path.join(self.checkpoints_dir, f"{checkpoint_id}.json")
        with open(checkpoint_file, 'w') as f:
            json.dump(asdict(checkpoint), f, indent=2)
        
        return checkpoint_id
    
    def load_checkpoint(self, checkpoint_id: str) -> tuple[ModelCheckpoint, Any]:
        """
        Load a model checkpoint.
        
        Args:
            checkpoint_id: Checkpoint ID to load
            
        Returns:
            Tuple of (checkpoint metadata, model data)
        """
        # Load checkpoint metadata
        checkpoint_file = os.path.join(self.checkpoints_dir, f"{checkpoint_id}.json")
        if not os.path.exists(checkpoint_file):
            raise FileNotFoundError(f"Checkpoint {checkpoint_id} not found")
        
        with open(checkpoint_file, 'r') as f:
            checkpoint_data = json.load(f)
        
        checkpoint = ModelCheckpoint(**checkpoint_data)
        
        # Load model data
        with open(checkpoint.model_path, 'rb') as f:
            model_data = pickle.load(f)
        
        return checkpoint, model_data
    
    def list_sessions(self, agent_type: str = None) -> List[TrainingSession]:
        """
        List all training sessions.
        
        Args:
            agent_type: Filter by agent type (optional)
            
        Returns:
            List of training sessions
        """
        sessions = []
        
        for filename in os.listdir(self.sessions_dir):
            if filename.endswith('.json'):
                filepath = os.path.join(self.sessions_dir, filename)
                with open(filepath, 'r') as f:
                    session_data = json.load(f)
                
                session = TrainingSession(**session_data)
                
                if agent_type is None or session.agent_type == agent_type:
                    sessions.append(session)
        
        # Sort by start time
        sessions.sort(key=lambda s: s.start_time, reverse=True)
        return sessions
    
    def list_checkpoints(self, session_id: str = None) -> List[ModelCheckpoint]:
        """
        List all checkpoints.
        
        Args:
            session_id: Filter by session ID (optional)
            
        Returns:
            List of checkpoints
        """
        checkpoints = []
        
        for filename in os.listdir(self.checkpoints_dir):
            if filename.endswith('.json'):
                filepath = os.path.join(self.checkpoints_dir, filename)
                with open(filepath, 'r') as f:
                    checkpoint_data = json.load(f)
                
                checkpoint = ModelCheckpoint(**checkpoint_data)
                
                if session_id is None or checkpoint.session_id == session_id:
                    checkpoints.append(checkpoint)
        
        # Sort by hand number
        checkpoints.sort(key=lambda c: c.hand_number, reverse=True)
        return checkpoints
    
    def save_agent_state(self, agent, filename: str = None) -> str:
        """
        Save complete agent state.
        
        Args:
            agent: Agent to save
            filename: Optional filename (auto-generated if not provided)
            
        Returns:
            Path to saved file
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{agent.__class__.__name__}_{timestamp}.pkl"
        
        filepath = os.path.join(self.models_dir, filename)
        
        with open(filepath, 'wb') as f:
            pickle.dump(agent, f)
        
        return filepath
    
    def load_agent_state(self, filepath: str) -> Any:
        """
        Load complete agent state.
        
        Args:
            filepath: Path to agent file
            
        Returns:
            Loaded agent
        """
        if not os.path.isabs(filepath):
            filepath = os.path.join(self.models_dir, filepath)
        
        with open(filepath, 'rb') as f:
            agent = pickle.load(f)
        
        return agent
    
    def export_session_summary(self, session_id: str, output_path: str = None) -> str:
        """
        Export a comprehensive session summary.
        
        Args:
            session_id: Session ID to export
            output_path: Output file path (optional)
            
        Returns:
            Path to exported file
        """
        # Load session
        session_file = os.path.join(self.sessions_dir, f"{session_id}.json")
        if not os.path.exists(session_file):
            raise FileNotFoundError(f"Session {session_id} not found")
        
        with open(session_file, 'r') as f:
            session_data = json.load(f)
        
        # Get checkpoints for this session
        checkpoints = self.list_checkpoints(session_id)
        
        # Create summary
        summary = {
            "session": session_data,
            "checkpoints": [asdict(cp) for cp in checkpoints],
            "export_time": datetime.now().isoformat()
        }
        
        # Save summary
        if output_path is None:
            output_path = os.path.join(self.base_dir, f"session_summary_{session_id}.json")
        
        with open(output_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        return output_path
    
    def _save_session(self) -> None:
        """Save the current session to disk."""
        if not self.current_session:
            return
        
        session_file = os.path.join(self.sessions_dir, f"{self.current_session.session_id}.json")
        with open(session_file, 'w') as f:
            json.dump(asdict(self.current_session), f, indent=2)
    
    def cleanup_old_checkpoints(self, keep_count: int = 10) -> int:
        """
        Clean up old checkpoints, keeping only the most recent ones.
        
        Args:
            keep_count: Number of checkpoints to keep per session
            
        Returns:
            Number of checkpoints removed
        """
        sessions = {}
        checkpoints = self.list_checkpoints()
        
        # Group checkpoints by session
        for checkpoint in checkpoints:
            if checkpoint.session_id not in sessions:
                sessions[checkpoint.session_id] = []
            sessions[checkpoint.session_id].append(checkpoint)
        
        removed_count = 0
        
        # Remove old checkpoints for each session
        for session_id, session_checkpoints in sessions.items():
            if len(session_checkpoints) > keep_count:
                # Sort by hand number and keep the most recent
                session_checkpoints.sort(key=lambda c: c.hand_number, reverse=True)
                to_remove = session_checkpoints[keep_count:]
                
                for checkpoint in to_remove:
                    # Remove model file
                    if os.path.exists(checkpoint.model_path):
                        os.remove(checkpoint.model_path)
                    
                    # Remove metadata file
                    metadata_file = os.path.join(self.checkpoints_dir, f"{checkpoint.checkpoint_id}.json")
                    if os.path.exists(metadata_file):
                        os.remove(metadata_file)
                    
                    removed_count += 1
        
        return removed_count

    def save_persona_training_session(self, session_name: str, agent,
                                    training_data: Dict[str, Any]) -> str:
        """
        Save a persona training session with behavioral data.

        Args:
            session_name: Name for the training session
            agent: The persona agent used in training
            training_data: Training session data

        Returns:
            Path to saved session file
        """
        session_data = {
            'session_name': session_name,
            'timestamp': time.time(),
            'agent_type': type(agent).__name__,
            'agent_name': agent.name,
            'training_data': training_data
        }

        # Add persona-specific data if available
        if hasattr(agent, 'persona'):
            session_data['persona_config'] = {
                'name': agent.persona.config.name,
                'description': agent.persona.config.description,
                'base_accuracy': agent.persona.config.decision_pattern.base_accuracy,
                'decision_speed': agent.persona.config.decision_pattern.decision_speed,
                'aggression_bias': agent.persona.config.decision_pattern.aggression_bias
            }
            session_data['behavioral_stats'] = agent.persona.get_behavioral_stats()

        # Add switcher data if available
        if hasattr(agent, 'switcher'):
            session_data['switching_stats'] = agent.get_switching_stats()
            session_data['switch_history'] = agent.get_switch_history()
            session_data['detection_risk'] = agent.get_detection_risk_assessment()

        # Save to file
        timestamp_str = time.strftime("%Y%m%d_%H%M%S", time.localtime())
        filename = f"persona_session_{session_name}_{timestamp_str}.json"
        filepath = os.path.join(self.data_dir, filename)

        with open(filepath, 'w') as f:
            json.dump(session_data, f, indent=2, default=str)

        self.logger.info(f"Persona training session saved: {filepath}")
        return filepath

    def load_persona_training_session(self, filepath: str) -> Dict[str, Any]:
        """
        Load a persona training session.

        Args:
            filepath: Path to session file

        Returns:
            Session data
        """
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Session file not found: {filepath}")

        with open(filepath, 'r') as f:
            session_data = json.load(f)

        self.logger.info(f"Persona training session loaded: {filepath}")
        return session_data

    def get_persona_training_history(self) -> List[Dict[str, Any]]:
        """
        Get history of all persona training sessions.

        Returns:
            List of session summaries
        """
        sessions = []

        for filename in os.listdir(self.data_dir):
            if filename.startswith('persona_session_') and filename.endswith('.json'):
                filepath = os.path.join(self.data_dir, filename)
                try:
                    session_data = self.load_persona_training_session(filepath)
                    summary = {
                        'filename': filename,
                        'filepath': filepath,
                        'session_name': session_data.get('session_name', 'unknown'),
                        'timestamp': session_data.get('timestamp', 0),
                        'agent_type': session_data.get('agent_type', 'unknown'),
                        'agent_name': session_data.get('agent_name', 'unknown')
                    }

                    if 'persona_config' in session_data:
                        summary['persona_name'] = session_data['persona_config'].get('name', 'unknown')

                    if 'switching_stats' in session_data:
                        summary['total_switches'] = session_data['switching_stats'].get('total_switches', 0)

                    sessions.append(summary)
                except Exception as e:
                    self.logger.warning(f"Failed to load session {filename}: {e}")

        # Sort by timestamp (newest first)
        sessions.sort(key=lambda x: x['timestamp'], reverse=True)
        return sessions
