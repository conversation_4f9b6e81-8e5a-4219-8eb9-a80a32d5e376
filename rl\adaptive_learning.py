"""
Adaptive Learning System for BlackJack Bot ML.

This module implements adaptive learning algorithms that can adjust
strategies based on environment feedback while maintaining human-like
characteristics and avoiding detection.
"""

import numpy as np
import random
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Tuple, Union
from enum import Enum

from .base_rl_agent import BaseRLAgent
from .evasion_manager import Evasion<PERSON>anager
from personas.base_persona import BasePersona, DecisionContext
from core.game_logic import GameA<PERSON>, GameState


class AdaptationTrigger(Enum):
    """Triggers for adaptive learning adjustments."""
    PERFORMANCE_DECLINE = "performance_decline"
    DETECTION_RISK = "detection_risk"
    ENVIRONMENT_CHANGE = "environment_change"
    EXPLORATION_NEED = "exploration_need"
    PERSONA_MISMATCH = "persona_mismatch"
    CONSISTENCY_DRIFT = "consistency_drift"


@dataclass
class AdaptationConfig:
    """Configuration for adaptive learning system."""
    
    # Performance monitoring
    performance_window: int = 100
    performance_threshold: float = -0.1  # Decline threshold
    min_adaptation_interval: int = 50
    
    # Learning rate adaptation
    lr_adaptation_enabled: bool = True
    lr_increase_factor: float = 1.1
    lr_decrease_factor: float = 0.9
    min_learning_rate: float = 1e-5
    max_learning_rate: float = 1e-2
    
    # Exploration adaptation
    exploration_adaptation_enabled: bool = True
    epsilon_increase_factor: float = 1.2
    epsilon_decrease_factor: float = 0.8
    min_epsilon_adaptive: float = 0.01
    max_epsilon_adaptive: float = 0.5
    
    # Persona adaptation
    persona_adaptation_enabled: bool = True
    persona_mismatch_threshold: float = 0.3
    persona_adaptation_strength: float = 0.1
    
    # Evasion adaptation
    evasion_adaptation_enabled: bool = True
    detection_risk_threshold: float = 0.8
    evasion_intensity_factor: float = 1.5
    
    # Consistency adaptation
    consistency_adaptation_enabled: bool = True
    target_consistency_range: Tuple[float, float] = (0.7, 0.85)
    consistency_adaptation_rate: float = 0.05


@dataclass
class AdaptationMetrics:
    """Metrics for tracking adaptation performance."""
    total_adaptations: int = 0
    adaptation_triggers: Dict[AdaptationTrigger, int] = field(default_factory=dict)
    performance_history: List[float] = field(default_factory=list)
    adaptation_effectiveness: float = 0.0
    last_adaptation_step: int = 0
    
    def __post_init__(self):
        """Initialize default values."""
        if not self.adaptation_triggers:
            self.adaptation_triggers = {trigger: 0 for trigger in AdaptationTrigger}


class BaseAdaptationStrategy(ABC):
    """Abstract base class for adaptation strategies."""
    
    def __init__(self, config: AdaptationConfig):
        """Initialize adaptation strategy."""
        self.config = config
        self.activation_count = 0
        self.last_activation = 0
        self.effectiveness_history = []
    
    @abstractmethod
    def should_adapt(self, agent: BaseRLAgent, metrics: Dict[str, Any]) -> bool:
        """Determine if adaptation should be triggered."""
        pass
    
    @abstractmethod
    def apply_adaptation(self, agent: BaseRLAgent, trigger: AdaptationTrigger) -> Dict[str, Any]:
        """Apply adaptation and return metadata."""
        pass
    
    def get_effectiveness_score(self) -> float:
        """Get effectiveness score for this strategy."""
        if not self.effectiveness_history:
            return 0.5  # Neutral
        return sum(self.effectiveness_history) / len(self.effectiveness_history)


class LearningRateAdaptation(BaseAdaptationStrategy):
    """Adaptive learning rate adjustment based on performance."""
    
    def should_adapt(self, agent: BaseRLAgent, metrics: Dict[str, Any]) -> bool:
        """Check if learning rate should be adapted."""
        if not self.config.lr_adaptation_enabled:
            return False
        
        # Check performance trend
        if len(metrics.get('performance_history', [])) < self.config.performance_window:
            return False
        
        recent_performance = metrics['performance_history'][-self.config.performance_window:]
        performance_trend = np.polyfit(range(len(recent_performance)), recent_performance, 1)[0]
        
        # Adapt if performance is declining or stagnating
        return performance_trend < self.config.performance_threshold
    
    def apply_adaptation(self, agent: BaseRLAgent, trigger: AdaptationTrigger) -> Dict[str, Any]:
        """Adapt learning rate based on performance."""
        old_lr = agent.optimizer.param_groups[0]['lr']
        
        if trigger == AdaptationTrigger.PERFORMANCE_DECLINE:
            # Increase learning rate to escape local minimum
            new_lr = min(old_lr * self.config.lr_increase_factor, self.config.max_learning_rate)
        else:
            # Decrease learning rate for stability
            new_lr = max(old_lr * self.config.lr_decrease_factor, self.config.min_learning_rate)
        
        # Update learning rate
        for param_group in agent.optimizer.param_groups:
            param_group['lr'] = new_lr
        
        self.activation_count += 1
        self.last_activation = agent.training_step
        
        return {
            "strategy": "learning_rate_adaptation",
            "old_lr": old_lr,
            "new_lr": new_lr,
            "trigger": trigger.value,
            "adaptation_factor": new_lr / old_lr
        }


class ExplorationAdaptation(BaseAdaptationStrategy):
    """Adaptive exploration (epsilon) adjustment."""
    
    def should_adapt(self, agent: BaseRLAgent, metrics: Dict[str, Any]) -> bool:
        """Check if exploration should be adapted."""
        if not self.config.exploration_adaptation_enabled:
            return False
        
        # Check if stuck in local optimum or need more exploration
        detection_risk = metrics.get('detection_risk', 0.0)
        performance_variance = metrics.get('performance_variance', 0.0)
        
        # Need more exploration if high detection risk or low performance variance
        return detection_risk > 0.7 or performance_variance < 0.1
    
    def apply_adaptation(self, agent: BaseRLAgent, trigger: AdaptationTrigger) -> Dict[str, Any]:
        """Adapt exploration rate."""
        old_epsilon = agent.epsilon
        
        if trigger in [AdaptationTrigger.DETECTION_RISK, AdaptationTrigger.EXPLORATION_NEED]:
            # Increase exploration
            new_epsilon = min(old_epsilon * self.config.epsilon_increase_factor, 
                            self.config.max_epsilon_adaptive)
        else:
            # Decrease exploration
            new_epsilon = max(old_epsilon * self.config.epsilon_decrease_factor,
                            self.config.min_epsilon_adaptive)
        
        agent.epsilon = new_epsilon
        
        self.activation_count += 1
        self.last_activation = agent.training_step
        
        return {
            "strategy": "exploration_adaptation",
            "old_epsilon": old_epsilon,
            "new_epsilon": new_epsilon,
            "trigger": trigger.value,
            "adaptation_factor": new_epsilon / old_epsilon if old_epsilon > 0 else 1.0
        }


class PersonaAdaptation(BaseAdaptationStrategy):
    """Adaptive persona behavior adjustment."""
    
    def should_adapt(self, agent: BaseRLAgent, metrics: Dict[str, Any]) -> bool:
        """Check if persona adaptation is needed."""
        if not self.config.persona_adaptation_enabled or not hasattr(agent, 'persona'):
            return False
        
        # Check persona-RL alignment
        persona_rl_mismatch = metrics.get('persona_rl_mismatch', 0.0)
        return persona_rl_mismatch > self.config.persona_mismatch_threshold
    
    def apply_adaptation(self, agent: BaseRLAgent, trigger: AdaptationTrigger) -> Dict[str, Any]:
        """Adapt persona behavior to better align with RL learning."""
        if not hasattr(agent, 'persona') or not agent.persona:
            return {"strategy": "persona_adaptation", "result": "no_persona"}
        
        # Adjust persona parameters to better align with RL
        old_accuracy = agent.persona.current_accuracy
        old_noise = getattr(agent.persona, 'behavioral_noise', 0.1)
        
        if trigger == AdaptationTrigger.PERSONA_MISMATCH:
            # Increase persona accuracy to align better with RL
            new_accuracy = min(old_accuracy + self.config.persona_adaptation_strength, 0.98)
            agent.persona.current_accuracy = new_accuracy
            
            # Adjust behavioral noise
            if hasattr(agent, 'evasion_manager'):
                noise_strategy = agent.evasion_manager.strategies.get('behavioral_noise')
                if noise_strategy:
                    noise_strategy.current_noise_level *= 0.9  # Reduce noise
        
        self.activation_count += 1
        self.last_activation = agent.training_step
        
        return {
            "strategy": "persona_adaptation",
            "old_accuracy": old_accuracy,
            "new_accuracy": agent.persona.current_accuracy,
            "trigger": trigger.value,
            "adaptation_strength": self.config.persona_adaptation_strength
        }


class EvasionAdaptation(BaseAdaptationStrategy):
    """Adaptive evasion strategy adjustment."""
    
    def should_adapt(self, agent: BaseRLAgent, metrics: Dict[str, Any]) -> bool:
        """Check if evasion adaptation is needed."""
        if not self.config.evasion_adaptation_enabled:
            return False
        
        detection_risk = metrics.get('detection_risk', 0.0)
        return detection_risk > self.config.detection_risk_threshold
    
    def apply_adaptation(self, agent: BaseRLAgent, trigger: AdaptationTrigger) -> Dict[str, Any]:
        """Adapt evasion strategies."""
        if not hasattr(agent, 'evasion_manager'):
            return {"strategy": "evasion_adaptation", "result": "no_evasion_manager"}
        
        evasion_manager = agent.evasion_manager
        old_config = evasion_manager.config
        
        # Increase evasion intensity
        new_noise_intensity = min(old_config.noise_intensity * self.config.evasion_intensity_factor, 0.5)
        new_disruption_prob = min(old_config.disruption_probability * self.config.evasion_intensity_factor, 0.2)
        
        # Update evasion configuration
        from .evasion_strategies import EvasionConfig
        new_config = EvasionConfig(
            consistency_threshold=old_config.consistency_threshold,
            noise_intensity=new_noise_intensity,
            disruption_probability=new_disruption_prob,
            technique_weights=old_config.technique_weights.copy()
        )
        
        evasion_manager.update_config(new_config)
        
        self.activation_count += 1
        self.last_activation = agent.training_step
        
        return {
            "strategy": "evasion_adaptation",
            "old_noise_intensity": old_config.noise_intensity,
            "new_noise_intensity": new_noise_intensity,
            "old_disruption_prob": old_config.disruption_probability,
            "new_disruption_prob": new_disruption_prob,
            "trigger": trigger.value
        }


class ConsistencyAdaptation(BaseAdaptationStrategy):
    """Adaptive consistency management."""
    
    def should_adapt(self, agent: BaseRLAgent, metrics: Dict[str, Any]) -> bool:
        """Check if consistency adaptation is needed."""
        if not self.config.consistency_adaptation_enabled:
            return False
        
        current_consistency = metrics.get('current_consistency', 0.0)
        target_min, target_max = self.config.target_consistency_range
        
        return current_consistency < target_min or current_consistency > target_max
    
    def apply_adaptation(self, agent: BaseRLAgent, trigger: AdaptationTrigger) -> Dict[str, Any]:
        """Adapt consistency levels."""
        current_consistency = agent.consistency_scores[-1] if agent.consistency_scores else 0.0
        target_min, target_max = self.config.target_consistency_range
        
        adaptation_applied = False
        
        if current_consistency > target_max:
            # Too consistent - increase randomness
            if hasattr(agent, 'evasion_manager'):
                noise_strategy = agent.evasion_manager.strategies.get('behavioral_noise')
                if noise_strategy:
                    noise_strategy.current_noise_level = min(
                        noise_strategy.current_noise_level * 1.2, 0.3
                    )
                    adaptation_applied = True
        
        elif current_consistency < target_min:
            # Too inconsistent - reduce randomness
            if hasattr(agent, 'evasion_manager'):
                noise_strategy = agent.evasion_manager.strategies.get('behavioral_noise')
                if noise_strategy:
                    noise_strategy.current_noise_level = max(
                        noise_strategy.current_noise_level * 0.8, 0.01
                    )
                    adaptation_applied = True
        
        self.activation_count += 1
        self.last_activation = agent.training_step
        
        return {
            "strategy": "consistency_adaptation",
            "current_consistency": current_consistency,
            "target_range": self.config.target_consistency_range,
            "adaptation_applied": adaptation_applied,
            "trigger": trigger.value
        }


class AdaptiveLearningSystem:
    """
    Comprehensive adaptive learning system.
    
    Coordinates multiple adaptation strategies to optimize learning
    while maintaining human-like behavior and evasion capabilities.
    """
    
    def __init__(self, config: AdaptationConfig):
        """Initialize adaptive learning system."""
        self.config = config
        self.metrics = AdaptationMetrics()
        
        # Initialize adaptation strategies
        self.strategies = {
            "learning_rate": LearningRateAdaptation(config),
            "exploration": ExplorationAdaptation(config),
            "persona": PersonaAdaptation(config),
            "evasion": EvasionAdaptation(config),
            "consistency": ConsistencyAdaptation(config)
        }
        
        # Adaptation history
        self.adaptation_history = []
        self.performance_baseline = None
        
    def update(self, agent: BaseRLAgent, current_metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Update adaptive learning system and apply adaptations if needed.
        
        Args:
            agent: RL agent to adapt
            current_metrics: Current performance and behavior metrics
            
        Returns:
            List of applied adaptations
        """
        applied_adaptations = []
        
        # Check if minimum interval has passed
        if (agent.training_step - self.metrics.last_adaptation_step < 
            self.config.min_adaptation_interval):
            return applied_adaptations
        
        # Determine adaptation triggers
        triggers = self._identify_adaptation_triggers(agent, current_metrics)
        
        # Apply adaptations based on triggers
        for trigger in triggers:
            adaptations = self._apply_adaptations_for_trigger(agent, trigger)
            applied_adaptations.extend(adaptations)
        
        # Update metrics
        if applied_adaptations:
            self.metrics.total_adaptations += len(applied_adaptations)
            self.metrics.last_adaptation_step = agent.training_step
            
            # Record adaptation event
            adaptation_event = {
                "timestamp": time.time(),
                "training_step": agent.training_step,
                "triggers": [t.value for t in triggers],
                "adaptations": applied_adaptations,
                "pre_adaptation_metrics": current_metrics.copy()
            }
            
            self.adaptation_history.append(adaptation_event)
            
            # Keep history manageable
            if len(self.adaptation_history) > 100:
                self.adaptation_history.pop(0)
        
        return applied_adaptations
    
    def _identify_adaptation_triggers(self, agent: BaseRLAgent, 
                                    metrics: Dict[str, Any]) -> List[AdaptationTrigger]:
        """Identify which adaptation triggers are active."""
        triggers = []
        
        # Performance decline trigger
        if self._check_performance_decline(metrics):
            triggers.append(AdaptationTrigger.PERFORMANCE_DECLINE)
        
        # Detection risk trigger
        detection_risk = metrics.get('detection_risk', 0.0)
        if detection_risk > self.config.detection_risk_threshold:
            triggers.append(AdaptationTrigger.DETECTION_RISK)
        
        # Exploration need trigger
        if self._check_exploration_need(metrics):
            triggers.append(AdaptationTrigger.EXPLORATION_NEED)
        
        # Persona mismatch trigger
        if self._check_persona_mismatch(agent, metrics):
            triggers.append(AdaptationTrigger.PERSONA_MISMATCH)
        
        # Consistency drift trigger
        if self._check_consistency_drift(metrics):
            triggers.append(AdaptationTrigger.CONSISTENCY_DRIFT)
        
        return triggers
    
    def _check_performance_decline(self, metrics: Dict[str, Any]) -> bool:
        """Check for performance decline."""
        performance_history = metrics.get('performance_history', [])
        
        if len(performance_history) < self.config.performance_window:
            return False
        
        recent_performance = performance_history[-self.config.performance_window:]
        trend = np.polyfit(range(len(recent_performance)), recent_performance, 1)[0]
        
        return trend < self.config.performance_threshold
    
    def _check_exploration_need(self, metrics: Dict[str, Any]) -> bool:
        """Check if more exploration is needed."""
        performance_variance = metrics.get('performance_variance', 0.0)
        return performance_variance < 0.1
    
    def _check_persona_mismatch(self, agent: BaseRLAgent, metrics: Dict[str, Any]) -> bool:
        """Check for persona-RL mismatch."""
        if not hasattr(agent, 'persona') or not agent.persona:
            return False
        
        persona_rl_mismatch = metrics.get('persona_rl_mismatch', 0.0)
        return persona_rl_mismatch > self.config.persona_mismatch_threshold
    
    def _check_consistency_drift(self, metrics: Dict[str, Any]) -> bool:
        """Check for consistency drift."""
        current_consistency = metrics.get('current_consistency', 0.0)
        target_min, target_max = self.config.target_consistency_range
        
        return current_consistency < target_min or current_consistency > target_max
    
    def _apply_adaptations_for_trigger(self, agent: BaseRLAgent, 
                                     trigger: AdaptationTrigger) -> List[Dict[str, Any]]:
        """Apply adaptations for a specific trigger."""
        adaptations = []
        
        # Determine which strategies should respond to this trigger
        strategy_mapping = {
            AdaptationTrigger.PERFORMANCE_DECLINE: ["learning_rate", "exploration"],
            AdaptationTrigger.DETECTION_RISK: ["evasion", "exploration"],
            AdaptationTrigger.EXPLORATION_NEED: ["exploration"],
            AdaptationTrigger.PERSONA_MISMATCH: ["persona"],
            AdaptationTrigger.CONSISTENCY_DRIFT: ["consistency", "evasion"]
        }
        
        relevant_strategies = strategy_mapping.get(trigger, [])
        
        for strategy_name in relevant_strategies:
            if strategy_name in self.strategies:
                strategy = self.strategies[strategy_name]
                
                # Apply adaptation
                adaptation_result = strategy.apply_adaptation(agent, trigger)
                adaptations.append(adaptation_result)
                
                # Update trigger count
                self.metrics.adaptation_triggers[trigger] += 1
        
        return adaptations
    
    def get_adaptation_metrics(self) -> AdaptationMetrics:
        """Get current adaptation metrics."""
        return self.metrics
    
    def get_adaptation_history(self) -> List[Dict[str, Any]]:
        """Get adaptation history."""
        return self.adaptation_history.copy()
    
    def get_strategy_effectiveness(self) -> Dict[str, float]:
        """Get effectiveness scores for each adaptation strategy."""
        return {
            name: strategy.get_effectiveness_score()
            for name, strategy in self.strategies.items()
        }
    
    def reset_metrics(self) -> None:
        """Reset adaptation metrics."""
        self.metrics = AdaptationMetrics()
        self.adaptation_history = []
        
        # Reset strategy counters
        for strategy in self.strategies.values():
            strategy.activation_count = 0
            strategy.last_activation = 0
            strategy.effectiveness_history = []
