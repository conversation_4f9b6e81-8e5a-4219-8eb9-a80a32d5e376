"""
Unit tests for BlackjackGame class.

Tests P1_T3 implementation: Complete game logic with all player actions.
"""

import unittest
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from core.card import Card, Suit, Rank
from core.hand import Hand
from core.game_logic import BlackjackGame, GameAction, GameResult


class TestBlackjackGame(unittest.TestCase):
    """Test cases for the BlackjackGame class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.game = BlackjackGame(num_decks=1)  # Use single deck for predictable testing
    
    def test_game_initialization(self):
        """Test game initialization."""
        self.assertIsNotNone(self.game.deck)
        self.assertEqual(self.game.dealer_hits_soft_17, True)
        self.assertEqual(self.game.double_after_split, True)
        self.assertEqual(self.game.blackjack_payout, 1.5)
    
    def test_new_game_start(self):
        """Test starting a new game."""
        state = self.game.start_new_game(bet_amount=10.0)

        # Check initial state
        self.assertEqual(len(state.player_hands), 1)
        self.assertEqual(len(state.player_hands[0]), 2)
        self.assertEqual(len(state.dealer_hand), 2)
        self.assertEqual(state.bet_amount, 10.0)
        self.assertEqual(state.current_hand_index, 0)
        # Note: game_over might be True if blackjacks are dealt
    
    def test_available_actions_basic(self):
        """Test basic available actions."""
        # Create a controlled game state
        self.game.start_new_game()
        
        # Manually set up a non-blackjack, non-pair hand
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.FIVE))
        player_hand.add_card(Card(Suit.SPADES, Rank.SEVEN))
        
        self.game.state.player_hands = [player_hand]
        self.game.state.can_double = [True]
        self.game.state.can_split = [False]
        
        actions = self.game.get_available_actions()
        
        # Should have hit, stand, and double
        self.assertIn(GameAction.HIT, actions)
        self.assertIn(GameAction.STAND, actions)
        self.assertIn(GameAction.DOUBLE, actions)
        self.assertNotIn(GameAction.SPLIT, actions)
    
    def test_available_actions_pair(self):
        """Test available actions with a pair."""
        self.game.start_new_game()
        
        # Create a pair
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.EIGHT))
        player_hand.add_card(Card(Suit.SPADES, Rank.EIGHT))
        
        self.game.state.player_hands = [player_hand]
        self.game.state.can_double = [True]
        self.game.state.can_split = [True]
        
        actions = self.game.get_available_actions()
        
        # Should have all actions including split
        self.assertIn(GameAction.HIT, actions)
        self.assertIn(GameAction.STAND, actions)
        self.assertIn(GameAction.DOUBLE, actions)
        self.assertIn(GameAction.SPLIT, actions)
    
    def test_hit_action(self):
        """Test hit action."""
        self.game.start_new_game()
        
        # Set up a hand that can take hits
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.FIVE))
        player_hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        self.game.state.player_hands = [player_hand]
        initial_cards = len(player_hand)
        
        # Take hit action
        self.game.take_action(GameAction.HIT)
        
        # Should have one more card
        self.assertEqual(len(player_hand), initial_cards + 1)
    
    def test_stand_action(self):
        """Test stand action."""
        self.game.start_new_game()
        
        # Set up a simple hand
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        player_hand.add_card(Card(Suit.SPADES, Rank.NINE))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SIX))
        dealer_hand.add_card(Card(Suit.DIAMONDS, Rank.FIVE))
        
        self.game.state.player_hands = [player_hand]
        self.game.state.dealer_hand = dealer_hand
        
        # Take stand action
        self.game.take_action(GameAction.STAND)
        
        # Game should be over and dealer should have played
        self.assertTrue(self.game.state.game_over)
        self.assertGreater(len(self.game.state.dealer_hand), 2)  # Dealer should have hit
    
    def test_double_action(self):
        """Test double down action."""
        self.game.start_new_game(bet_amount=10.0)

        # Set up a hand suitable for doubling
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.FIVE))
        player_hand.add_card(Card(Suit.SPADES, Rank.SIX))

        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        dealer_hand.add_card(Card(Suit.DIAMONDS, Rank.EIGHT))

        self.game.state.player_hands = [player_hand]
        self.game.state.dealer_hand = dealer_hand
        self.game.state.can_double = [True]
        self.game.state.game_over = False  # Ensure game is not over

        # Take double action
        self.game.take_action(GameAction.DOUBLE)

        # Bet should be doubled and hand should have exactly 3 cards
        self.assertEqual(self.game.state.bet_amount, 20.0)
        self.assertEqual(len(player_hand), 3)
    
    def test_split_action(self):
        """Test split action."""
        self.game.start_new_game()

        # Set up a pair
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.EIGHT))
        player_hand.add_card(Card(Suit.SPADES, Rank.EIGHT))

        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        dealer_hand.add_card(Card(Suit.DIAMONDS, Rank.NINE))

        self.game.state.player_hands = [player_hand]
        self.game.state.dealer_hand = dealer_hand
        self.game.state.can_double = [True]
        self.game.state.can_split = [True]
        self.game.state.game_over = False  # Ensure game is not over

        # Take split action
        self.game.take_action(GameAction.SPLIT)

        # Should now have two hands
        self.assertEqual(len(self.game.state.player_hands), 2)

        # Each hand should have 2 cards (original + new card)
        self.assertEqual(len(self.game.state.player_hands[0]), 2)
        self.assertEqual(len(self.game.state.player_hands[1]), 2)
    
    def test_blackjack_detection(self):
        """Test blackjack detection and immediate resolution."""
        self.game.start_new_game()
        
        # Set up player blackjack
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.ACE))
        player_hand.add_card(Card(Suit.SPADES, Rank.KING))
        
        # Set up dealer non-blackjack
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        dealer_hand.add_card(Card(Suit.DIAMONDS, Rank.NINE))
        
        self.game.state.player_hands = [player_hand]
        self.game.state.dealer_hand = dealer_hand
        
        # Manually trigger blackjack resolution
        self.game._resolve_blackjacks()
        
        self.assertTrue(self.game.state.game_over)
        self.assertEqual(self.game.state.results[0], GameResult.PLAYER_BLACKJACK)
    
    def test_dealer_soft_17_rule(self):
        """Test dealer hits soft 17 rule."""
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.HEARTS, Rank.ACE))
        dealer_hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        # Dealer should hit soft 17
        self.assertTrue(self.game._dealer_should_hit(dealer_hand))
        
        # Add another card to make it hard 17
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))  # Now hard 17
        
        # Dealer should not hit hard 17
        self.assertFalse(self.game._dealer_should_hit(dealer_hand))
    
    def test_bust_handling(self):
        """Test bust detection and handling."""
        self.game.start_new_game()
        
        # Create a hand that will bust
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        player_hand.add_card(Card(Suit.SPADES, Rank.NINE))
        player_hand.add_card(Card(Suit.CLUBS, Rank.FIVE))  # 24 - bust
        
        self.game.state.player_hands = [player_hand]
        
        # Manually resolve game
        self.game._resolve_game()
        
        self.assertEqual(self.game.state.results[0], GameResult.PLAYER_BUST)
    
    def test_invalid_action_handling(self):
        """Test handling of invalid actions."""
        self.game.start_new_game()
        
        # Set up a hand that cannot split
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        player_hand.add_card(Card(Suit.SPADES, Rank.NINE))
        
        self.game.state.player_hands = [player_hand]
        self.game.state.can_split = [False]
        
        # Try to split - should raise ValueError
        with self.assertRaises(ValueError):
            self.game.take_action(GameAction.SPLIT)
    
    def test_game_state_consistency(self):
        """Test game state remains consistent throughout play."""
        self.game.start_new_game()
        
        # Play a simple game
        if GameAction.HIT in self.game.get_available_actions():
            self.game.take_action(GameAction.HIT)
        
        if GameAction.STAND in self.game.get_available_actions():
            self.game.take_action(GameAction.STAND)
        
        # Game should be over with valid results
        self.assertTrue(self.game.state.game_over)
        self.assertGreater(len(self.game.state.results), 0)
        self.assertIn(self.game.state.results[0], list(GameResult))


if __name__ == '__main__':
    unittest.main()
