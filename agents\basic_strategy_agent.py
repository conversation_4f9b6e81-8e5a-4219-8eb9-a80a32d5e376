"""
Basic Strategy agent for BlackJack Bot ML.

This module implements a perfect Basic Strategy player that makes optimal
decisions according to mathematically proven blackjack strategy for
6-deck H17 DAS rules.
"""

import os
from typing import Optional
from .base_agent import BaseAgent
from core.game_logic import GameAction, GameState
from utils.basic_strategy_charts import BasicStrategyCharts, BasicStrategyAction


class BasicStrategyAgent(BaseAgent):
    """
    Perfect Basic Strategy blackjack agent.
    
    This agent plays optimal Basic Strategy according to 6-deck H17 DAS rules,
    making mathematically correct decisions for every situation.
    """
    
    def __init__(self, name: str = "Basic Strategy Bot", 
                 charts_file: Optional[str] = None):
        """
        Initialize the Basic Strategy agent.
        
        Args:
            name: Name for this agent
            charts_file: Path to Basic Strategy charts JSON file (optional)
        """
        super().__init__(name)
        
        # Load Basic Strategy charts
        if charts_file and os.path.exists(charts_file):
            self.charts = BasicStrategyCharts.load_from_file(charts_file)
        else:
            # Create charts from scratch if file not provided
            self.charts = BasicStrategyCharts()
        
        # Basic Strategy uses flat betting
        self.bet_amount = 1.0
    
    def get_action(self, game_state: GameState, hand_index: int = 0) -> GameAction:
        """
        Get the optimal Basic Strategy action for the current situation.
        
        Args:
            game_state: Current state of the blackjack game
            hand_index: Index of the hand to make decision for
            
        Returns:
            The optimal Basic Strategy action
        """
        if hand_index >= len(game_state.player_hands):
            raise ValueError(f"Invalid hand index: {hand_index}")
        
        player_hand = game_state.player_hands[hand_index]
        dealer_upcard = self._get_dealer_upcard_value(game_state)
        
        # Check what actions are available
        can_double = (hand_index < len(game_state.can_double) and 
                     game_state.can_double[hand_index])
        can_split = (hand_index < len(game_state.can_split) and 
                    game_state.can_split[hand_index])
        
        # Get Basic Strategy recommendation
        bs_action = self.charts.get_action(
            player_hand, 
            dealer_upcard, 
            can_double=can_double, 
            can_split=can_split
        )
        
        # Convert Basic Strategy action to game action
        return self._convert_bs_action_to_game_action(bs_action)
    
    def get_bet_amount(self, min_bet: float = 1.0, max_bet: float = 100.0) -> float:
        """
        Get the bet amount (Basic Strategy uses flat betting).
        
        Args:
            min_bet: Minimum allowed bet
            max_bet: Maximum allowed bet
            
        Returns:
            The bet amount (always minimum for Basic Strategy)
        """
        return max(min_bet, min(self.bet_amount, max_bet))
    
    def set_bet_amount(self, amount: float) -> None:
        """
        Set the flat bet amount for this agent.
        
        Args:
            amount: The bet amount to use
        """
        self.bet_amount = amount
    
    def _get_dealer_upcard_value(self, game_state: GameState) -> int:
        """
        Get the dealer's upcard value for Basic Strategy lookup.
        
        Args:
            game_state: Current game state
            
        Returns:
            Dealer upcard value (2-11, where 11 = Ace)
        """
        if not game_state.dealer_hand.cards:
            raise ValueError("Dealer has no cards")
        
        # First card is the upcard
        upcard = game_state.dealer_hand.cards[0]
        
        if upcard.is_ace:
            return 11  # Ace is represented as 11 in Basic Strategy charts
        else:
            return upcard.get_value()
    
    def _convert_bs_action_to_game_action(self, bs_action: BasicStrategyAction) -> GameAction:
        """
        Convert Basic Strategy action to game action.
        
        Args:
            bs_action: Basic Strategy action from charts
            
        Returns:
            Corresponding game action
        """
        action_mapping = {
            BasicStrategyAction.HIT: GameAction.HIT,
            BasicStrategyAction.STAND: GameAction.STAND,
            BasicStrategyAction.DOUBLE: GameAction.DOUBLE,
            BasicStrategyAction.SPLIT: GameAction.SPLIT,
            BasicStrategyAction.SURRENDER: GameAction.HIT,  # Surrender not implemented
        }
        
        return action_mapping.get(bs_action, GameAction.HIT)
    
    def get_strategy_accuracy(self, game_state: GameState, hand_index: int = 0) -> float:
        """
        Get the strategy accuracy (always 1.0 for perfect Basic Strategy).
        
        Args:
            game_state: Current game state
            hand_index: Hand index
            
        Returns:
            Strategy accuracy (always 1.0)
        """
        return 1.0
    
    def get_decision_time(self) -> float:
        """
        Get decision time in seconds (very fast for Basic Strategy).
        
        Returns:
            Decision time in seconds (always very low)
        """
        return 0.1  # Basic Strategy decisions are instant
    
    def save_charts(self, filepath: str) -> None:
        """
        Save the Basic Strategy charts to a file.
        
        Args:
            filepath: Path to save the charts
        """
        self.charts.save_to_file(filepath)
    
    def get_expected_value(self, game_state: GameState, hand_index: int = 0) -> float:
        """
        Get the expected value of the current situation (theoretical).
        
        This is a simplified estimate based on Basic Strategy theory.
        
        Args:
            game_state: Current game state
            hand_index: Hand index
            
        Returns:
            Estimated expected value
        """
        # This is a simplified calculation
        # In reality, expected value depends on exact composition and rules
        
        player_hand = game_state.player_hands[hand_index]
        dealer_upcard = self._get_dealer_upcard_value(game_state)
        
        # Basic estimates based on common Basic Strategy knowledge
        if player_hand.is_blackjack():
            return 1.5  # 3:2 payout
        elif player_hand.is_bust():
            return -1.0  # Always lose
        elif player_hand.get_value() == 21:
            return 0.8  # Very likely to win
        elif player_hand.get_value() >= 17:
            if dealer_upcard <= 6:
                return 0.4  # Good position vs weak dealer
            else:
                return -0.1  # Marginal vs strong dealer
        else:
            # More complex calculation would be needed for accurate EV
            return 0.0  # Neutral estimate
    
    def __str__(self) -> str:
        """String representation of the agent."""
        return f"BasicStrategyAgent(name='{self.name}', bet=${self.bet_amount})"
