"""
Unit tests for Base Persona framework.

Tests P2_T1 implementation: Persona Framework Implementation.
"""

import unittest
import sys
import os
import time

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from personas.base_persona import (
    BasePersona, PersonaConfig, DecisionPattern, DecisionContext, ErrorType
)
from core.card import Card, Suit, Rank
from core.hand import Hand
from core.game_logic import GameState, GameAction
from utils.basic_strategy_charts import BasicStrategyAction


class TestPersona(BasePersona):
    """Test implementation of BasePersona for testing."""
    
    def _apply_persona_bias(self, optimal_action, player_hand, dealer_upcard, game_state):
        """Simple test implementation - just return optimal action."""
        return optimal_action


class TestBasePersona(unittest.TestCase):
    """Test cases for the BasePersona framework."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create test persona configuration
        decision_pattern = DecisionPattern(
            base_accuracy=0.9,
            decision_speed=1.0,
            avg_decision_time=2.0,
            min_decision_time=0.5,
            max_decision_time=5.0
        )
        
        self.config = PersonaConfig(
            name="Test Persona",
            description="A test persona for unit testing",
            decision_pattern=decision_pattern
        )
        
        self.persona = TestPersona(self.config)
    
    def test_persona_initialization(self):
        """Test persona initialization."""
        self.assertEqual(self.persona.config.name, "Test Persona")
        self.assertEqual(self.persona.hands_played, 0)
        self.assertEqual(self.persona.current_context, DecisionContext.NORMAL)
        self.assertEqual(self.persona.current_accuracy, 0.9)
        self.assertIsNotNone(self.persona.charts)
    
    def test_decision_pattern_defaults(self):
        """Test decision pattern default values."""
        pattern = DecisionPattern()
        self.assertEqual(pattern.base_accuracy, 0.95)
        self.assertEqual(pattern.decision_speed, 1.0)
        self.assertEqual(pattern.avg_decision_time, 2.5)
        self.assertGreater(pattern.min_decision_time, 0)
        self.assertLess(pattern.max_decision_time, 10)
    
    def test_persona_config_defaults(self):
        """Test persona config default initialization."""
        pattern = DecisionPattern()
        config = PersonaConfig("Test", "Description", pattern)
        
        # Check error patterns are initialized
        self.assertIn(ErrorType.STRATEGY_DEVIATION, config.error_patterns)
        self.assertIn(ErrorType.TIMING_INCONSISTENCY, config.error_patterns)
        
        # Check context modifiers are initialized
        self.assertIn(DecisionContext.PRESSURE, config.context_modifiers)
        self.assertIn(DecisionContext.CONFIDENT, config.context_modifiers)
    
    def test_decision_timing_simulation(self):
        """Test decision timing simulation."""
        # Create test hand
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        # Test timing simulation
        timing = self.persona._simulate_decision_timing(hand, 10)
        
        # Should be within configured bounds
        self.assertGreaterEqual(timing, self.config.decision_pattern.min_decision_time)
        self.assertLessEqual(timing, self.config.decision_pattern.max_decision_time)
        self.assertIsInstance(timing, float)
    
    def test_timing_complexity_factors(self):
        """Test that hand complexity affects timing."""
        # Simple hard hand
        simple_hand = Hand()
        simple_hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        simple_hand.add_card(Card(Suit.SPADES, Rank.NINE))
        simple_time = self.persona._simulate_decision_timing(simple_hand, 6)
        
        # Complex soft hand
        soft_hand = Hand()
        soft_hand.add_card(Card(Suit.HEARTS, Rank.ACE))
        soft_hand.add_card(Card(Suit.SPADES, Rank.SIX))
        soft_time = self.persona._simulate_decision_timing(soft_hand, 6)
        
        # Pair hand
        pair_hand = Hand()
        pair_hand.add_card(Card(Suit.HEARTS, Rank.EIGHT))
        pair_hand.add_card(Card(Suit.SPADES, Rank.EIGHT))
        pair_time = self.persona._simulate_decision_timing(pair_hand, 6)
        
        # Complex hands should generally take longer (though variance may cause exceptions)
        # Test multiple times to account for randomness
        soft_times = [self.persona._simulate_decision_timing(soft_hand, 6) for _ in range(10)]
        simple_times = [self.persona._simulate_decision_timing(simple_hand, 6) for _ in range(10)]
        
        avg_soft = sum(soft_times) / len(soft_times)
        avg_simple = sum(simple_times) / len(simple_times)
        
        # Soft hands should generally take longer on average
        self.assertGreater(avg_soft, avg_simple * 0.9)  # Allow some variance
    
    def test_context_updates(self):
        """Test context updates based on game state."""
        # Simulate losing streak
        for _ in range(4):
            self.persona.update_result("loss")
        
        # Create dummy game state
        game_state = GameState()
        game_state.player_hands = [Hand()]
        game_state.dealer_hand = Hand()
        game_state.dealer_hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        
        self.persona._update_context(game_state)
        self.assertEqual(self.persona.current_context, DecisionContext.PRESSURE)
        
        # Reset and simulate winning streak
        self.persona.consecutive_losses = 0
        for _ in range(5):
            self.persona.update_result("win")
        
        self.persona._update_context(game_state)
        self.assertEqual(self.persona.current_context, DecisionContext.CONFIDENT)
    
    def test_accuracy_degradation(self):
        """Test accuracy degradation with fatigue."""
        initial_accuracy = self.persona._get_effective_accuracy()
        
        # Simulate many hands
        self.persona.hands_played = 1000
        
        degraded_accuracy = self.persona._get_effective_accuracy()
        
        # Accuracy should degrade with fatigue
        self.assertLess(degraded_accuracy, initial_accuracy)
        self.assertGreaterEqual(degraded_accuracy, 0.1)  # Should not go below minimum
    
    def test_error_decision_generation(self):
        """Test error decision generation."""
        # Create test scenario
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        optimal_action = BasicStrategyAction.STAND
        
        # Generate error decisions multiple times
        error_actions = []
        for _ in range(20):
            error_action = self.persona._make_error_decision(optimal_action, hand, 6, None)
            error_actions.append(error_action)
        
        # Should generate some different actions
        unique_actions = set(error_actions)
        self.assertGreaterEqual(len(unique_actions), 1)
        
        # Should include some non-optimal actions
        non_optimal = [a for a in error_actions if a != optimal_action]
        self.assertGreater(len(non_optimal), 0)
    
    def test_decision_recording(self):
        """Test decision recording and history tracking."""
        # Create test scenario
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        game_state = GameState()
        game_state.player_hands = [hand]
        game_state.dealer_hand = Hand()
        game_state.dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        
        optimal_action = BasicStrategyAction.STAND
        actual_action = BasicStrategyAction.HIT  # Error
        decision_time = 2.5
        
        # Record decision
        self.persona._record_decision(
            optimal_action, actual_action, decision_time, hand, 10, game_state
        )
        
        # Check decision history
        self.assertEqual(len(self.persona.decision_history), 1)
        decision = self.persona.decision_history[0]
        
        self.assertEqual(decision["optimal_action"], "S")
        self.assertEqual(decision["actual_action"], "H")
        self.assertEqual(decision["decision_time"], 2.5)
        self.assertTrue(decision["is_error"])
        
        # Check error history
        self.assertEqual(len(self.persona.error_history), 1)
        error = self.persona.error_history[0]
        self.assertEqual(error["optimal"], "S")
        self.assertEqual(error["actual"], "H")
    
    def test_behavioral_stats_calculation(self):
        """Test behavioral statistics calculation."""
        # Record some decisions
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        game_state = GameState()
        game_state.player_hands = [hand]
        game_state.dealer_hand = Hand()
        game_state.dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        
        # Record correct decision
        self.persona._record_decision(
            BasicStrategyAction.STAND, BasicStrategyAction.STAND, 2.0, hand, 10, game_state
        )
        
        # Record error decision
        self.persona._record_decision(
            BasicStrategyAction.STAND, BasicStrategyAction.HIT, 3.0, hand, 10, game_state
        )
        
        # Get stats
        stats = self.persona.get_behavioral_stats()
        
        self.assertEqual(stats["total_decisions"], 2)
        self.assertEqual(stats["error_count"], 1)
        self.assertEqual(stats["accuracy"], 0.5)
        self.assertEqual(stats["avg_decision_time"], 2.5)
        self.assertIn("context_distribution", stats)
        self.assertIn("error_types", stats)
    
    def test_session_reset(self):
        """Test session reset functionality."""
        # Modify persona state
        self.persona.hands_played = 100
        self.persona.consecutive_wins = 5
        self.persona.current_accuracy = 0.7
        self.persona.decision_history = [{"test": "data"}]
        
        # Reset session
        self.persona.reset_session()
        
        # Check reset state
        self.assertEqual(self.persona.hands_played, 0)
        self.assertEqual(self.persona.consecutive_wins, 0)
        self.assertEqual(self.persona.current_accuracy, self.config.decision_pattern.base_accuracy)
        self.assertEqual(len(self.persona.decision_history), 0)
        self.assertEqual(self.persona.current_context, DecisionContext.NORMAL)
    
    def test_result_updates(self):
        """Test result updates and streak tracking."""
        # Test win streak
        self.persona.update_result("win")
        self.persona.update_result("blackjack")
        self.assertEqual(self.persona.consecutive_wins, 2)
        self.assertEqual(self.persona.consecutive_losses, 0)
        
        # Test loss breaks win streak
        self.persona.update_result("loss")
        self.assertEqual(self.persona.consecutive_wins, 0)
        self.assertEqual(self.persona.consecutive_losses, 1)
        
        # Test push doesn't affect streaks
        self.persona.update_result("push")
        self.assertEqual(self.persona.consecutive_wins, 0)
        self.assertEqual(self.persona.consecutive_losses, 1)
    
    def test_context_modifiers(self):
        """Test context modifier application."""
        original_accuracy = self.persona.current_accuracy
        
        # Set pressure context
        self.persona.current_context = DecisionContext.PRESSURE
        self.persona._apply_context_modifiers()
        
        # Accuracy should be modified
        self.assertNotEqual(self.persona.current_accuracy, original_accuracy)
        
        # Reset and test confident context
        self.persona.current_context = DecisionContext.CONFIDENT
        self.persona._apply_context_modifiers()
        
        # Should be different from pressure context
        confident_accuracy = self.persona.current_accuracy
        self.persona.current_context = DecisionContext.PRESSURE
        self.persona._apply_context_modifiers()
        pressure_accuracy = self.persona.current_accuracy
        
        self.assertNotEqual(confident_accuracy, pressure_accuracy)


if __name__ == '__main__':
    unittest.main()
