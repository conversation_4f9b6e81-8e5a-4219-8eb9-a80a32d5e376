"""
Training Pipeline for BlackJack Bot ML.

This module implements a comprehensive training pipeline that integrates
RL learning, evasion strategies, adaptive learning, and persona systems.
"""

import time
import json
import pickle
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum

from .evasive_dqn_agent import EvasiveDQNAgent
from .dqn_agent import DQNConfig
from .evasion_strategies import EvasionConfig
from .adaptive_learning import AdaptationConfig
from .rl_environment import BlackjackRLEnvironment
from personas.persona_switcher import PersonaSwitcher, SwitchConfig
from core.game_logic import GameState, GameAction
# Note: BlackjackSimulator not implemented yet - using environment for evaluation


class TrainingPhase(Enum):
    """Training phases."""
    INITIALIZATION = "initialization"
    EXPLORATION = "exploration"
    LEARNING = "learning"
    OPTIMIZATION = "optimization"
    EVALUATION = "evaluation"
    COMPLETED = "completed"


@dataclass
class TrainingConfig:
    """Configuration for training pipeline."""
    
    # Training parameters
    total_episodes: int = 10000
    episodes_per_phase: Dict[TrainingPhase, int] = field(default_factory=lambda: {
        TrainingPhase.EXPLORATION: 2000,
        TrainingPhase.LEARNING: 5000,
        TrainingPhase.OPTIMIZATION: 2000,
        TrainingPhase.EVALUATION: 1000
    })
    
    # Evaluation parameters
    evaluation_frequency: int = 500
    evaluation_episodes: int = 100
    
    # Checkpointing
    checkpoint_frequency: int = 1000
    save_directory: str = "training_checkpoints"
    
    # Performance thresholds
    target_win_rate: float = 0.45  # Target win rate
    target_consistency: float = 0.8  # Target consistency
    max_detection_risk: float = 0.3  # Maximum acceptable detection risk
    
    # Early stopping
    early_stopping_enabled: bool = True
    patience: int = 2000  # Episodes without improvement
    min_improvement: float = 0.01
    
    # Logging
    log_frequency: int = 100
    detailed_logging: bool = True


@dataclass
class TrainingMetrics:
    """Metrics for tracking training progress."""
    
    # Episode tracking
    current_episode: int = 0
    current_phase: TrainingPhase = TrainingPhase.INITIALIZATION
    
    # Performance metrics
    win_rate_history: List[float] = field(default_factory=list)
    avg_reward_history: List[float] = field(default_factory=list)
    consistency_history: List[float] = field(default_factory=list)
    detection_risk_history: List[float] = field(default_factory=list)
    
    # Training efficiency
    episodes_per_second: float = 0.0
    total_training_time: float = 0.0
    
    # Best performance
    best_win_rate: float = 0.0
    best_episode: int = 0
    episodes_since_improvement: int = 0
    
    # Phase completion
    completed_phases: List[TrainingPhase] = field(default_factory=list)


class TrainingPipeline:
    """
    Comprehensive training pipeline for BlackJack Bot ML.
    
    Integrates RL learning, evasion strategies, adaptive learning,
    and persona systems into a cohesive training process.
    """
    
    def __init__(self, 
                 dqn_config: DQNConfig,
                 evasion_config: EvasionConfig,
                 adaptation_config: AdaptationConfig,
                 training_config: TrainingConfig,
                 persona_switcher: Optional[PersonaSwitcher] = None):
        """
        Initialize training pipeline.
        
        Args:
            dqn_config: DQN agent configuration
            evasion_config: Evasion strategies configuration
            adaptation_config: Adaptive learning configuration
            training_config: Training pipeline configuration
            persona_switcher: Optional persona switcher
        """
        self.dqn_config = dqn_config
        self.evasion_config = evasion_config
        self.adaptation_config = adaptation_config
        self.training_config = training_config
        self.persona_switcher = persona_switcher
        
        # Initialize components
        self.agent = None
        self.environment = None
        self.simulator = None
        
        # Training state
        self.metrics = TrainingMetrics()
        self.training_log = []
        self.is_training = False
        
        # Callbacks
        self.episode_callbacks: List[Callable] = []
        self.phase_callbacks: List[Callable] = []
        
        # Setup directories
        self.save_dir = Path(training_config.save_directory)
        self.save_dir.mkdir(exist_ok=True)
    
    def initialize_training(self) -> None:
        """Initialize training components."""
        print("Initializing training pipeline...")
        
        # Create agent
        self.agent = EvasiveDQNAgent(
            name="BlackJack RL Agent",
            dqn_config=self.dqn_config,
            evasion_config=self.evasion_config,
            adaptation_config=self.adaptation_config,
            persona_switcher=self.persona_switcher
        )
        
        # Create environment
        self.environment = BlackjackRLEnvironment(num_decks=6)
        
        # Note: Using environment for evaluation (simulator not implemented yet)
        self.simulator = None
        
        # Set initial phase
        self.metrics.current_phase = TrainingPhase.EXPLORATION
        
        print(f"✅ Training initialized - Agent: {self.agent.name}")
        print(f"   Device: {self.agent.device}")
        print(f"   Network: {self.agent.get_network_info()['total_parameters']} parameters")
    
    def train(self) -> TrainingMetrics:
        """
        Execute complete training pipeline.
        
        Returns:
            Final training metrics
        """
        if not self.agent:
            self.initialize_training()
        
        print(f"Starting training for {self.training_config.total_episodes} episodes...")
        self.is_training = True
        start_time = time.time()
        
        try:
            # Training loop
            for episode in range(self.training_config.total_episodes):
                self.metrics.current_episode = episode
                
                # Update training phase
                self._update_training_phase()
                
                # Run episode
                episode_metrics = self._run_episode()
                
                # Update metrics
                self._update_metrics(episode_metrics)
                
                # Execute callbacks
                self._execute_episode_callbacks(episode_metrics)
                
                # Periodic evaluation
                if episode % self.training_config.evaluation_frequency == 0:
                    self._evaluate_agent()
                
                # Checkpointing
                if episode % self.training_config.checkpoint_frequency == 0:
                    self._save_checkpoint()
                
                # Logging
                if episode % self.training_config.log_frequency == 0:
                    self._log_progress()
                
                # Early stopping check
                if self._check_early_stopping():
                    print(f"Early stopping triggered at episode {episode}")
                    break
        
        except KeyboardInterrupt:
            print("Training interrupted by user")
        
        finally:
            self.is_training = False
            self.metrics.total_training_time = time.time() - start_time
            
            # Final evaluation
            print("Performing final evaluation...")
            final_evaluation = self._evaluate_agent()
            
            # Save final model
            self._save_final_model()
            
            print(f"Training completed in {self.metrics.total_training_time:.2f} seconds")
            print(f"Final win rate: {final_evaluation['win_rate']:.3f}")
            print(f"Final detection risk: {final_evaluation['detection_risk']:.3f}")
        
        return self.metrics
    
    def _run_episode(self) -> Dict[str, Any]:
        """Run a single training episode."""
        # Reset environment
        state = self.environment.reset()
        total_reward = 0.0
        steps = 0
        episode_actions = []
        
        while not self.environment.done:
            # Get action from agent
            action = self.agent.get_action(state.game_state)
            episode_actions.append(action)
            
            # Take step in environment
            next_state, reward, done = self.environment.step(action)
            
            # Update agent
            self.agent.update_experience(reward, next_state.game_state, done)
            
            # Update for next iteration
            state = next_state
            total_reward += reward
            steps += 1
        
        # Calculate episode metrics
        episode_metrics = {
            "episode": self.metrics.current_episode,
            "total_reward": total_reward,
            "steps": steps,
            "actions": episode_actions,
            "win": total_reward > 0,
            "detection_risk": self.agent.evasion_manager.current_risk_level,
            "consistency": self.agent.consistency_scores[-1] if self.agent.consistency_scores else 0.0
        }
        
        return episode_metrics
    
    def _update_training_phase(self) -> None:
        """Update current training phase based on episode count."""
        episode = self.metrics.current_episode
        
        # Determine current phase
        if episode < self.training_config.episodes_per_phase[TrainingPhase.EXPLORATION]:
            new_phase = TrainingPhase.EXPLORATION
        elif episode < (self.training_config.episodes_per_phase[TrainingPhase.EXPLORATION] + 
                       self.training_config.episodes_per_phase[TrainingPhase.LEARNING]):
            new_phase = TrainingPhase.LEARNING
        elif episode < (self.training_config.episodes_per_phase[TrainingPhase.EXPLORATION] + 
                       self.training_config.episodes_per_phase[TrainingPhase.LEARNING] +
                       self.training_config.episodes_per_phase[TrainingPhase.OPTIMIZATION]):
            new_phase = TrainingPhase.OPTIMIZATION
        else:
            new_phase = TrainingPhase.EVALUATION
        
        # Check for phase transition
        if new_phase != self.metrics.current_phase:
            old_phase = self.metrics.current_phase
            self.metrics.current_phase = new_phase
            
            if old_phase not in self.metrics.completed_phases:
                self.metrics.completed_phases.append(old_phase)
            
            print(f"Phase transition: {old_phase.value} → {new_phase.value}")
            self._execute_phase_callbacks(old_phase, new_phase)
            
            # Adjust agent parameters for new phase
            self._adjust_agent_for_phase(new_phase)
    
    def _adjust_agent_for_phase(self, phase: TrainingPhase) -> None:
        """Adjust agent parameters for training phase."""
        if phase == TrainingPhase.EXPLORATION:
            # High exploration
            self.agent.epsilon = 0.9
            self.agent.epsilon_decay = 0.995
        elif phase == TrainingPhase.LEARNING:
            # Balanced exploration/exploitation
            self.agent.epsilon = 0.5
            self.agent.epsilon_decay = 0.999
        elif phase == TrainingPhase.OPTIMIZATION:
            # Low exploration, focus on optimization
            self.agent.epsilon = 0.1
            self.agent.epsilon_decay = 0.9995
        elif phase == TrainingPhase.EVALUATION:
            # Minimal exploration
            self.agent.epsilon = 0.05
            self.agent.epsilon_decay = 1.0
    
    def _update_metrics(self, episode_metrics: Dict[str, Any]) -> None:
        """Update training metrics with episode results."""
        # Calculate running averages
        window_size = 100
        
        # Win rate
        recent_wins = [m.get("win", False) for m in self.training_log[-window_size:]]
        recent_wins.append(episode_metrics["win"])
        win_rate = sum(recent_wins) / len(recent_wins)
        self.metrics.win_rate_history.append(win_rate)
        
        # Average reward
        recent_rewards = [m.get("total_reward", 0) for m in self.training_log[-window_size:]]
        recent_rewards.append(episode_metrics["total_reward"])
        avg_reward = sum(recent_rewards) / len(recent_rewards)
        self.metrics.avg_reward_history.append(avg_reward)
        
        # Consistency
        self.metrics.consistency_history.append(episode_metrics["consistency"])
        
        # Detection risk
        self.metrics.detection_risk_history.append(episode_metrics["detection_risk"])
        
        # Check for best performance
        if win_rate > self.metrics.best_win_rate:
            self.metrics.best_win_rate = win_rate
            self.metrics.best_episode = self.metrics.current_episode
            self.metrics.episodes_since_improvement = 0
        else:
            self.metrics.episodes_since_improvement += 1
        
        # Calculate training speed
        if len(self.training_log) > 0:
            time_diff = time.time() - self.training_log[-1].get("timestamp", time.time())
            if time_diff > 0:
                self.metrics.episodes_per_second = 1.0 / time_diff
        
        # Add to training log
        episode_metrics["timestamp"] = time.time()
        episode_metrics["win_rate"] = win_rate
        episode_metrics["avg_reward"] = avg_reward
        self.training_log.append(episode_metrics)
        
        # Keep log manageable
        if len(self.training_log) > 10000:
            self.training_log = self.training_log[-5000:]
    
    def _evaluate_agent(self) -> Dict[str, Any]:
        """Evaluate agent performance."""
        print(f"Evaluating agent at episode {self.metrics.current_episode}...")
        
        # Run evaluation episodes
        evaluation_results = []
        
        for _ in range(self.training_config.evaluation_episodes):
            state = self.environment.reset()
            total_reward = 0.0
            
            while not self.environment.done:
                # Use agent in evaluation mode (no exploration)
                old_epsilon = self.agent.epsilon
                self.agent.epsilon = 0.0  # No exploration during evaluation
                
                action = self.agent.get_action(state.game_state)
                next_state, reward, done = self.environment.step(action)
                
                state = next_state
                total_reward += reward
                
                # Restore epsilon
                self.agent.epsilon = old_epsilon
            
            evaluation_results.append({
                "reward": total_reward,
                "win": total_reward > 0
            })
        
        # Calculate evaluation metrics
        win_rate = sum(1 for r in evaluation_results if r["win"]) / len(evaluation_results)
        avg_reward = sum(r["reward"] for r in evaluation_results) / len(evaluation_results)
        
        # Get agent statistics
        agent_stats = self.agent.get_comprehensive_stats()
        
        evaluation = {
            "episode": self.metrics.current_episode,
            "win_rate": win_rate,
            "avg_reward": avg_reward,
            "detection_risk": agent_stats["detection_assessment"]["current_risk"],
            "consistency": agent_stats.get("current_consistency", 0.0),
            "evasion_effectiveness": agent_stats["evasion_metrics"]["evasion_effectiveness"],
            "total_adaptations": agent_stats["adaptive_learning_metrics"]["total_adaptations"]
        }
        
        print(f"   Win rate: {win_rate:.3f}")
        print(f"   Avg reward: {avg_reward:.3f}")
        print(f"   Detection risk: {evaluation['detection_risk']:.3f}")
        
        return evaluation
    
    def _check_early_stopping(self) -> bool:
        """Check if early stopping criteria are met."""
        if not self.training_config.early_stopping_enabled:
            return False
        
        # Check if we've reached target performance
        if (len(self.metrics.win_rate_history) > 0 and
            self.metrics.win_rate_history[-1] >= self.training_config.target_win_rate and
            len(self.metrics.detection_risk_history) > 0 and
            self.metrics.detection_risk_history[-1] <= self.training_config.max_detection_risk):
            print("Target performance reached!")
            return True
        
        # Check patience
        if self.metrics.episodes_since_improvement >= self.training_config.patience:
            print(f"No improvement for {self.training_config.patience} episodes")
            return True
        
        return False
    
    def _save_checkpoint(self) -> None:
        """Save training checkpoint."""
        checkpoint_path = self.save_dir / f"checkpoint_episode_{self.metrics.current_episode}.pkl"
        
        checkpoint_data = {
            "episode": self.metrics.current_episode,
            "agent_state": self.agent.state_dict(),
            "metrics": self.metrics,
            "training_log": self.training_log[-1000:],  # Last 1000 episodes
            "configs": {
                "dqn": self.dqn_config,
                "evasion": self.evasion_config,
                "adaptation": self.adaptation_config,
                "training": self.training_config
            }
        }
        
        with open(checkpoint_path, 'wb') as f:
            pickle.dump(checkpoint_data, f)
        
        print(f"Checkpoint saved: {checkpoint_path}")
    
    def _save_final_model(self) -> None:
        """Save final trained model."""
        model_path = self.save_dir / "final_model.pkl"
        
        final_data = {
            "agent_state": self.agent.state_dict(),
            "final_metrics": self.metrics,
            "training_log": self.training_log,
            "agent_stats": self.agent.get_comprehensive_stats(),
            "configs": {
                "dqn": self.dqn_config,
                "evasion": self.evasion_config,
                "adaptation": self.adaptation_config,
                "training": self.training_config
            }
        }
        
        with open(model_path, 'wb') as f:
            pickle.dump(final_data, f)
        
        print(f"Final model saved: {model_path}")
    
    def _log_progress(self) -> None:
        """Log training progress."""
        episode = self.metrics.current_episode
        phase = self.metrics.current_phase.value
        
        if len(self.metrics.win_rate_history) > 0:
            win_rate = self.metrics.win_rate_history[-1]
            avg_reward = self.metrics.avg_reward_history[-1]
            detection_risk = self.metrics.detection_risk_history[-1]
            consistency = self.metrics.consistency_history[-1]
            
            print(f"Episode {episode:5d} | Phase: {phase:12s} | "
                  f"Win Rate: {win_rate:.3f} | Reward: {avg_reward:6.2f} | "
                  f"Risk: {detection_risk:.3f} | Consistency: {consistency:.3f}")
    
    def add_episode_callback(self, callback: Callable) -> None:
        """Add callback to be executed after each episode."""
        self.episode_callbacks.append(callback)
    
    def add_phase_callback(self, callback: Callable) -> None:
        """Add callback to be executed on phase transitions."""
        self.phase_callbacks.append(callback)
    
    def _execute_episode_callbacks(self, episode_metrics: Dict[str, Any]) -> None:
        """Execute episode callbacks."""
        for callback in self.episode_callbacks:
            try:
                callback(self, episode_metrics)
            except Exception as e:
                print(f"Episode callback error: {e}")
    
    def _execute_phase_callbacks(self, old_phase: TrainingPhase, new_phase: TrainingPhase) -> None:
        """Execute phase transition callbacks."""
        for callback in self.phase_callbacks:
            try:
                callback(self, old_phase, new_phase)
            except Exception as e:
                print(f"Phase callback error: {e}")
    
    def load_checkpoint(self, checkpoint_path: str) -> None:
        """Load training checkpoint."""
        with open(checkpoint_path, 'rb') as f:
            checkpoint_data = pickle.load(f)
        
        # Restore agent state
        if self.agent:
            self.agent.load_state_dict(checkpoint_data["agent_state"])
        
        # Restore metrics
        self.metrics = checkpoint_data["metrics"]
        self.training_log = checkpoint_data["training_log"]
        
        print(f"Checkpoint loaded from episode {self.metrics.current_episode}")
    
    def get_training_summary(self) -> Dict[str, Any]:
        """Get comprehensive training summary."""
        return {
            "training_config": self.training_config,
            "final_metrics": self.metrics,
            "agent_stats": self.agent.get_comprehensive_stats() if self.agent else None,
            "training_phases": [phase.value for phase in self.metrics.completed_phases],
            "performance_summary": {
                "best_win_rate": self.metrics.best_win_rate,
                "best_episode": self.metrics.best_episode,
                "final_win_rate": self.metrics.win_rate_history[-1] if self.metrics.win_rate_history else 0.0,
                "total_training_time": self.metrics.total_training_time,
                "episodes_per_second": self.metrics.episodes_per_second
            }
        }
