"""
Comprehensive test script for Phase 3: Reinforcement Learning with Evasion.

This script tests all Phase 3 components working together including:
- Evasive DQN Agent with all strategies
- Training Pipeline with phase transitions
- Persona switching integration
- Adaptive learning system
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, '.')

def test_evasive_agent_with_persona():
    """Test evasive agent with persona switcher."""
    
    print("Testing evasive agent with persona switcher...")
    
    try:
        from rl.dqn_agent import DQNConfig
        from rl.evasion_strategies import EvasionConfig
        from rl.adaptive_learning import AdaptationConfig
        from rl.evasive_dqn_agent import EvasiveDQNAgent
        from personas.persona_switcher import PersonaSwitcher, SwitchConfig
        from personas.cautious_persona import C<PERSON><PERSON><PERSON>erson<PERSON>
        from personas.aggressive_persona import AggressivePersona
        from core.game_logic import GameState
        from core.hand import Hand
        from core.card import Card, Suit, Rank
        
        # Create configurations
        dqn_config = DQNConfig(hidden_layers=[16, 8], batch_size=4, min_buffer_size=5)
        evasion_config = EvasionConfig(noise_intensity=0.2)
        adaptation_config = AdaptationConfig(min_adaptation_interval=5)
        
        # Create persona switcher
        switch_config = SwitchConfig(min_hands_per_persona=3, max_hands_per_persona=5)
        persona_switcher = PersonaSwitcher(switch_config)
        persona_switcher.add_persona('cautious', CautiousPersona())
        persona_switcher.add_persona('aggressive', AggressivePersona())
        
        print("   ✅ Persona switcher created with 2 personas")
        
        # Create evasive agent
        agent = EvasiveDQNAgent(
            'Comprehensive Test Agent',
            dqn_config,
            evasion_config,
            adaptation_config,
            persona_switcher=persona_switcher
        )
        
        print(f"   ✅ Evasive agent created: {agent.name}")
        
        # Create test game state
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        game_state.bet_amount = 10.0
        
        # Test action selection with evasion
        action = agent.get_action(game_state)
        print(f"   ✅ Action selected: {action.value}")
        
        # Test experience update
        agent.update_experience(10.0, game_state, True)
        print("   ✅ Experience updated")
        
        # Test comprehensive stats
        stats = agent.get_comprehensive_stats()
        print(f"   ✅ Comprehensive stats: {len(stats)} categories")
        
        # Test evasion history
        evasion_history = agent.get_evasion_history()
        print(f"   ✅ Evasion history: {len(evasion_history)} events")
        
        # Test bet amount
        bet_amount = agent.get_bet_amount(1.0, 100.0)
        print(f"   ✅ Bet amount: ${bet_amount}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Evasive agent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_pipeline():
    """Test training pipeline functionality."""
    
    print("\\nTesting training pipeline...")
    
    try:
        from rl.training_pipeline import TrainingPipeline, TrainingConfig, TrainingPhase
        from rl.dqn_agent import DQNConfig
        from rl.evasion_strategies import EvasionConfig
        from rl.adaptive_learning import AdaptationConfig
        from personas.persona_switcher import PersonaSwitcher, SwitchConfig
        from personas.cautious_persona import CautiousPersona
        
        # Create configurations for fast testing
        dqn_config = DQNConfig(hidden_layers=[8, 4], batch_size=2, min_buffer_size=3)
        evasion_config = EvasionConfig(noise_intensity=0.1)
        adaptation_config = AdaptationConfig(min_adaptation_interval=2)
        
        training_config = TrainingConfig(
            total_episodes=5,  # Very small for testing
            episodes_per_phase={
                TrainingPhase.EXPLORATION: 2,
                TrainingPhase.LEARNING: 2,
                TrainingPhase.OPTIMIZATION: 1,
                TrainingPhase.EVALUATION: 0
            },
            evaluation_frequency=3,
            evaluation_episodes=1,
            checkpoint_frequency=10,
            log_frequency=1,
            early_stopping_enabled=False
        )
        
        # Create persona switcher
        switch_config = SwitchConfig(min_hands_per_persona=1, max_hands_per_persona=2)
        persona_switcher = PersonaSwitcher(switch_config)
        persona_switcher.add_persona('cautious', CautiousPersona())
        
        print("   ✅ Training configurations created")
        
        # Create training pipeline
        pipeline = TrainingPipeline(
            dqn_config, evasion_config, adaptation_config, training_config, persona_switcher
        )
        
        print("   ✅ Training pipeline created")
        
        # Initialize training
        pipeline.initialize_training()
        print(f"   ✅ Training initialized: {pipeline.agent.name}")
        
        # Test phase transitions
        initial_phase = pipeline.metrics.current_phase
        pipeline.metrics.current_episode = 2
        pipeline._update_training_phase()
        new_phase = pipeline.metrics.current_phase
        print(f"   ✅ Phase transition: {initial_phase.value} → {new_phase.value}")
        
        # Test episode execution
        episode_metrics = pipeline._run_episode()
        print(f"   ✅ Episode executed: {episode_metrics['steps']} steps")
        
        # Test evaluation
        evaluation = pipeline._evaluate_agent()
        print(f"   ✅ Evaluation completed: {evaluation['win_rate']:.3f} win rate")
        
        # Test training summary
        summary = pipeline.get_training_summary()
        print(f"   ✅ Training summary: {len(summary)} sections")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Training pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_adaptive_learning_integration():
    """Test adaptive learning system integration."""
    
    print("\\nTesting adaptive learning integration...")
    
    try:
        from rl.adaptive_learning import AdaptiveLearningSystem, AdaptationConfig, AdaptationTrigger
        from rl.evasive_dqn_agent import EvasiveDQNAgent
        from rl.dqn_agent import DQNConfig
        from rl.evasion_strategies import EvasionConfig
        
        # Create configurations
        dqn_config = DQNConfig(hidden_layers=[8, 4], batch_size=2, min_buffer_size=3)
        evasion_config = EvasionConfig()
        adaptation_config = AdaptationConfig(min_adaptation_interval=1)
        
        # Create agent
        agent = EvasiveDQNAgent('Adaptive Test Agent', dqn_config, evasion_config, adaptation_config)
        
        # Test adaptive learning system
        adaptive_system = agent.adaptive_learning
        print(f"   ✅ Adaptive system: {len(adaptive_system.strategies)} strategies")
        
        # Test metrics calculation
        metrics = agent._calculate_current_metrics()
        print(f"   ✅ Current metrics: {len(metrics)} metrics calculated")
        
        # Test adaptation triggers
        # Simulate declining performance
        for i in range(10):
            agent.learning_performance_history.append({
                "training_step": i,
                "original_reward": 10.0 - i,  # Declining rewards
                "adjusted_reward": 10.0 - i,
                "detection_risk": 0.5
            })
        
        # Update metrics and trigger adaptations
        current_metrics = agent._calculate_current_metrics()
        adaptations = adaptive_system.update(agent, current_metrics)
        print(f"   ✅ Adaptations triggered: {len(adaptations)} adaptations")
        
        # Test strategy effectiveness
        effectiveness = adaptive_system.get_strategy_effectiveness()
        print(f"   ✅ Strategy effectiveness: {len(effectiveness)} strategies tracked")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Adaptive learning test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_evasion_strategies():
    """Test individual evasion strategies."""
    
    print("\\nTesting evasion strategies...")
    
    try:
        from rl.evasion_strategies import (
            EvasionConfig, BehavioralNoiseStrategy, TimingVariationStrategy,
            DecisionMaskingStrategy, PatternDisruptionStrategy
        )
        from rl.evasive_dqn_agent import EvasiveDQNAgent
        from rl.dqn_agent import DQNConfig
        from core.game_logic import GameState, GameAction
        from core.hand import Hand
        from core.card import Card, Suit, Rank
        
        # Create test agent
        dqn_config = DQNConfig(hidden_layers=[8, 4])
        evasion_config = EvasionConfig(noise_intensity=0.3)
        agent = EvasiveDQNAgent('Strategy Test Agent', dqn_config, evasion_config)
        
        # Create test game state
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        game_state.player_hands = [hand]
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Test behavioral noise strategy
        noise_strategy = BehavioralNoiseStrategy(evasion_config)
        action, metadata = noise_strategy.apply_evasion(agent, game_state, GameAction.HIT)
        print(f"   ✅ Behavioral noise: {metadata['technique'].value}")
        
        # Test timing variation strategy
        timing_strategy = TimingVariationStrategy(evasion_config)
        action, metadata = timing_strategy.apply_evasion(agent, game_state, GameAction.HIT)
        print(f"   ✅ Timing variation: {metadata['final_timing']:.3f}s")
        
        # Test decision masking strategy
        masking_strategy = DecisionMaskingStrategy(evasion_config)
        action, metadata = masking_strategy.apply_evasion(agent, game_state, GameAction.HIT)
        print(f"   ✅ Decision masking: {metadata['was_masked']}")
        
        # Test pattern disruption strategy
        disruption_strategy = PatternDisruptionStrategy(evasion_config)
        action, metadata = disruption_strategy.apply_evasion(agent, game_state, GameAction.HIT)
        print(f"   ✅ Pattern disruption: {metadata['was_disrupted']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Evasion strategies test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main comprehensive test function."""
    print("🔍 PHASE 3 COMPREHENSIVE INTEGRATION TEST")
    print("=" * 50)
    
    tests = [
        ("Evasive Agent with Persona", test_evasive_agent_with_persona),
        ("Training Pipeline", test_training_pipeline),
        ("Adaptive Learning Integration", test_adaptive_learning_integration),
        ("Evasion Strategies", test_evasion_strategies)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\\n🧪 {test_name}")
        print("-" * 30)
        
        if test_func():
            print(f"✅ {test_name} PASSED")
            passed_tests += 1
        else:
            print(f"❌ {test_name} FAILED")
    
    print("\\n" + "=" * 50)
    print(f"📊 TEST RESULTS: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL COMPREHENSIVE TESTS PASSED!")
        print("Phase 3: Reinforcement Learning with Evasion is fully functional!")
        return True
    else:
        print("❌ Some tests failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
