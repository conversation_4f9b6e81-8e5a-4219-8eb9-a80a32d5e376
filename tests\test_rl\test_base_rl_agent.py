"""
Unit tests for Base RL Agent.

Tests P3_T1 implementation: RL Agent Foundation.
"""

import unittest
import sys
import os
import numpy as np

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from rl.base_rl_agent import BaseRLAgent, RLConfig, Experience, ExperienceBuffer
from rl.rl_environment import RLState
from core.card import Card, Suit, Rank
from core.hand import Hand
from core.game_logic import Game<PERSON>tate, GameAction
from personas.cautious_persona import CautiousPersona


class TestRLAgent(BaseRLAgent):
    """Test implementation of BaseRLAgent for testing."""
    
    def _get_state_representation(self, game_state, hand_index=0):
        """Simple test state representation."""
        if not game_state.player_hands:
            return np.zeros(18)
        
        player_hand = game_state.player_hands[hand_index]
        state = np.zeros(18)
        state[0] = player_hand.get_value() / 21.0
        state[1] = float(player_hand.is_soft())
        
        if game_state.dealer_hand.cards:
            state[2] = game_state.dealer_hand.cards[0].get_value() / 11.0
        
        return state
    
    def _select_action(self, state, available_actions, training=True):
        """Random action selection for testing."""
        return available_actions[0] if available_actions else GameAction.HIT
    
    def _update_model(self, batch):
        """Mock model update."""
        return 0.5  # Mock loss
    
    def save_model(self, filepath):
        """Mock save."""
        pass
    
    def load_model(self, filepath):
        """Mock load."""
        pass


class TestBaseRLAgent(unittest.TestCase):
    """Test cases for the BaseRLAgent class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = RLConfig(
            learning_rate=0.001,
            epsilon_start=1.0,
            epsilon_end=0.01,
            buffer_size=1000,
            batch_size=32
        )
        
        self.agent = TestRLAgent("Test RL Agent", self.config)
        self.persona_agent = TestRLAgent("Persona RL Agent", self.config, CautiousPersona())
    
    def test_agent_initialization(self):
        """Test agent initialization."""
        self.assertEqual(self.agent.name, "Test RL Agent")
        self.assertEqual(self.agent.epsilon, 1.0)
        self.assertEqual(self.agent.episode, 0)
        self.assertEqual(self.agent.training_step, 0)
        self.assertIsInstance(self.agent.experience_buffer, ExperienceBuffer)
        self.assertEqual(len(self.agent.experience_buffer), 0)
    
    def test_persona_integration(self):
        """Test persona integration."""
        self.assertIsNotNone(self.persona_agent.persona)
        self.assertTrue(self.persona_agent.config.persona_integration)
        self.assertEqual(self.persona_agent.persona.config.name, "Cautious Basic Strategy")
    
    def test_experience_buffer(self):
        """Test experience buffer functionality."""
        buffer = ExperienceBuffer(capacity=5)
        
        # Test adding experiences
        for i in range(3):
            exp = Experience(
                state=np.array([i]),
                action=i,
                reward=float(i),
                next_state=np.array([i+1]),
                done=False
            )
            buffer.add(exp)
        
        self.assertEqual(len(buffer), 3)
        
        # Test sampling
        sample = buffer.sample(2)
        self.assertEqual(len(sample), 2)
        
        # Test capacity limit
        for i in range(5):
            exp = Experience(
                state=np.array([i+10]),
                action=i,
                reward=float(i),
                next_state=np.array([i+11]),
                done=False
            )
            buffer.add(exp)
        
        self.assertEqual(len(buffer), 5)  # Should not exceed capacity
    
    def test_action_conversion(self):
        """Test action to index conversion."""
        # Test action to index
        self.assertEqual(self.agent._action_to_index(GameAction.HIT), 0)
        self.assertEqual(self.agent._action_to_index(GameAction.STAND), 1)
        self.assertEqual(self.agent._action_to_index(GameAction.DOUBLE), 2)
        self.assertEqual(self.agent._action_to_index(GameAction.SPLIT), 3)
        
        # Test index to action
        self.assertEqual(self.agent._index_to_action(0), GameAction.HIT)
        self.assertEqual(self.agent._index_to_action(1), GameAction.STAND)
        self.assertEqual(self.agent._index_to_action(2), GameAction.DOUBLE)
        self.assertEqual(self.agent._index_to_action(3), GameAction.SPLIT)
    
    def test_state_representation(self):
        """Test state representation generation."""
        # Create test game state
        game_state = GameState()
        
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        
        # Get state representation
        state = self.agent._get_state_representation(game_state)
        
        # Should be numpy array
        self.assertIsInstance(state, np.ndarray)
        self.assertEqual(len(state), 18)
        
        # Check values
        self.assertAlmostEqual(state[0], 16.0 / 21.0, places=3)  # Player value
        self.assertEqual(state[1], 0.0)  # Not soft
        self.assertAlmostEqual(state[2], 7.0 / 11.0, places=3)  # Dealer upcard
    
    def test_get_action(self):
        """Test action selection."""
        # Create test game state
        game_state = GameState()
        
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Get action
        action = self.agent.get_action(game_state)
        
        # Should return valid action
        self.assertIsInstance(action, GameAction)
        
        # Should have stored state and action
        self.assertIsNotNone(self.agent.current_state)
        self.assertIsNotNone(self.agent.last_action)
    
    def test_experience_update(self):
        """Test experience buffer updates."""
        # Create initial state
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Get initial action
        self.agent.get_action(game_state)
        
        # Create next state
        next_game_state = GameState()
        next_hand = Hand()
        next_hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        next_hand.add_card(Card(Suit.SPADES, Rank.SIX))
        next_hand.add_card(Card(Suit.DIAMONDS, Rank.FIVE))
        next_game_state.player_hands = [next_hand]
        next_game_state.dealer_hand = dealer_hand
        
        # Update experience
        self.agent.update_experience(10.0, next_game_state, True)
        
        # Should have added experience
        self.assertEqual(len(self.agent.experience_buffer), 1)
        
        # Check experience
        experiences = self.agent.experience_buffer.sample(1)
        exp = experiences[0]
        self.assertEqual(exp.reward, 10.0)
        self.assertTrue(exp.done)
    
    def test_episode_management(self):
        """Test episode start and end."""
        initial_episode = self.agent.episode
        initial_reward = self.agent.total_reward
        
        # Start episode
        self.agent.start_episode()
        
        self.assertEqual(self.agent.episode, initial_episode + 1)
        self.assertEqual(self.agent.total_reward, 0.0)
        self.assertIsNone(self.agent.current_state)
        
        # Simulate some reward
        self.agent.total_reward = 15.0
        
        # End episode
        self.agent.end_episode()
        
        # Should have recorded episode reward
        self.assertIn(15.0, self.agent.training_metrics['episode_rewards'])
    
    def test_consistency_tracking(self):
        """Test decision consistency tracking."""
        # Make several decisions
        for i in range(10):
            decision_record = {
                'episode': 1,
                'step': i,
                'rl_action': 'H',
                'final_action': 'H',
                'is_persona_influenced': False,
                'timestamp': i
            }
            self.agent.decision_history.append(decision_record)
        
        # Calculate consistency
        consistency = self.agent._calculate_consistency()
        
        # Should be high consistency (all same action)
        self.assertGreater(consistency, 0.8)
    
    def test_training_metrics(self):
        """Test training metrics collection."""
        # Add some mock data
        self.agent.training_metrics['episode_rewards'] = [10, 15, 20]
        self.agent.training_metrics['loss_history'] = [0.5, 0.3, 0.2]
        self.agent.epsilon = 0.5
        self.agent.consistency_scores = [0.8, 0.9, 0.95]
        
        # Get metrics
        metrics = self.agent.get_training_metrics()
        
        # Check structure
        self.assertIn('episode', metrics)
        self.assertIn('training_step', metrics)
        self.assertIn('epsilon', metrics)
        self.assertIn('metrics', metrics)
        self.assertIn('detection_risk', metrics)
        
        # Check detection risk assessment
        risk = metrics['detection_risk']
        self.assertIn('risk_level', risk)
        self.assertIn('consistency', risk)
        self.assertIn('recommendations', risk)
    
    def test_detection_risk_assessment(self):
        """Test detection risk assessment."""
        # Test high consistency (high risk)
        self.agent.consistency_scores = [0.96, 0.97, 0.98]
        risk = self.agent._assess_detection_risk()
        self.assertEqual(risk['risk_level'], 'HIGH')
        
        # Test medium consistency (medium risk)
        self.agent.consistency_scores = [0.85, 0.86, 0.87]
        risk = self.agent._assess_detection_risk()
        self.assertEqual(risk['risk_level'], 'MEDIUM')
        
        # Test low consistency (low risk)
        self.agent.consistency_scores = [0.5, 0.6, 0.7]
        risk = self.agent._assess_detection_risk()
        self.assertEqual(risk['risk_level'], 'LOW')
    
    def test_persona_context_tracking(self):
        """Test persona context tracking."""
        # Test with persona agent
        context = self.persona_agent._get_persona_context()
        
        self.assertIn('persona_name', context)
        self.assertIn('current_context', context)
        self.assertIn('current_accuracy', context)
        self.assertEqual(context['persona_name'], "Cautious Basic Strategy")
    
    def test_config_validation(self):
        """Test configuration validation."""
        # Test valid config
        config = RLConfig(
            learning_rate=0.001,
            discount_factor=0.99,
            epsilon_start=1.0,
            epsilon_end=0.01
        )
        
        self.assertEqual(config.learning_rate, 0.001)
        self.assertEqual(config.discount_factor, 0.99)
        self.assertTrue(config.persona_integration)
        
        # Test default values
        self.assertEqual(config.buffer_size, 10000)
        self.assertEqual(config.batch_size, 32)


if __name__ == '__main__':
    unittest.main()
