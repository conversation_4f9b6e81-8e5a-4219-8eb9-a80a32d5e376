"""
Phase 2 Integration Tests

Comprehensive tests for Phase 2 Human Persona Simulation implementation.
Tests all components working together: P2_T1, P2_T2, and P2_T3.
"""

import unittest
import sys
import os
import tempfile
import shutil

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from personas import (
    CautiousPersona, AggressivePersona, IntuitivePersona,
    HumanPersonaAgent, PersonaSwitcherAgent, SwitchConfig
)
from utils.simulation import BlackjackSimulator, SimulationConfig
from utils.progress_manager import ProgressManager
from core.card import Card, Suit, Rank
from core.hand import Hand
from core.game_logic import GameState


class TestPhase2Integration(unittest.TestCase):
    """Integration tests for Phase 2 implementation."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create temporary directory for test data
        self.test_dir = tempfile.mkdtemp()
        self.progress_manager = ProgressManager(self.test_dir)
        
        # Create test agents
        self.cautious_agent = HumanPersonaAgent("Cautious Test", CautiousPersona())
        self.aggressive_agent = HumanPersonaAgent("Aggressive Test", AggressivePersona())
        self.intuitive_agent = HumanPersonaAgent("Intuitive Test", IntuitivePersona())
        
        switch_config = SwitchConfig(
            min_hands_per_persona=10,
            max_hands_per_persona=25
        )
        self.switcher_agent = PersonaSwitcherAgent("Switcher Test", switch_config)
    
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_complete_persona_workflow(self):
        """Test complete workflow with all persona types."""
        # Test simulation configuration
        sim_config = SimulationConfig(
            num_hands=50,
            verbose=False,
            random_seed=42
        )
        
        simulator = BlackjackSimulator(sim_config)
        
        # Test each persona type
        agents = [
            self.cautious_agent,
            self.aggressive_agent,
            self.intuitive_agent,
            self.switcher_agent
        ]
        
        results = {}
        
        for agent in agents:
            # Run simulation
            result = simulator.run_simulation(agent)
            results[agent.name] = result
            
            # Verify basic functionality
            self.assertIn('agent_stats', result)
            self.assertIn('simulation_stats', result)
            
            agent_stats = result['agent_stats']
            self.assertEqual(agent_stats['hands_played'], 50)
            self.assertGreaterEqual(agent_stats['win_rate'], 0.0)
            self.assertLessEqual(agent_stats['win_rate'], 1.0)
            
            # Verify persona-specific stats
            if hasattr(agent, 'persona'):
                self.assertIn('behavioral_stats', agent_stats)
                self.assertIn('persona_metrics', agent_stats)
                
                behavioral = agent_stats['behavioral_stats']
                self.assertIn('accuracy', behavioral)
                self.assertIn('avg_decision_time', behavioral)
            
            # Verify switcher-specific stats
            if hasattr(agent, 'switcher'):
                self.assertIn('switching_stats', agent_stats)
                self.assertIn('current_persona', agent_stats)
    
    def test_persona_behavioral_differences(self):
        """Test that personas exhibit measurably different behaviors."""
        # Create test scenario
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Collect decisions from each persona
        personas = [
            self.cautious_agent.persona,
            self.aggressive_agent.persona,
            self.intuitive_agent.persona
        ]
        
        decision_patterns = {}
        
        for persona in personas:
            decisions = []
            for _ in range(20):
                action = persona.get_action(game_state, 0)
                decisions.append(action.value)
            decision_patterns[persona.config.name] = decisions
        
        # Verify different patterns
        cautious_decisions = decision_patterns["Cautious Basic Strategy"]
        aggressive_decisions = decision_patterns["Slightly Aggressive"]
        intuitive_decisions = decision_patterns["Intuitive/Emotional"]
        
        # Should have some variation between personas
        # (Note: Due to randomness, we can't guarantee specific differences,
        # but we can check that the framework is working)
        self.assertTrue(len(set(cautious_decisions)) >= 1)
        self.assertTrue(len(set(aggressive_decisions)) >= 1)
        self.assertTrue(len(set(intuitive_decisions)) >= 1)
    
    def test_persona_accuracy_levels(self):
        """Test that personas maintain their configured accuracy levels."""
        # Run longer simulations to get statistical significance
        sim_config = SimulationConfig(
            num_hands=100,
            verbose=False,
            random_seed=123
        )
        
        simulator = BlackjackSimulator(sim_config)
        
        # Test individual personas
        personas_to_test = [
            (self.cautious_agent, 0.95),
            (self.aggressive_agent, 0.90),
            (self.intuitive_agent, 0.70)
        ]
        
        for agent, expected_accuracy in personas_to_test:
            result = simulator.run_simulation(agent)
            behavioral_stats = result['agent_stats'].get('behavioral_stats', {})
            
            if behavioral_stats:
                actual_accuracy = behavioral_stats.get('accuracy', 0)
                
                # Allow for some variance due to randomness and small sample size
                # The accuracy should be within a reasonable range of the expected value
                tolerance = 0.15  # 15% tolerance
                self.assertGreater(actual_accuracy, expected_accuracy - tolerance)
                self.assertLess(actual_accuracy, expected_accuracy + tolerance)
    
    def test_persona_switcher_functionality(self):
        """Test persona switcher detection avoidance functionality."""
        # Run simulation long enough to trigger switches
        sim_config = SimulationConfig(
            num_hands=100,
            verbose=False,
            random_seed=456
        )
        
        simulator = BlackjackSimulator(sim_config)
        result = simulator.run_simulation(self.switcher_agent)
        
        # Verify switcher functionality
        agent_stats = result['agent_stats']
        self.assertIn('switching_stats', agent_stats)
        self.assertIn('current_persona', agent_stats)
        
        switching_stats = agent_stats['switching_stats']
        
        # Should have some persona usage data
        self.assertIn('persona_usage', switching_stats)
        persona_usage = switching_stats['persona_usage']
        self.assertGreater(len(persona_usage), 0)
        
        # Total hands should match simulation
        total_hands = sum(persona_usage.values())
        self.assertEqual(total_hands, 100)
        
        # Get detection risk assessment
        risk_assessment = self.switcher_agent.get_detection_risk_assessment()
        self.assertIn('overall_risk', risk_assessment)
        self.assertIn('consistency_risk', risk_assessment)
        self.assertIn('recommendations', risk_assessment)
    
    def test_progress_manager_persona_integration(self):
        """Test progress manager integration with persona training sessions."""
        # Run a short simulation
        sim_config = SimulationConfig(
            num_hands=25,
            verbose=False,
            random_seed=789
        )
        
        simulator = BlackjackSimulator(sim_config)
        result = simulator.run_simulation(self.cautious_agent)
        
        # Save persona training session
        session_path = self.progress_manager.save_persona_training_session(
            "test_session",
            self.cautious_agent,
            result
        )
        
        # Verify session was saved
        self.assertTrue(os.path.exists(session_path))
        
        # Load and verify session data
        session_data = self.progress_manager.load_persona_training_session(session_path)
        
        self.assertEqual(session_data['session_name'], "test_session")
        self.assertEqual(session_data['agent_type'], "HumanPersonaAgent")
        self.assertIn('persona_config', session_data)
        self.assertIn('behavioral_stats', session_data)
        
        # Test training history
        history = self.progress_manager.get_persona_training_history()
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]['session_name'], "test_session")
    
    def test_persona_switcher_session_saving(self):
        """Test saving persona switcher sessions."""
        # Run simulation with switcher
        sim_config = SimulationConfig(
            num_hands=30,
            verbose=False,
            random_seed=101112
        )
        
        simulator = BlackjackSimulator(sim_config)
        result = simulator.run_simulation(self.switcher_agent)
        
        # Save switcher session
        session_path = self.progress_manager.save_persona_training_session(
            "switcher_test",
            self.switcher_agent,
            result
        )
        
        # Load and verify switcher-specific data
        session_data = self.progress_manager.load_persona_training_session(session_path)
        
        self.assertIn('switching_stats', session_data)
        self.assertIn('switch_history', session_data)
        self.assertIn('detection_risk', session_data)
    
    def test_persona_timing_simulation(self):
        """Test that persona timing simulation works correctly."""
        # Test timing for each persona
        personas = [
            self.cautious_agent.persona,
            self.aggressive_agent.persona,
            self.intuitive_agent.persona
        ]
        
        # Create test hand
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        for persona in personas:
            # Test timing simulation
            timing = persona._simulate_decision_timing(hand, 10)
            
            # Should be within configured bounds
            pattern = persona.config.decision_pattern
            self.assertGreaterEqual(timing, pattern.min_decision_time)
            self.assertLessEqual(timing, pattern.max_decision_time)
            self.assertIsInstance(timing, float)
    
    def test_error_injection_mechanisms(self):
        """Test that error injection mechanisms work correctly."""
        # Test each persona's error generation
        personas = [
            self.cautious_agent.persona,
            self.aggressive_agent.persona,
            self.intuitive_agent.persona
        ]
        
        # Create test scenario
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        from utils.basic_strategy_charts import BasicStrategyAction
        
        for persona in personas:
            # Test error decision generation
            optimal_action = BasicStrategyAction.STAND
            
            # Generate multiple error decisions
            error_actions = []
            for _ in range(10):
                error_action = persona._make_error_decision(optimal_action, hand, 10, None)
                error_actions.append(error_action)
            
            # Should generate some decisions (may include optimal action)
            self.assertEqual(len(error_actions), 10)
            self.assertTrue(all(isinstance(action, BasicStrategyAction) for action in error_actions))
    
    def test_context_sensitivity(self):
        """Test that personas respond to context changes."""
        persona = self.cautious_agent.persona
        
        # Test different contexts
        from personas.base_persona import DecisionContext
        
        # Set up losing streak (should trigger pressure context)
        for _ in range(4):
            persona.update_result("loss")
        
        # Create test game state
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Make decision (should update context)
        persona.get_action(game_state, 0)
        
        # Should be in pressure context
        self.assertEqual(persona.current_context, DecisionContext.PRESSURE)
        
        # Test winning streak
        persona.reset_session()
        for _ in range(5):
            persona.update_result("win")
        
        persona.get_action(game_state, 0)
        self.assertEqual(persona.current_context, DecisionContext.CONFIDENT)
    
    def test_comprehensive_statistics_tracking(self):
        """Test comprehensive statistics tracking across all components."""
        # Run simulation with all agent types
        sim_config = SimulationConfig(
            num_hands=50,
            verbose=False,
            random_seed=131415
        )
        
        simulator = BlackjackSimulator(sim_config)
        
        agents = [
            self.cautious_agent,
            self.aggressive_agent,
            self.intuitive_agent,
            self.switcher_agent
        ]
        
        for agent in agents:
            result = simulator.run_simulation(agent)
            stats = result['agent_stats']
            
            # Verify comprehensive stats are available
            required_stats = ['hands_played', 'win_rate', 'total_winnings']
            for stat in required_stats:
                self.assertIn(stat, stats)
            
            # Verify persona-specific stats
            if hasattr(agent, 'persona'):
                self.assertIn('behavioral_stats', stats)
                behavioral = stats['behavioral_stats']
                
                required_behavioral = ['accuracy', 'avg_decision_time', 'error_count']
                for stat in required_behavioral:
                    self.assertIn(stat, behavioral)
            
            # Verify switcher-specific stats
            if hasattr(agent, 'switcher'):
                self.assertIn('switching_stats', stats)
                switching = stats['switching_stats']
                
                required_switching = ['current_persona', 'persona_usage']
                for stat in required_switching:
                    self.assertIn(stat, switching)


if __name__ == '__main__':
    unittest.main()
