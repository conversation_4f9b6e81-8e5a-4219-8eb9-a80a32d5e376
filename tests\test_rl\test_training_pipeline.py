"""
Unit tests for Training Pipeline.

Tests P3_T4 implementation: Training Pipeline.
"""

import unittest
import sys
import os
import tempfile
import shutil
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from rl.training_pipeline import (
    TrainingPipeline, TrainingConfig, TrainingPhase, TrainingMetrics
)
from rl.dqn_agent import DQNConfig
from rl.evasion_strategies import EvasionConfig
from rl.adaptive_learning import AdaptationConfig
from personas.persona_switcher import PersonaSwitcher, SwitchConfig
from personas.cautious_persona import CautiousPersona


class TestTrainingConfig(unittest.TestCase):
    """Test cases for TrainingConfig."""
    
    def test_config_creation(self):
        """Test training config creation with defaults."""
        config = TrainingConfig()
        
        # Check default values
        self.assertEqual(config.total_episodes, 10000)
        self.assertIn(TrainingPhase.EXPLORATION, config.episodes_per_phase)
        self.assertEqual(config.evaluation_frequency, 500)
        self.assertEqual(config.target_win_rate, 0.45)
        self.assertTrue(config.early_stopping_enabled)
    
    def test_config_customization(self):
        """Test custom training config."""
        custom_episodes = {
            TrainingPhase.EXPLORATION: 500,
            TrainingPhase.LEARNING: 1000,
            TrainingPhase.OPTIMIZATION: 300,
            TrainingPhase.EVALUATION: 200
        }
        
        config = TrainingConfig(
            total_episodes=2000,
            episodes_per_phase=custom_episodes,
            target_win_rate=0.5,
            early_stopping_enabled=False
        )
        
        self.assertEqual(config.total_episodes, 2000)
        self.assertEqual(config.episodes_per_phase, custom_episodes)
        self.assertEqual(config.target_win_rate, 0.5)
        self.assertFalse(config.early_stopping_enabled)


class TestTrainingMetrics(unittest.TestCase):
    """Test cases for TrainingMetrics."""
    
    def test_metrics_initialization(self):
        """Test metrics initialization."""
        metrics = TrainingMetrics()
        
        # Check initial values
        self.assertEqual(metrics.current_episode, 0)
        self.assertEqual(metrics.current_phase, TrainingPhase.INITIALIZATION)
        self.assertEqual(len(metrics.win_rate_history), 0)
        self.assertEqual(metrics.best_win_rate, 0.0)
        self.assertEqual(len(metrics.completed_phases), 0)
    
    def test_metrics_updates(self):
        """Test metrics updates."""
        metrics = TrainingMetrics()
        
        # Update metrics
        metrics.current_episode = 100
        metrics.current_phase = TrainingPhase.LEARNING
        metrics.win_rate_history.append(0.4)
        metrics.best_win_rate = 0.4
        metrics.completed_phases.append(TrainingPhase.EXPLORATION)
        
        # Check updates
        self.assertEqual(metrics.current_episode, 100)
        self.assertEqual(metrics.current_phase, TrainingPhase.LEARNING)
        self.assertEqual(len(metrics.win_rate_history), 1)
        self.assertEqual(metrics.best_win_rate, 0.4)
        self.assertIn(TrainingPhase.EXPLORATION, metrics.completed_phases)


class TestTrainingPipeline(unittest.TestCase):
    """Test cases for TrainingPipeline."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create temporary directory for testing
        self.temp_dir = tempfile.mkdtemp()
        
        # Create configurations
        self.dqn_config = DQNConfig(
            hidden_layers=[16, 8],
            batch_size=4,
            min_buffer_size=5
        )
        
        self.evasion_config = EvasionConfig()
        self.adaptation_config = AdaptationConfig()
        
        self.training_config = TrainingConfig(
            total_episodes=50,  # Small for testing
            episodes_per_phase={
                TrainingPhase.EXPLORATION: 10,
                TrainingPhase.LEARNING: 20,
                TrainingPhase.OPTIMIZATION: 15,
                TrainingPhase.EVALUATION: 5
            },
            evaluation_frequency=10,
            evaluation_episodes=5,
            checkpoint_frequency=20,
            save_directory=self.temp_dir,
            log_frequency=5
        )
        
        # Create persona switcher
        switch_config = SwitchConfig(min_hands_per_persona=5, max_hands_per_persona=10)
        self.persona_switcher = PersonaSwitcher(switch_config)
        self.persona_switcher.add_persona("cautious", CautiousPersona())
    
    def tearDown(self):
        """Clean up test fixtures."""
        # Remove temporary directory
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_pipeline_initialization(self):
        """Test pipeline initialization."""
        pipeline = TrainingPipeline(
            self.dqn_config,
            self.evasion_config,
            self.adaptation_config,
            self.training_config,
            self.persona_switcher
        )
        
        # Check initialization
        self.assertEqual(pipeline.dqn_config, self.dqn_config)
        self.assertEqual(pipeline.evasion_config, self.evasion_config)
        self.assertEqual(pipeline.training_config, self.training_config)
        self.assertEqual(pipeline.persona_switcher, self.persona_switcher)
        self.assertIsNone(pipeline.agent)  # Not initialized yet
        self.assertFalse(pipeline.is_training)
    
    def test_training_initialization(self):
        """Test training component initialization."""
        pipeline = TrainingPipeline(
            self.dqn_config,
            self.evasion_config,
            self.adaptation_config,
            self.training_config,
            self.persona_switcher
        )
        
        # Initialize training
        pipeline.initialize_training()
        
        # Check components
        self.assertIsNotNone(pipeline.agent)
        self.assertIsNotNone(pipeline.environment)
        self.assertIsNotNone(pipeline.simulator)
        self.assertEqual(pipeline.metrics.current_phase, TrainingPhase.EXPLORATION)
    
    def test_phase_transitions(self):
        """Test training phase transitions."""
        pipeline = TrainingPipeline(
            self.dqn_config,
            self.evasion_config,
            self.adaptation_config,
            self.training_config,
            self.persona_switcher
        )
        
        pipeline.initialize_training()
        
        # Test phase transitions
        initial_phase = pipeline.metrics.current_phase
        
        # Simulate episode progression
        pipeline.metrics.current_episode = 10  # Should trigger learning phase
        pipeline._update_training_phase()
        
        self.assertEqual(pipeline.metrics.current_phase, TrainingPhase.LEARNING)
        self.assertIn(initial_phase, pipeline.metrics.completed_phases)
        
        # Continue to optimization phase
        pipeline.metrics.current_episode = 30  # Should trigger optimization phase
        pipeline._update_training_phase()
        
        self.assertEqual(pipeline.metrics.current_phase, TrainingPhase.OPTIMIZATION)
    
    def test_agent_adjustment_for_phase(self):
        """Test agent parameter adjustment for different phases."""
        pipeline = TrainingPipeline(
            self.dqn_config,
            self.evasion_config,
            self.adaptation_config,
            self.training_config,
            self.persona_switcher
        )
        
        pipeline.initialize_training()
        
        # Test exploration phase
        pipeline._adjust_agent_for_phase(TrainingPhase.EXPLORATION)
        self.assertEqual(pipeline.agent.epsilon, 0.9)
        
        # Test learning phase
        pipeline._adjust_agent_for_phase(TrainingPhase.LEARNING)
        self.assertEqual(pipeline.agent.epsilon, 0.5)
        
        # Test optimization phase
        pipeline._adjust_agent_for_phase(TrainingPhase.OPTIMIZATION)
        self.assertEqual(pipeline.agent.epsilon, 0.1)
        
        # Test evaluation phase
        pipeline._adjust_agent_for_phase(TrainingPhase.EVALUATION)
        self.assertEqual(pipeline.agent.epsilon, 0.05)
    
    def test_episode_execution(self):
        """Test single episode execution."""
        pipeline = TrainingPipeline(
            self.dqn_config,
            self.evasion_config,
            self.adaptation_config,
            self.training_config,
            self.persona_switcher
        )
        
        pipeline.initialize_training()
        
        # Run single episode
        episode_metrics = pipeline._run_episode()
        
        # Check episode metrics
        self.assertIn("episode", episode_metrics)
        self.assertIn("total_reward", episode_metrics)
        self.assertIn("steps", episode_metrics)
        self.assertIn("actions", episode_metrics)
        self.assertIn("win", episode_metrics)
        self.assertIn("detection_risk", episode_metrics)
        self.assertIn("consistency", episode_metrics)
        
        # Check types
        self.assertIsInstance(episode_metrics["total_reward"], (int, float))
        self.assertIsInstance(episode_metrics["steps"], int)
        self.assertIsInstance(episode_metrics["actions"], list)
        self.assertIsInstance(episode_metrics["win"], bool)
    
    def test_metrics_update(self):
        """Test metrics update with episode results."""
        pipeline = TrainingPipeline(
            self.dqn_config,
            self.evasion_config,
            self.adaptation_config,
            self.training_config,
            self.persona_switcher
        )
        
        pipeline.initialize_training()
        
        # Create mock episode metrics
        episode_metrics = {
            "episode": 0,
            "total_reward": 10.0,
            "win": True,
            "detection_risk": 0.3,
            "consistency": 0.8
        }
        
        # Update metrics
        pipeline._update_metrics(episode_metrics)
        
        # Check updates
        self.assertEqual(len(pipeline.metrics.win_rate_history), 1)
        self.assertEqual(len(pipeline.metrics.avg_reward_history), 1)
        self.assertEqual(len(pipeline.metrics.detection_risk_history), 1)
        self.assertEqual(len(pipeline.metrics.consistency_history), 1)
        self.assertEqual(len(pipeline.training_log), 1)
    
    def test_evaluation(self):
        """Test agent evaluation."""
        pipeline = TrainingPipeline(
            self.dqn_config,
            self.evasion_config,
            self.adaptation_config,
            self.training_config,
            self.persona_switcher
        )
        
        pipeline.initialize_training()
        
        # Run evaluation
        evaluation = pipeline._evaluate_agent()
        
        # Check evaluation results
        self.assertIn("episode", evaluation)
        self.assertIn("win_rate", evaluation)
        self.assertIn("avg_reward", evaluation)
        self.assertIn("detection_risk", evaluation)
        self.assertIn("consistency", evaluation)
        self.assertIn("evasion_effectiveness", evaluation)
        
        # Check value ranges
        self.assertGreaterEqual(evaluation["win_rate"], 0.0)
        self.assertLessEqual(evaluation["win_rate"], 1.0)
        self.assertGreaterEqual(evaluation["detection_risk"], 0.0)
        self.assertLessEqual(evaluation["detection_risk"], 1.0)
    
    def test_early_stopping_conditions(self):
        """Test early stopping conditions."""
        pipeline = TrainingPipeline(
            self.dqn_config,
            self.evasion_config,
            self.adaptation_config,
            self.training_config,
            self.persona_switcher
        )
        
        pipeline.initialize_training()
        
        # Test target performance reached
        pipeline.metrics.win_rate_history = [0.5]  # Above target
        pipeline.metrics.detection_risk_history = [0.2]  # Below max
        
        self.assertTrue(pipeline._check_early_stopping())
        
        # Test patience exceeded
        pipeline.metrics.win_rate_history = [0.3]  # Below target
        pipeline.metrics.episodes_since_improvement = 2000  # Exceeds patience
        
        self.assertTrue(pipeline._check_early_stopping())
        
        # Test no early stopping
        pipeline.metrics.win_rate_history = [0.3]
        pipeline.metrics.episodes_since_improvement = 100  # Within patience
        
        self.assertFalse(pipeline._check_early_stopping())
    
    def test_callbacks(self):
        """Test callback system."""
        pipeline = TrainingPipeline(
            self.dqn_config,
            self.evasion_config,
            self.adaptation_config,
            self.training_config,
            self.persona_switcher
        )
        
        # Test callback tracking
        episode_callback_called = False
        phase_callback_called = False
        
        def test_episode_callback(pipeline, episode_metrics):
            nonlocal episode_callback_called
            episode_callback_called = True
        
        def test_phase_callback(pipeline, old_phase, new_phase):
            nonlocal phase_callback_called
            phase_callback_called = True
        
        # Add callbacks
        pipeline.add_episode_callback(test_episode_callback)
        pipeline.add_phase_callback(test_phase_callback)
        
        # Execute callbacks
        pipeline._execute_episode_callbacks({"episode": 1})
        pipeline._execute_phase_callbacks(TrainingPhase.EXPLORATION, TrainingPhase.LEARNING)
        
        # Check callbacks were called
        self.assertTrue(episode_callback_called)
        self.assertTrue(phase_callback_called)
    
    def test_training_summary(self):
        """Test training summary generation."""
        pipeline = TrainingPipeline(
            self.dqn_config,
            self.evasion_config,
            self.adaptation_config,
            self.training_config,
            self.persona_switcher
        )
        
        pipeline.initialize_training()
        
        # Add some mock data
        pipeline.metrics.best_win_rate = 0.45
        pipeline.metrics.best_episode = 100
        pipeline.metrics.win_rate_history = [0.3, 0.4, 0.45]
        pipeline.metrics.total_training_time = 120.5
        pipeline.metrics.episodes_per_second = 2.5
        
        # Get training summary
        summary = pipeline.get_training_summary()
        
        # Check summary structure
        self.assertIn("training_config", summary)
        self.assertIn("final_metrics", summary)
        self.assertIn("agent_stats", summary)
        self.assertIn("performance_summary", summary)
        
        # Check performance summary
        performance = summary["performance_summary"]
        self.assertEqual(performance["best_win_rate"], 0.45)
        self.assertEqual(performance["best_episode"], 100)
        self.assertEqual(performance["final_win_rate"], 0.45)
        self.assertEqual(performance["total_training_time"], 120.5)
        self.assertEqual(performance["episodes_per_second"], 2.5)


class TestTrainingIntegration(unittest.TestCase):
    """Integration tests for complete training pipeline."""
    
    def setUp(self):
        """Set up integration test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        
        # Minimal configurations for fast testing
        self.dqn_config = DQNConfig(
            hidden_layers=[8, 4],
            batch_size=2,
            min_buffer_size=3,
            buffer_size=10
        )
        
        self.evasion_config = EvasionConfig(noise_intensity=0.1)
        self.adaptation_config = AdaptationConfig(min_adaptation_interval=5)
        
        self.training_config = TrainingConfig(
            total_episodes=10,  # Very small for integration test
            episodes_per_phase={
                TrainingPhase.EXPLORATION: 3,
                TrainingPhase.LEARNING: 4,
                TrainingPhase.OPTIMIZATION: 2,
                TrainingPhase.EVALUATION: 1
            },
            evaluation_frequency=5,
            evaluation_episodes=2,
            checkpoint_frequency=5,
            save_directory=self.temp_dir,
            log_frequency=2,
            early_stopping_enabled=False  # Disable for test
        )
    
    def tearDown(self):
        """Clean up integration test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_complete_training_run(self):
        """Test complete training run with all components."""
        # Create pipeline
        pipeline = TrainingPipeline(
            self.dqn_config,
            self.evasion_config,
            self.adaptation_config,
            self.training_config
        )
        
        # Run training
        final_metrics = pipeline.train()
        
        # Check that training completed
        self.assertIsInstance(final_metrics, TrainingMetrics)
        self.assertEqual(final_metrics.current_episode, self.training_config.total_episodes)
        self.assertGreater(final_metrics.total_training_time, 0)
        
        # Check that phases were completed
        self.assertGreater(len(final_metrics.completed_phases), 0)
        
        # Check that metrics were recorded
        self.assertGreater(len(final_metrics.win_rate_history), 0)
        self.assertGreater(len(final_metrics.avg_reward_history), 0)
        
        # Check that agent was trained
        self.assertIsNotNone(pipeline.agent)
        self.assertGreater(pipeline.agent.training_step, 0)


if __name__ == '__main__':
    unittest.main()
