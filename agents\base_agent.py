"""
Base agent class for BlackJack Bot ML.

This module defines the abstract base class that all blackjack agents must implement,
providing a consistent interface for different playing strategies.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from core.game_logic import GameAction, GameState


class BaseAgent(ABC):
    """
    Abstract base class for all blackjack agents.
    
    Defines the interface that all agents must implement for consistent
    interaction with the game engine and simulation framework.
    """
    
    def __init__(self, name: str):
        """
        Initialize the base agent.
        
        Args:
            name: Human-readable name for this agent
        """
        self.name = name
        self.stats = {
            'hands_played': 0,
            'hands_won': 0,
            'hands_lost': 0,
            'hands_pushed': 0,
            'blackjacks': 0,
            'busts': 0,
            'total_bet': 0.0,
            'total_winnings': 0.0
        }
    
    @abstractmethod
    def get_action(self, game_state: GameState, hand_index: int = 0) -> GameAction:
        """
        Get the action to take for the current game state.
        
        Args:
            game_state: Current state of the blackjack game
            hand_index: Index of the hand to make decision for (for splits)
            
        Returns:
            The action to take
        """
        pass
    
    @abstractmethod
    def get_bet_amount(self, min_bet: float = 1.0, max_bet: float = 100.0) -> float:
        """
        Get the bet amount for the next hand.
        
        Args:
            min_bet: Minimum allowed bet
            max_bet: Maximum allowed bet
            
        Returns:
            The bet amount
        """
        pass
    
    def update_stats(self, game_result: str, bet_amount: float, winnings: float) -> None:
        """
        Update agent statistics after a hand.
        
        Args:
            game_result: Result of the hand ('win', 'loss', 'push', 'blackjack', 'bust')
            bet_amount: Amount bet on this hand
            winnings: Amount won/lost (negative for losses)
        """
        self.stats['hands_played'] += 1
        self.stats['total_bet'] += bet_amount
        self.stats['total_winnings'] += winnings
        
        if game_result == 'win':
            self.stats['hands_won'] += 1
        elif game_result == 'loss':
            self.stats['hands_lost'] += 1
        elif game_result == 'push':
            self.stats['hands_pushed'] += 1
        elif game_result == 'blackjack':
            self.stats['hands_won'] += 1
            self.stats['blackjacks'] += 1
        elif game_result == 'bust':
            self.stats['hands_lost'] += 1
            self.stats['busts'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get current agent statistics.
        
        Returns:
            Dictionary containing agent statistics
        """
        stats = self.stats.copy()
        
        # Calculate derived statistics
        if stats['hands_played'] > 0:
            stats['win_rate'] = stats['hands_won'] / stats['hands_played']
            stats['loss_rate'] = stats['hands_lost'] / stats['hands_played']
            stats['push_rate'] = stats['hands_pushed'] / stats['hands_played']
            stats['blackjack_rate'] = stats['blackjacks'] / stats['hands_played']
            stats['bust_rate'] = stats['busts'] / stats['hands_played']
        else:
            stats['win_rate'] = 0.0
            stats['loss_rate'] = 0.0
            stats['push_rate'] = 0.0
            stats['blackjack_rate'] = 0.0
            stats['bust_rate'] = 0.0
        
        if stats['total_bet'] > 0:
            stats['return_on_investment'] = stats['total_winnings'] / stats['total_bet']
        else:
            stats['return_on_investment'] = 0.0
        
        return stats
    
    def reset_stats(self) -> None:
        """Reset all agent statistics."""
        self.stats = {
            'hands_played': 0,
            'hands_won': 0,
            'hands_lost': 0,
            'hands_pushed': 0,
            'blackjacks': 0,
            'busts': 0,
            'total_bet': 0.0,
            'total_winnings': 0.0
        }
    
    def __str__(self) -> str:
        """String representation of the agent."""
        return f"{self.__class__.__name__}(name='{self.name}')"
    
    def __repr__(self) -> str:
        """Detailed string representation for debugging."""
        stats = self.get_stats()
        return (f"{self.__class__.__name__}(name='{self.name}', "
                f"hands_played={stats['hands_played']}, "
                f"win_rate={stats['win_rate']:.3f})")
