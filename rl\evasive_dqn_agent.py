"""
Evasive DQN Agent for BlackJack Bot ML.

This module implements a DQN agent with integrated evasion strategies
that can learn optimal strategies while avoiding detection.
"""

import time
import numpy as np
from typing import Dict, List, Any, Optional, Tuple

from .dqn_agent import DQNAgent, DQNConfig
from .evasion_manager import Eva<PERSON><PERSON>anager, EvasionConfig
from .adaptive_learning import AdaptiveLearningSystem, AdaptationConfig
from .base_rl_agent import BaseRLAgent
from personas.base_persona import BasePersona
from personas.persona_switcher import PersonaSwitcher, SwitchConfig
from core.game_logic import GameAction, GameState


class EvasiveDQNAgent(DQNAgent):
    """
    DQN Agent with integrated evasion strategies.
    
    Combines deep Q-learning with sophisticated evasion techniques
    to learn optimal blackjack strategies while maintaining human-like
    behavior patterns and avoiding detection.
    """
    
    def __init__(self, name: str, dqn_config: DQNConfig, evasion_config: EvasionConfig,
                 adaptation_config: Optional[AdaptationConfig] = None,
                 persona: Optional[BasePersona] = None,
                 persona_switcher: Optional[PersonaSwitcher] = None):
        """
        Initialize evasive DQN agent.

        Args:
            name: Agent name
            dqn_config: DQN configuration
            evasion_config: Evasion configuration
            adaptation_config: Optional adaptive learning configuration
            persona: Optional single persona
            persona_switcher: Optional persona switcher for dynamic behavior
        """
        super().__init__(name, dqn_config, persona)

        # Evasion system
        self.evasion_config = evasion_config
        self.evasion_manager = EvasionManager(evasion_config, persona_switcher)
        self.persona_switcher = persona_switcher

        # Adaptive learning system
        self.adaptation_config = adaptation_config or AdaptationConfig()
        self.adaptive_learning = AdaptiveLearningSystem(self.adaptation_config)

        # Enhanced tracking
        self.evasion_history = []
        self.detection_assessments = []
        self.learning_performance_history = []
        self.adaptation_history = []

        # Performance metrics
        self.evasion_overhead = 0.0
        self.learning_efficiency = 1.0
        self.performance_baseline = None
        
    def get_action(self, game_state: GameState, hand_index: int = 0) -> GameAction:
        """
        Get action with integrated evasion strategies.
        
        Args:
            game_state: Current game state
            hand_index: Index of hand to decide for
            
        Returns:
            Action selected with evasion applied
        """
        start_time = time.time()
        
        # Get state representation
        state = self._get_state_representation(game_state, hand_index)
        
        # Get available actions
        available_actions = self._get_available_actions(game_state, hand_index)
        
        # Select action using RL policy
        rl_action = self._select_action(state, available_actions, training=True)
        
        # Apply evasion strategies
        final_action, evasion_metadata = self.evasion_manager.apply_evasion(
            self, game_state, rl_action
        )
        
        # Track evasion overhead
        evasion_time = time.time() - start_time
        self.evasion_overhead = 0.9 * self.evasion_overhead + 0.1 * evasion_time
        
        # Record evasion event
        evasion_record = {
            "training_step": self.training_step,
            "rl_action": rl_action.value,
            "final_action": final_action.value,
            "evasion_metadata": evasion_metadata,
            "processing_time": evasion_time
        }
        self.evasion_history.append(evasion_record)
        
        # Keep history manageable
        if len(self.evasion_history) > 1000:
            self.evasion_history.pop(0)
        
        # Update persona switcher if available
        if self.persona_switcher:
            self.persona_switcher.track_decision(final_action)
        
        # Store current state and action for learning
        self.current_state = state
        self.last_action = self._action_to_index(final_action)
        
        return final_action
    
    def update_experience(self, reward: float, next_game_state: GameState, 
                         done: bool, hand_index: int = 0) -> None:
        """
        Update experience with evasion-aware reward adjustment.
        
        Args:
            reward: Base reward from environment
            next_game_state: Next game state
            done: Whether episode is done
            hand_index: Hand index
        """
        # Apply evasion reward adjustments
        adjusted_reward = self._adjust_reward_for_evasion(reward)
        
        # Call parent update
        super().update_experience(adjusted_reward, next_game_state, done, hand_index)
        
        # Track learning performance
        self.learning_performance_history.append({
            "training_step": self.training_step,
            "original_reward": reward,
            "adjusted_reward": adjusted_reward,
            "detection_risk": self.evasion_manager.current_risk_level
        })
        
        # Keep history manageable
        if len(self.learning_performance_history) > 1000:
            self.learning_performance_history.pop(0)
        
        # Update persona switcher if available
        if self.persona_switcher:
            # Convert reward to result string for persona
            if reward > 5:
                result = "win"
            elif reward < -5:
                result = "loss"
            else:
                result = "push"

            self.persona_switcher.update_result(result)

        # Apply adaptive learning
        if self.training_step % 10 == 0:  # Check every 10 steps
            current_metrics = self._calculate_current_metrics()
            adaptations = self.adaptive_learning.update(self, current_metrics)

            if adaptations:
                self.adaptation_history.extend(adaptations)
                # Keep history manageable
                if len(self.adaptation_history) > 100:
                    self.adaptation_history = self.adaptation_history[-100:]
    
    def _adjust_reward_for_evasion(self, base_reward: float) -> float:
        """
        Adjust reward based on evasion performance.
        
        Args:
            base_reward: Original reward from environment
            
        Returns:
            Adjusted reward incorporating evasion factors
        """
        adjusted_reward = base_reward
        
        # Penalty for high detection risk
        detection_risk = self.evasion_manager.current_risk_level
        if detection_risk > 0.8:
            adjusted_reward += self.evasion_config.detection_penalty * detection_risk
        
        # Small bonus for successful evasion
        if len(self.evasion_history) > 0:
            last_evasion = self.evasion_history[-1]
            if last_evasion["evasion_metadata"]["was_modified"]:
                # Bonus for using evasion when needed
                adjusted_reward += 0.1
        
        # Efficiency bonus/penalty
        evasion_metrics = self.evasion_manager.get_evasion_metrics()
        if evasion_metrics.evasion_effectiveness > 0.7:
            adjusted_reward += 0.2  # Bonus for effective evasion
        
        return adjusted_reward

    def _calculate_current_metrics(self) -> Dict[str, Any]:
        """Calculate current performance and behavior metrics for adaptive learning."""
        metrics = {}

        # Performance metrics
        if len(self.learning_performance_history) > 0:
            recent_rewards = [p["adjusted_reward"] for p in self.learning_performance_history[-50:]]
            metrics["performance_history"] = recent_rewards
            metrics["avg_performance"] = sum(recent_rewards) / len(recent_rewards) if recent_rewards else 0.0
            metrics["performance_variance"] = np.var(recent_rewards) if len(recent_rewards) > 1 else 0.0
        else:
            metrics["performance_history"] = []
            metrics["avg_performance"] = 0.0
            metrics["performance_variance"] = 0.0

        # Detection risk
        metrics["detection_risk"] = self.evasion_manager.current_risk_level

        # Consistency metrics
        if len(self.consistency_scores) > 0:
            metrics["current_consistency"] = self.consistency_scores[-1]
        else:
            metrics["current_consistency"] = 0.0

        # Persona-RL alignment (if persona available)
        if hasattr(self, 'persona') and self.persona:
            metrics["persona_rl_mismatch"] = self._calculate_persona_rl_mismatch()
        else:
            metrics["persona_rl_mismatch"] = 0.0

        # Learning efficiency
        metrics["learning_efficiency"] = self.learning_efficiency

        # Evasion effectiveness
        evasion_metrics = self.evasion_manager.get_evasion_metrics()
        metrics["evasion_effectiveness"] = evasion_metrics.evasion_effectiveness

        return metrics

    def _calculate_persona_rl_mismatch(self) -> float:
        """Calculate mismatch between persona and RL decisions."""
        if len(self.evasion_history) < 10:
            return 0.0

        recent_evasions = self.evasion_history[-10:]
        mismatches = sum(
            1 for e in recent_evasions
            if e["rl_action"] != e["final_action"] and
               e["evasion_metadata"].get("was_modified", False)
        )

        return mismatches / len(recent_evasions)
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get comprehensive statistics including evasion metrics."""
        # Get base stats
        base_stats = super().get_training_metrics()
        
        # Get evasion metrics
        evasion_metrics = self.evasion_manager.get_evasion_metrics()
        strategy_stats = self.evasion_manager.get_strategy_stats()
        detection_assessment = self.evasion_manager.get_detection_risk_assessment()
        
        # Calculate performance metrics
        performance_metrics = self._calculate_performance_metrics()
        
        # Get adaptive learning metrics
        adaptation_metrics = self.adaptive_learning.get_adaptation_metrics()
        strategy_effectiveness = self.adaptive_learning.get_strategy_effectiveness()

        # Combine all statistics
        comprehensive_stats = {
            **base_stats,
            "evasion_metrics": {
                "total_evasions": evasion_metrics.total_evasions,
                "technique_usage": dict(evasion_metrics.technique_usage),
                "avg_detection_risk": evasion_metrics.avg_detection_risk,
                "evasion_effectiveness": evasion_metrics.evasion_effectiveness
            },
            "strategy_stats": strategy_stats,
            "detection_assessment": detection_assessment,
            "performance_metrics": performance_metrics,
            "persona_switcher_stats": self._get_persona_switcher_stats(),
            "adaptive_learning_metrics": {
                "total_adaptations": adaptation_metrics.total_adaptations,
                "adaptation_triggers": dict(adaptation_metrics.adaptation_triggers),
                "adaptation_effectiveness": adaptation_metrics.adaptation_effectiveness,
                "last_adaptation_step": adaptation_metrics.last_adaptation_step
            },
            "strategy_effectiveness": strategy_effectiveness,
            "current_learning_metrics": self._calculate_current_metrics()
        }
        
        return comprehensive_stats
    
    def _calculate_performance_metrics(self) -> Dict[str, Any]:
        """Calculate learning and evasion performance metrics."""
        metrics = {
            "evasion_overhead": self.evasion_overhead,
            "learning_efficiency": self.learning_efficiency,
            "evasion_frequency": 0.0,
            "reward_adjustment_impact": 0.0
        }
        
        # Calculate evasion frequency
        if len(self.evasion_history) > 0:
            evasions_with_modification = sum(
                1 for e in self.evasion_history 
                if e["evasion_metadata"]["was_modified"]
            )
            metrics["evasion_frequency"] = evasions_with_modification / len(self.evasion_history)
        
        # Calculate reward adjustment impact
        if len(self.learning_performance_history) > 10:
            recent_performance = self.learning_performance_history[-10:]
            original_rewards = [p["original_reward"] for p in recent_performance]
            adjusted_rewards = [p["adjusted_reward"] for p in recent_performance]
            
            if original_rewards:
                avg_original = sum(original_rewards) / len(original_rewards)
                avg_adjusted = sum(adjusted_rewards) / len(adjusted_rewards)
                metrics["reward_adjustment_impact"] = avg_adjusted - avg_original
        
        return metrics
    
    def _get_persona_switcher_stats(self) -> Optional[Dict[str, Any]]:
        """Get persona switcher statistics if available."""
        if not self.persona_switcher:
            return None
        
        return {
            "current_persona": self.persona_switcher.get_current_persona_name(),
            "switching_stats": self.persona_switcher.get_switching_stats(),
            "switch_history": len(self.persona_switcher.get_switch_history()),
            "detection_risk": self.persona_switcher.get_detection_risk_assessment()
        }
    
    def force_evasion_activation(self, technique: str) -> bool:
        """
        Force activation of specific evasion technique for testing.
        
        Args:
            technique: Name of evasion technique to activate
            
        Returns:
            True if technique was activated successfully
        """
        from .evasion_strategies import EvasionTechnique
        
        try:
            technique_enum = EvasionTechnique(technique)
            if technique_enum in self.evasion_manager.strategies:
                strategy = self.evasion_manager.strategies[technique_enum]
                strategy.activation_count += 1
                strategy.last_activation = self.training_step
                return True
        except ValueError:
            pass
        
        return False
    
    def update_evasion_config(self, new_config: EvasionConfig) -> None:
        """Update evasion configuration."""
        self.evasion_config = new_config
        self.evasion_manager.update_config(new_config)
    
    def set_persona_switcher(self, persona_switcher: PersonaSwitcher) -> None:
        """Set or update persona switcher."""
        self.persona_switcher = persona_switcher
        self.evasion_manager.set_persona_switcher(persona_switcher)
    
    def get_evasion_history(self) -> List[Dict[str, Any]]:
        """Get evasion history."""
        return self.evasion_history.copy()
    
    def get_learning_performance_history(self) -> List[Dict[str, Any]]:
        """Get learning performance history."""
        return self.learning_performance_history.copy()
    
    def reset_evasion_metrics(self) -> None:
        """Reset evasion metrics and history."""
        self.evasion_manager.reset_metrics()
        self.evasion_history = []
        self.learning_performance_history = []
        self.evasion_overhead = 0.0
    
    def export_evasion_data(self) -> Dict[str, Any]:
        """Export comprehensive evasion data for analysis."""
        return {
            "agent_stats": self.get_comprehensive_stats(),
            "evasion_history": self.get_evasion_history(),
            "learning_performance": self.get_learning_performance_history(),
            "evasion_config": {
                "consistency_threshold": self.evasion_config.consistency_threshold,
                "technique_weights": {k.value: v for k, v in self.evasion_config.technique_weights.items()},
                "noise_intensity": self.evasion_config.noise_intensity,
                "target_consistency_range": self.evasion_config.target_consistency_range
            },
            "detection_assessment": self.evasion_manager.get_detection_risk_assessment(),
            "strategy_effectiveness": self._analyze_strategy_effectiveness()
        }
    
    def _analyze_strategy_effectiveness(self) -> Dict[str, Any]:
        """Analyze effectiveness of different evasion strategies."""
        effectiveness = {}
        
        # Analyze each strategy's impact on detection risk
        for technique, strategy in self.evasion_manager.strategies.items():
            stats = strategy.get_activation_stats()
            
            # Calculate effectiveness metrics
            effectiveness[technique.value] = {
                "activation_count": stats["activation_count"],
                "usage_frequency": stats["activation_count"] / max(1, self.training_step),
                "avg_risk_reduction": 0.0  # Would need more sophisticated analysis
            }
        
        return effectiveness
