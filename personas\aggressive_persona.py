"""
Slightly Aggressive Persona for BlackJack Bot ML.

This persona represents a confident, action-oriented player who:
- Follows Basic Strategy with 90% accuracy
- Makes decisions at moderate speed
- Tends toward hitting and doubling when uncertain
- Takes more risks than optimal
"""

import random
from .base_persona import <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>onfig, DecisionPattern, DecisionContext, ErrorType
from core.hand import Hand
from core.game_logic import GameState
from utils.basic_strategy_charts import BasicStrategyAction


def create_aggressive_persona_config() -> PersonaConfig:
    """Create configuration for the Slightly Aggressive persona."""
    
    decision_pattern = DecisionPattern(
        base_accuracy=0.90,  # Good accuracy but more errors than cautious
        decision_speed=1.3,  # Faster decisions - more confident
        consistency=0.85,    # Somewhat consistent but more variable
        aggression_bias=0.4,  # Aggressive bias toward action
        risk_tolerance=0.7,  # High risk tolerance
        emotional_volatility=0.15,  # Moderate emotional swings
        fatigue_rate=0.0008,  # Moderate fatigue accumulation
        
        # Timing parameters - faster, more confident
        min_decision_time=0.3,
        max_decision_time=6.0,
        avg_decision_time=1.8,
        timing_variance=0.4  # Higher variance - sometimes quick, sometimes deliberate
    )
    
    # Error patterns - more strategy deviations toward aggressive play
    error_patterns = {
        ErrorType.STRATEGY_DEVIATION: 0.08,  # 8% strategy errors
        ErrorType.TIMING_INCONSISTENCY: 0.1,  # More timing variation
        ErrorType.EMOTIONAL_DECISION: 0.015,  # Occasional emotional decisions
        ErrorType.FATIGUE_ERROR: 0.005,  # Some fatigue errors
        ErrorType.DISTRACTION_ERROR: 0.005  # Occasional distractions
    }
    
    # Context modifiers - more affected by emotions
    context_modifiers = {
        DecisionContext.PRESSURE: {
            "accuracy_modifier": -0.1,  # Moderate accuracy drop under pressure
            "speed_modifier": 1.4,  # Faster when pressured (impulsive)
            "aggression_modifier": 0.2  # More aggressive under pressure
        },
        DecisionContext.CONFIDENT: {
            "accuracy_modifier": 0.05,  # Better when confident
            "speed_modifier": 0.7,  # Much faster when confident
            "aggression_modifier": 0.3  # Much more aggressive when winning
        },
        DecisionContext.TIRED: {
            "accuracy_modifier": -0.12,  # Significantly affected by fatigue
            "speed_modifier": 1.1,  # Slightly slower when tired
            "aggression_modifier": 0.1  # Slightly more aggressive (impatient)
        },
        DecisionContext.DISTRACTED: {
            "accuracy_modifier": -0.15,  # Very affected by distractions
            "speed_modifier": 0.8,  # Faster when distracted (impulsive)
            "aggression_modifier": 0.15  # More aggressive when distracted
        }
    }
    
    return PersonaConfig(
        name="Slightly Aggressive",
        description="A confident, action-oriented player who tends toward hitting and doubling with moderate accuracy",
        decision_pattern=decision_pattern,
        error_patterns=error_patterns,
        context_modifiers=context_modifiers
    )


class AggressivePersona(BasePersona):
    """
    Slightly Aggressive persona implementation.
    
    This persona represents a player who:
    - Knows Basic Strategy reasonably well but makes more errors
    - Prefers action over standing
    - Takes calculated risks
    - Gets more aggressive when winning or under pressure
    """
    
    def __init__(self):
        """Initialize the Aggressive persona."""
        config = create_aggressive_persona_config()
        super().__init__(config)
    
    def _apply_persona_bias(self, optimal_action: BasicStrategyAction,
                          player_hand: Hand, dealer_upcard: int,
                          game_state: GameState) -> BasicStrategyAction:
        """
        Apply aggressive bias to decision making.
        
        The aggressive persona tends to:
        - Hit instead of stand on borderline decisions
        - Double more frequently, even on marginal situations
        - Split more often
        - Take action rather than wait
        """
        hand_value = player_hand.get_value()
        
        # Aggressive bias on standing decisions
        if optimal_action == BasicStrategyAction.STAND:
            # Sometimes hit on stiff hands vs strong dealer (impatience)
            if (hand_value in [12, 13, 14, 15, 16] and 
                dealer_upcard in [7, 8, 9, 10, 11] and 
                random.random() < 0.12):  # 12% chance to be aggressive
                return BasicStrategyAction.HIT
            
            # Sometimes hit on soft 18 vs more dealer cards
            if (player_hand.is_soft() and hand_value == 18 and
                dealer_upcard in [2, 7, 8] and random.random() < 0.15):
                return BasicStrategyAction.HIT
        
        # Aggressive bias on hitting decisions
        elif optimal_action == BasicStrategyAction.HIT:
            # Sometimes double instead of hit on 9, 10, 11
            if (hand_value in [9, 10, 11] and 
                player_hand.can_double() and
                random.random() < 0.2):  # 20% chance to double aggressively
                return BasicStrategyAction.DOUBLE
            
            # Sometimes double on soft hands more liberally
            if (player_hand.is_soft() and hand_value in [13, 14, 15, 16, 17] and
                player_hand.can_double() and random.random() < 0.15):
                return BasicStrategyAction.DOUBLE
        
        # More liberal doubling
        elif optimal_action == BasicStrategyAction.DOUBLE:
            # Almost always double (rarely back down)
            if random.random() < 0.05:  # Only 5% chance to not double
                return BasicStrategyAction.HIT
        
        # More liberal splitting
        elif optimal_action == BasicStrategyAction.SPLIT:
            # Almost always split
            pass  # Keep the split decision
        
        # Apply additional aggressive bias based on context
        if self.current_context == DecisionContext.CONFIDENT:
            return self._apply_confident_aggression(optimal_action, player_hand, dealer_upcard)
        elif self.current_context == DecisionContext.PRESSURE:
            return self._apply_pressure_aggression(optimal_action, player_hand, dealer_upcard)
        
        return optimal_action
    
    def _apply_confident_aggression(self, optimal_action: BasicStrategyAction,
                                  player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """Apply extra aggressive bias when confident (winning streak)."""
        hand_value = player_hand.get_value()
        
        # When confident, be even more aggressive
        if optimal_action == BasicStrategyAction.STAND and hand_value <= 16:
            # More likely to hit stiff hands when confident
            if dealer_upcard >= 7 and random.random() < 0.25:
                return BasicStrategyAction.HIT
        
        elif optimal_action == BasicStrategyAction.HIT:
            # More likely to double when confident
            if (hand_value in [9, 10, 11] and 
                player_hand.can_double() and random.random() < 0.35):
                return BasicStrategyAction.DOUBLE
        
        return optimal_action
    
    def _apply_pressure_aggression(self, optimal_action: BasicStrategyAction,
                                 player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """Apply aggressive bias under pressure (losing streak)."""
        hand_value = player_hand.get_value()
        
        # Under pressure, make more desperate aggressive plays
        if optimal_action == BasicStrategyAction.STAND:
            # Hit stiff hands more often when desperate
            if hand_value in [12, 13, 14, 15, 16] and random.random() < 0.2:
                return BasicStrategyAction.HIT
        
        elif optimal_action == BasicStrategyAction.HIT:
            # Double more liberally when trying to recover
            if (hand_value >= 9 and player_hand.can_double() and 
                random.random() < 0.25):
                return BasicStrategyAction.DOUBLE
        
        return optimal_action
    
    def _strategy_deviation_error(self, optimal_action: BasicStrategyAction,
                                player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """
        Generate strategy deviation errors with aggressive bias.
        
        Aggressive players' errors tend to be toward more action-oriented play.
        """
        hand_value = player_hand.get_value()
        
        # Aggressive errors are typically toward more action
        if optimal_action == BasicStrategyAction.STAND:
            # Error: hit when should stand (impatience/aggression)
            if hand_value in [12, 13, 14, 15, 16]:
                return BasicStrategyAction.HIT
            elif hand_value == 17 and player_hand.is_soft():
                return BasicStrategyAction.HIT
        
        elif optimal_action == BasicStrategyAction.HIT:
            # Error: double when should just hit (overconfidence)
            if hand_value in [9, 10, 11] and player_hand.can_double():
                return BasicStrategyAction.DOUBLE
        
        elif optimal_action == BasicStrategyAction.DOUBLE:
            # Rarely back down from doubling
            pass
        
        elif optimal_action == BasicStrategyAction.SPLIT:
            # Rarely avoid splitting
            pass
        
        return optimal_action
    
    def _emotional_decision_error(self, optimal_action: BasicStrategyAction,
                                player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """
        Generate emotional decision errors for aggressive persona.
        
        Aggressive players make emotional decisions toward more action.
        """
        hand_value = player_hand.get_value()
        
        # Emotional decisions based on recent results
        if self.consecutive_losses >= 2:
            # After losses, become more desperate/aggressive
            if optimal_action == BasicStrategyAction.STAND and hand_value <= 16:
                return BasicStrategyAction.HIT
            elif optimal_action == BasicStrategyAction.HIT and player_hand.can_double():
                return BasicStrategyAction.DOUBLE
        
        elif self.consecutive_wins >= 3:
            # When winning, become overconfident
            if optimal_action == BasicStrategyAction.STAND and hand_value <= 15:
                return BasicStrategyAction.HIT
            elif optimal_action == BasicStrategyAction.HIT and hand_value >= 9:
                if player_hand.can_double():
                    return BasicStrategyAction.DOUBLE
        
        return optimal_action
    
    def _fatigue_error(self, optimal_action: BasicStrategyAction,
                     player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """
        Generate fatigue-induced errors for aggressive persona.
        
        When tired, aggressive players become more impulsive.
        """
        hand_value = player_hand.get_value()
        
        # Fatigue leads to more impulsive, aggressive decisions
        if optimal_action == BasicStrategyAction.STAND:
            # Hit more often when tired (impatience)
            if hand_value in [12, 13, 14, 15, 16] and random.random() < 0.3:
                return BasicStrategyAction.HIT
        
        elif optimal_action == BasicStrategyAction.HIT:
            # Double more impulsively when tired
            if hand_value >= 9 and player_hand.can_double() and random.random() < 0.2:
                return BasicStrategyAction.DOUBLE
        
        return optimal_action
