"""
Base Reinforcement Learning Agent for BlackJack Bot ML.

This module provides the foundation for RL agents that can learn optimal
blackjack strategies while maintaining human-like behavior patterns.
"""

import random
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Tuple, Union
from collections import deque
import numpy as np

from agents.base_agent import BaseAgent
from core.game_logic import GameAction, GameState
from personas.base_persona import BasePersona


@dataclass
class RLConfig:
    """Configuration for reinforcement learning agents."""
    
    # Learning parameters
    learning_rate: float = 0.001
    discount_factor: float = 0.99
    epsilon_start: float = 1.0
    epsilon_end: float = 0.01
    epsilon_decay: float = 0.995
    
    # Experience replay
    buffer_size: int = 10000
    batch_size: int = 32
    min_buffer_size: int = 1000
    
    # Training parameters
    target_update_frequency: int = 100
    training_frequency: int = 4
    max_episodes: int = 10000
    max_steps_per_episode: int = 1000
    
    # Evasion parameters
    persona_integration: bool = True
    detection_penalty: float = -10.0
    consistency_threshold: float = 0.95
    behavioral_noise: float = 0.1
    
    # Evaluation parameters
    eval_frequency: int = 100
    eval_episodes: int = 10
    
    # Logging and checkpointing
    log_frequency: int = 10
    checkpoint_frequency: int = 500
    verbose: bool = True


@dataclass
class Experience:
    """Single experience tuple for replay buffer."""
    state: np.ndarray
    action: int
    reward: float
    next_state: np.ndarray
    done: bool
    persona_context: Optional[Dict[str, Any]] = None


class ExperienceBuffer:
    """Experience replay buffer for RL training."""
    
    def __init__(self, capacity: int):
        """
        Initialize experience buffer.
        
        Args:
            capacity: Maximum number of experiences to store
        """
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)
        self.position = 0
    
    def add(self, experience: Experience) -> None:
        """Add experience to buffer."""
        self.buffer.append(experience)
    
    def sample(self, batch_size: int) -> List[Experience]:
        """Sample random batch of experiences."""
        if len(self.buffer) < batch_size:
            return list(self.buffer)
        return random.sample(self.buffer, batch_size)
    
    def __len__(self) -> int:
        """Get current buffer size."""
        return len(self.buffer)
    
    def clear(self) -> None:
        """Clear all experiences."""
        self.buffer.clear()
        self.position = 0


class BaseRLAgent(BaseAgent, ABC):
    """
    Abstract base class for reinforcement learning agents.
    
    Provides framework for RL agents that can learn optimal strategies
    while maintaining human-like behavior patterns for evasion.
    """
    
    def __init__(self, name: str, config: RLConfig, persona: Optional[BasePersona] = None):
        """
        Initialize RL agent.
        
        Args:
            name: Agent name
            config: RL configuration
            persona: Optional persona for human-like behavior
        """
        super().__init__(name)
        self.config = config
        self.persona = persona
        
        # Learning state
        self.epsilon = config.epsilon_start
        self.training_step = 0
        self.episode = 0
        self.total_reward = 0.0
        
        # Experience buffer
        self.experience_buffer = ExperienceBuffer(config.buffer_size)
        
        # Training metrics
        self.training_metrics = {
            'episode_rewards': [],
            'episode_lengths': [],
            'loss_history': [],
            'epsilon_history': [],
            'win_rates': [],
            'detection_scores': []
        }
        
        # Evasion tracking
        self.decision_history = []
        self.consistency_scores = []
        self.last_persona_switch = 0
        
        # State tracking
        self.current_state = None
        self.last_action = None
        self.last_reward = 0.0
    
    @abstractmethod
    def _get_state_representation(self, game_state: GameState, hand_index: int = 0) -> np.ndarray:
        """
        Convert game state to neural network input.
        
        Args:
            game_state: Current game state
            hand_index: Index of hand to process
            
        Returns:
            State representation as numpy array
        """
        pass
    
    @abstractmethod
    def _select_action(self, state: np.ndarray, available_actions: List[GameAction], 
                      training: bool = True) -> GameAction:
        """
        Select action using RL policy.
        
        Args:
            state: Current state representation
            available_actions: Available actions
            training: Whether in training mode
            
        Returns:
            Selected action
        """
        pass
    
    @abstractmethod
    def _update_model(self, batch: List[Experience]) -> float:
        """
        Update model with batch of experiences.
        
        Args:
            batch: Batch of experiences
            
        Returns:
            Training loss
        """
        pass
    
    @abstractmethod
    def save_model(self, filepath: str) -> None:
        """Save model to file."""
        pass
    
    @abstractmethod
    def load_model(self, filepath: str) -> None:
        """Load model from file."""
        pass
    
    def get_action(self, game_state: GameState, hand_index: int = 0) -> GameAction:
        """
        Get action using RL policy with optional persona integration.
        
        Args:
            game_state: Current game state
            hand_index: Index of hand to decide for
            
        Returns:
            Selected action
        """
        # Get state representation
        state = self._get_state_representation(game_state, hand_index)
        
        # Get available actions
        available_actions = self._get_available_actions(game_state, hand_index)
        
        # Select action using RL policy
        rl_action = self._select_action(state, available_actions, training=True)
        
        # Apply persona integration if enabled
        if self.config.persona_integration and self.persona:
            final_action = self._integrate_persona_behavior(
                rl_action, game_state, hand_index, state
            )
        else:
            final_action = rl_action
        
        # Track decision for evasion analysis
        self._track_decision(state, rl_action, final_action, game_state)
        
        # Store current state and action for learning
        self.current_state = state
        self.last_action = self._action_to_index(final_action)
        
        return final_action
    
    def _integrate_persona_behavior(self, rl_action: GameAction, game_state: GameState,
                                  hand_index: int, state: np.ndarray) -> GameAction:
        """
        Integrate persona behavior with RL decision.
        
        Args:
            rl_action: Action selected by RL policy
            game_state: Current game state
            hand_index: Hand index
            state: State representation
            
        Returns:
            Final action with persona integration
        """
        # Get persona recommendation
        persona_action = self.persona.get_action(game_state, hand_index)
        
        # Calculate behavioral noise factor
        noise_factor = self.config.behavioral_noise
        
        # Decide whether to follow RL or persona
        if random.random() < noise_factor:
            # Follow persona for behavioral diversity
            return persona_action
        else:
            # Follow RL policy
            return rl_action
    
    def _track_decision(self, state: np.ndarray, rl_action: GameAction, 
                       final_action: GameAction, game_state: GameState) -> None:
        """Track decision for evasion analysis."""
        decision_record = {
            'episode': self.episode,
            'step': self.training_step,
            'rl_action': rl_action.value,
            'final_action': final_action.value,
            'is_persona_influenced': rl_action != final_action,
            'timestamp': time.time()
        }
        
        self.decision_history.append(decision_record)
        
        # Keep only recent decisions
        if len(self.decision_history) > 1000:
            self.decision_history.pop(0)
        
        # Calculate consistency score
        if len(self.decision_history) >= 50:
            consistency = self._calculate_consistency()
            self.consistency_scores.append(consistency)
    
    def _calculate_consistency(self, lookback: int = 50) -> float:
        """Calculate decision consistency for detection avoidance."""
        if len(self.decision_history) < lookback:
            return 0.0
        
        recent_decisions = self.decision_history[-lookback:]
        
        # Group by similar states and check consistency
        state_actions = {}
        for decision in recent_decisions:
            # Simplified state grouping (would need more sophisticated approach)
            state_key = f"{decision['rl_action']}"
            if state_key not in state_actions:
                state_actions[state_key] = []
            state_actions[state_key].append(decision['final_action'])
        
        # Calculate consistency
        total_situations = 0
        consistent_situations = 0
        
        for actions in state_actions.values():
            if len(actions) >= 2:
                total_situations += 1
                if len(set(actions)) == 1:
                    consistent_situations += 1
        
        if total_situations == 0:
            return 0.0
        
        return consistent_situations / total_situations
    
    def update_experience(self, reward: float, next_game_state: GameState, 
                         done: bool, hand_index: int = 0) -> None:
        """
        Update experience buffer with transition.
        
        Args:
            reward: Reward received
            next_game_state: Next game state
            done: Whether episode is done
            hand_index: Hand index
        """
        if self.current_state is not None and self.last_action is not None:
            # Get next state representation
            next_state = self._get_state_representation(next_game_state, hand_index)
            
            # Create experience
            experience = Experience(
                state=self.current_state,
                action=self.last_action,
                reward=reward,
                next_state=next_state,
                done=done,
                persona_context=self._get_persona_context() if self.persona else None
            )
            
            # Add to buffer
            self.experience_buffer.add(experience)
            
            # Update total reward
            self.total_reward += reward
            self.last_reward = reward
            
            # Train if conditions are met
            if (len(self.experience_buffer) >= self.config.min_buffer_size and
                self.training_step % self.config.training_frequency == 0):
                self._train_step()
            
            self.training_step += 1
    
    def _train_step(self) -> None:
        """Perform one training step."""
        # Sample batch
        batch = self.experience_buffer.sample(self.config.batch_size)
        
        # Update model
        loss = self._update_model(batch)
        
        # Record metrics
        self.training_metrics['loss_history'].append(loss)
        
        # Update epsilon
        self.epsilon = max(
            self.config.epsilon_end,
            self.epsilon * self.config.epsilon_decay
        )
        self.training_metrics['epsilon_history'].append(self.epsilon)
    
    def _get_persona_context(self) -> Dict[str, Any]:
        """Get current persona context for experience."""
        if not self.persona:
            return {}
        
        return {
            'persona_name': self.persona.config.name,
            'current_context': self.persona.current_context.value,
            'current_accuracy': self.persona.current_accuracy,
            'hands_played': self.persona.hands_played
        }
    
    def _get_available_actions(self, game_state: GameState, hand_index: int) -> List[GameAction]:
        """Get available actions for current state."""
        # This would typically come from the game environment
        # For now, return all possible actions
        return [GameAction.HIT, GameAction.STAND, GameAction.DOUBLE, GameAction.SPLIT]
    
    def _action_to_index(self, action: GameAction) -> int:
        """Convert action to index for neural network."""
        action_mapping = {
            GameAction.HIT: 0,
            GameAction.STAND: 1,
            GameAction.DOUBLE: 2,
            GameAction.SPLIT: 3
        }
        return action_mapping.get(action, 0)
    
    def _index_to_action(self, index: int) -> GameAction:
        """Convert index to action."""
        index_mapping = {
            0: GameAction.HIT,
            1: GameAction.STAND,
            2: GameAction.DOUBLE,
            3: GameAction.SPLIT
        }
        return index_mapping.get(index, GameAction.HIT)
    
    def start_episode(self) -> None:
        """Start new episode."""
        self.episode += 1
        self.total_reward = 0.0
        self.current_state = None
        self.last_action = None
    
    def end_episode(self) -> None:
        """End current episode."""
        # Record episode metrics
        self.training_metrics['episode_rewards'].append(self.total_reward)
        
        # Log progress
        if self.config.verbose and self.episode % self.config.log_frequency == 0:
            self._log_progress()
    
    def _log_progress(self) -> None:
        """Log training progress."""
        recent_rewards = self.training_metrics['episode_rewards'][-10:]
        avg_reward = sum(recent_rewards) / len(recent_rewards) if recent_rewards else 0
        
        print(f"Episode {self.episode}: "
              f"Avg Reward: {avg_reward:.2f}, "
              f"Epsilon: {self.epsilon:.3f}, "
              f"Buffer Size: {len(self.experience_buffer)}")
        
        if self.consistency_scores:
            recent_consistency = self.consistency_scores[-1]
            print(f"  Consistency: {recent_consistency:.3f}")
    
    def get_training_metrics(self) -> Dict[str, Any]:
        """Get comprehensive training metrics."""
        return {
            'episode': self.episode,
            'training_step': self.training_step,
            'epsilon': self.epsilon,
            'total_reward': self.total_reward,
            'buffer_size': len(self.experience_buffer),
            'metrics': self.training_metrics.copy(),
            'consistency_scores': self.consistency_scores.copy(),
            'detection_risk': self._assess_detection_risk()
        }
    
    def _assess_detection_risk(self) -> Dict[str, Any]:
        """Assess current detection risk."""
        if not self.consistency_scores:
            return {'risk_level': 'UNKNOWN', 'consistency': 0.0}
        
        current_consistency = self.consistency_scores[-1] if self.consistency_scores else 0.0
        
        if current_consistency >= self.config.consistency_threshold:
            risk_level = 'HIGH'
        elif current_consistency >= 0.8:
            risk_level = 'MEDIUM'
        else:
            risk_level = 'LOW'
        
        return {
            'risk_level': risk_level,
            'consistency': current_consistency,
            'recommendations': self._get_risk_recommendations(risk_level)
        }
    
    def _get_risk_recommendations(self, risk_level: str) -> List[str]:
        """Get recommendations for reducing detection risk."""
        if risk_level == 'HIGH':
            return [
                "Increase behavioral noise factor",
                "Switch persona more frequently",
                "Add more decision randomness"
            ]
        elif risk_level == 'MEDIUM':
            return [
                "Monitor consistency closely",
                "Consider persona integration"
            ]
        else:
            return ["Current behavior appears natural"]

    def get_bet_amount(self, min_bet: float = 1.0, max_bet: float = 100.0) -> float:
        """
        Get the bet amount for the next hand.

        For RL agents, we typically use a fixed bet amount to focus on
        strategy learning rather than betting strategy.

        Args:
            min_bet: Minimum allowed bet
            max_bet: Maximum allowed bet

        Returns:
            The bet amount
        """
        # Use persona betting if available
        if self.persona and hasattr(self.persona, 'get_bet_amount'):
            return self.persona.get_bet_amount(min_bet, max_bet)

        # Default to minimum bet for RL training
        return min_bet
