"""
Unit tests for Human Persona Agent.

Tests P2_T1 implementation: Human Persona Agent integration.
"""

import unittest
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from personas.base_persona import <PERSON><PERSON>ersona, PersonaConfig, DecisionPattern
from personas.human_persona_agent import HumanPersonaAgent
from core.card import Card, Suit, Rank
from core.hand import Hand
from core.game_logic import GameState, GameAction
from utils.basic_strategy_charts import BasicStrategyAction


class TestPersonaImplementation(BasePersona):
    """Test persona implementation for testing."""
    
    def _apply_persona_bias(self, optimal_action, player_hand, dealer_upcard, game_state):
        """Test implementation that sometimes makes errors."""
        # 10% chance to hit instead of stand on 17+
        if (optimal_action == BasicStrategyAction.STAND and 
            player_hand.get_value() >= 17 and 
            self.hands_played % 10 == 0):
            return BasicStrategyAction.HIT
        return optimal_action


class TestHumanPersonaAgent(unittest.TestCase):
    """Test cases for the HumanPersonaAgent class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create test persona
        decision_pattern = DecisionPattern(
            base_accuracy=0.85,
            decision_speed=1.2,
            avg_decision_time=2.0
        )
        
        config = PersonaConfig(
            name="Test Persona",
            description="Test persona for unit testing",
            decision_pattern=decision_pattern
        )
        
        persona = TestPersonaImplementation(config)
        self.agent = HumanPersonaAgent("Test Agent", persona, bet_amount=10.0)
    
    def test_agent_initialization(self):
        """Test agent initialization."""
        self.assertEqual(self.agent.name, "Test Agent")
        self.assertEqual(self.agent.bet_amount, 10.0)
        self.assertIsNotNone(self.agent.persona)
        self.assertEqual(self.agent.persona.config.name, "Test Persona")
    
    def test_betting_functionality(self):
        """Test betting functionality."""
        # Test default bet
        bet = self.agent.get_bet_amount()
        self.assertEqual(bet, 10.0)
        
        # Test with constraints
        bet = self.agent.get_bet_amount(min_bet=15.0, max_bet=50.0)
        self.assertEqual(bet, 15.0)  # Should use minimum
        
        bet = self.agent.get_bet_amount(min_bet=1.0, max_bet=5.0)
        self.assertEqual(bet, 5.0)  # Should use maximum
        
        # Test setting bet amount
        self.agent.set_bet_amount(25.0)
        bet = self.agent.get_bet_amount()
        self.assertEqual(bet, 25.0)
    
    def test_action_delegation(self):
        """Test that actions are properly delegated to persona."""
        # Create test game state
        game_state = GameState()
        
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SEVEN))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SIX))
        dealer_hand.add_card(Card(Suit.DIAMONDS, Rank.FIVE))
        
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Get action
        action = self.agent.get_action(game_state)
        
        # Should return a valid GameAction
        self.assertIsInstance(action, GameAction)
        
        # Should have updated persona state
        self.assertEqual(self.agent.persona.hands_played, 1)
    
    def test_statistics_integration(self):
        """Test statistics integration between agent and persona."""
        # Update stats
        self.agent.update_stats("win", 10.0, 10.0)
        self.agent.update_stats("loss", 10.0, -10.0)
        
        # Get combined stats
        stats = self.agent.get_stats()
        
        # Should have base agent stats
        self.assertIn("hands_played", stats)
        self.assertIn("win_rate", stats)
        
        # Should have persona-specific stats
        self.assertIn("persona_name", stats)
        self.assertIn("persona_description", stats)
        self.assertIn("behavioral_stats", stats)
        self.assertIn("current_context", stats)
        self.assertIn("current_accuracy", stats)
        
        # Check persona name
        self.assertEqual(stats["persona_name"], "Test Persona")
    
    def test_decision_timing_tracking(self):
        """Test decision timing tracking."""
        # Create test game state
        game_state = GameState()
        
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Make several decisions
        for _ in range(5):
            self.agent.get_action(game_state)
        
        # Check timing stats
        stats = self.agent.get_stats()
        persona_metrics = stats["persona_metrics"]
        
        self.assertEqual(persona_metrics["decision_count"], 5)
        self.assertGreater(persona_metrics["total_decision_time"], 0)
        self.assertGreater(persona_metrics["avg_decision_time"], 0)
    
    def test_context_change_tracking(self):
        """Test context change tracking."""
        # Create test game state
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Make initial decision
        self.agent.get_action(game_state)
        
        # Simulate losing streak to trigger context change
        for _ in range(4):
            self.agent.update_stats("loss", 10.0, -10.0)
        
        # Make another decision (should trigger context change)
        self.agent.get_action(game_state)
        
        # Check context switches
        stats = self.agent.get_stats()
        persona_metrics = stats["persona_metrics"]
        
        # Should have tracked at least one context switch
        self.assertGreaterEqual(persona_metrics["context_switches"], 0)
    
    def test_strategy_accuracy_reporting(self):
        """Test strategy accuracy reporting."""
        accuracy = self.agent.get_strategy_accuracy()
        
        # Should return a valid accuracy value
        self.assertIsInstance(accuracy, float)
        self.assertGreaterEqual(accuracy, 0.0)
        self.assertLessEqual(accuracy, 1.0)
        
        # Should match persona's effective accuracy
        persona_accuracy = self.agent.persona._get_effective_accuracy()
        self.assertEqual(accuracy, persona_accuracy)
    
    def test_decision_time_reporting(self):
        """Test decision time reporting."""
        # Create test game state
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Make a decision
        self.agent.get_action(game_state)
        
        # Get decision time
        decision_time = self.agent.get_decision_time()
        
        # Should be a positive value
        self.assertGreater(decision_time, 0)
        self.assertIsInstance(decision_time, float)
    
    def test_data_export(self):
        """Test behavioral data export."""
        # Create test game state and make some decisions
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Make decisions and update results
        for i in range(3):
            self.agent.get_action(game_state)
            result = "win" if i % 2 == 0 else "loss"
            self.agent.update_stats(result, 10.0, 10.0 if result == "win" else -10.0)
        
        # Export data
        data = self.agent.export_behavioral_data()
        
        # Check data structure
        self.assertIn("agent_stats", data)
        self.assertIn("persona_config", data)
        self.assertIn("decision_history", data)
        self.assertIn("error_history", data)
        self.assertIn("timing_history", data)
        self.assertIn("behavioral_stats", data)
        
        # Check persona config export
        persona_config = data["persona_config"]
        self.assertEqual(persona_config["name"], "Test Persona")
        self.assertIn("decision_pattern", persona_config)
        self.assertIn("error_patterns", persona_config)
        
        # Check decision history
        decision_history = data["decision_history"]
        self.assertEqual(len(decision_history), 3)
        
        # Check timing history
        timing_history = data["timing_history"]
        self.assertEqual(len(timing_history), 3)
        self.assertTrue(all(t > 0 for t in timing_history))
    
    def test_history_access(self):
        """Test access to persona history data."""
        # Create test game state
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Make a decision
        self.agent.get_action(game_state)
        
        # Access history
        decision_history = self.agent.get_decision_history()
        error_history = self.agent.get_error_history()
        timing_history = self.agent.get_timing_history()
        
        # Should return lists
        self.assertIsInstance(decision_history, list)
        self.assertIsInstance(error_history, list)
        self.assertIsInstance(timing_history, list)
        
        # Should have one decision recorded
        self.assertEqual(len(decision_history), 1)
        self.assertEqual(len(timing_history), 1)
    
    def test_reset_functionality(self):
        """Test reset functionality."""
        # Create test game state and make decisions
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Make decisions and update stats
        for _ in range(3):
            self.agent.get_action(game_state)
            self.agent.update_stats("win", 10.0, 10.0)
        
        # Verify state before reset
        stats_before = self.agent.get_stats()
        self.assertGreater(stats_before["hands_played"], 0)
        
        # Reset
        self.agent.reset_stats()
        
        # Verify reset state
        stats_after = self.agent.get_stats()
        self.assertEqual(stats_after["hands_played"], 0)
        self.assertEqual(len(self.agent.get_decision_history()), 0)
        self.assertEqual(len(self.agent.get_timing_history()), 0)
    
    def test_string_representations(self):
        """Test string representations."""
        # Test __str__
        str_repr = str(self.agent)
        self.assertIn("HumanPersonaAgent", str_repr)
        self.assertIn("Test Agent", str_repr)
        self.assertIn("Test Persona", str_repr)
        
        # Test __repr__
        repr_str = repr(self.agent)
        self.assertIn("HumanPersonaAgent", repr_str)
        self.assertIn("Test Agent", repr_str)
        self.assertIn("Test Persona", repr_str)


if __name__ == '__main__':
    unittest.main()
