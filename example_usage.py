#!/usr/bin/env python3
"""
BlackJack Bot ML - Example Usage

This script demonstrates the key features of the BlackJack Bot ML project.
Run this script to see the system in action with Basic Strategy.

Usage:
    python example_usage.py
"""

import time
from core.game_logic import BlackjackGame, GameAction
from agents.basic_strategy_agent import BasicStrategyAgent
from utils.simulation import BlackjackSimulator, SimulationConfig
from utils.progress_manager import ProgressManager


def demo_single_hand():
    """Demonstrate playing a single hand with Basic Strategy."""
    print("=" * 60)
    print("DEMO 1: Playing a Single Hand with Basic Strategy")
    print("=" * 60)
    
    # Create game and agent
    game = BlackjackGame(num_decks=6)
    agent = BasicStrategyAgent("Demo Bot")
    agent.set_bet_amount(10.0)
    
    # Start new hand
    print(f"Starting new hand with ${agent.get_bet_amount():.2f} bet...")
    game_state = game.start_new_game(bet_amount=agent.get_bet_amount())
    
    print(f"\nInitial Deal:")
    print(f"Player: {game_state.player_hands[0]}")
    print(f"Dealer: {game_state.dealer_hand.cards[0]} [Hidden]")
    
    # Play the hand
    hand_number = 0
    while not game_state.game_over:
        available_actions = game.get_available_actions()
        if not available_actions:
            break
        
        current_hand = game_state.player_hands[game_state.current_hand_index]
        print(f"\nHand {game_state.current_hand_index + 1}: {current_hand}")
        print(f"Available actions: {[action.value for action in available_actions]}")
        
        # Get agent's decision
        action = agent.get_action(game_state, game_state.current_hand_index)
        print(f"Basic Strategy decision: {action.value}")
        
        # Take the action
        game_state = game.take_action(action)
        
        if action == GameAction.HIT:
            print(f"After hit: {current_hand}")
        elif action == GameAction.SPLIT:
            print(f"Split! Now have {len(game_state.player_hands)} hands")
    
    # Show final results
    print(f"\nFinal Results:")
    print(f"Dealer: {game_state.dealer_hand}")
    for i, hand in enumerate(game_state.player_hands):
        result = game_state.results[i] if i < len(game_state.results) else "Unknown"
        print(f"Player Hand {i+1}: {hand} - {result.value}")
    
    print(f"\nGame complete!")


def demo_simulation():
    """Demonstrate running a simulation with statistics."""
    print("\n" + "=" * 60)
    print("DEMO 2: Running Basic Strategy Simulation")
    print("=" * 60)
    
    # Configure simulation
    config = SimulationConfig(
        num_hands=1000,
        min_bet=5.0,
        max_bet=25.0,
        verbose=False,
        random_seed=42  # For reproducible results
    )
    
    # Create simulator and agent
    simulator = BlackjackSimulator(config)
    agent = BasicStrategyAgent("Simulation Bot")
    agent.set_bet_amount(10.0)
    
    print(f"Running simulation: {config.num_hands} hands...")
    print(f"Bet amount: ${agent.get_bet_amount():.2f}")
    
    # Run simulation with progress updates
    def progress_callback(current, total):
        if current % 200 == 0:
            print(f"Progress: {current}/{total} hands ({current/total*100:.1f}%)")
    
    start_time = time.time()
    results = simulator.run_simulation(agent, progress_callback)
    end_time = time.time()
    
    # Display results
    stats = results['agent_stats']
    print(f"\nSimulation Results:")
    print(f"  Total hands played: {stats['hands_played']}")
    print(f"  Win rate: {stats['win_rate']:.3f} ({stats['win_rate']*100:.1f}%)")
    print(f"  Loss rate: {stats['loss_rate']:.3f} ({stats['loss_rate']*100:.1f}%)")
    print(f"  Push rate: {stats['push_rate']:.3f} ({stats['push_rate']*100:.1f}%)")
    print(f"  Blackjack rate: {stats['blackjack_rate']:.3f} ({stats['blackjack_rate']*100:.1f}%)")
    print(f"  Bust rate: {stats['bust_rate']:.3f} ({stats['bust_rate']*100:.1f}%)")
    print(f"  Total bet: ${stats['total_bet']:.2f}")
    print(f"  Total winnings: ${stats['total_winnings']:.2f}")
    print(f"  Return on investment: {stats['return_on_investment']:.4f} ({stats['return_on_investment']*100:.2f}%)")
    print(f"  Execution time: {end_time - start_time:.2f} seconds")
    print(f"  Hands per second: {results['hands_per_second']:.1f}")
    
    # Show action frequency
    if 'detailed_analysis' in results:
        action_freq = results['detailed_analysis']['action_frequency']
        print(f"\nAction Distribution:")
        total_actions = sum(action_freq.values())
        for action, count in sorted(action_freq.items()):
            percentage = count / total_actions * 100
            print(f"  {action.upper()}: {count} ({percentage:.1f}%)")


def demo_progress_tracking():
    """Demonstrate progress tracking and session management."""
    print("\n" + "=" * 60)
    print("DEMO 3: Progress Tracking and Session Management")
    print("=" * 60)
    
    # Create progress manager
    progress = ProgressManager()
    
    # Start a training session
    session_id = progress.start_session(
        agent_type="BasicStrategy",
        parameters={
            "bet_amount": 10.0,
            "num_decks": 6,
            "rules": "H17_DAS"
        }
    )
    
    print(f"Started training session: {session_id}")
    
    # Simulate training progress with checkpoints
    agent = BasicStrategyAgent("Training Bot")
    
    for checkpoint in range(0, 500, 100):
        # Simulate some training
        time.sleep(0.1)  # Simulate work
        
        # Update progress
        win_rate = 0.42 + checkpoint * 0.0001  # Simulate improving performance
        progress.update_session(
            current_hand=checkpoint,
            metrics={
                "win_rate": win_rate,
                "hands_played": checkpoint,
                "roi": -0.01 + checkpoint * 0.00002
            }
        )
        
        # Save checkpoint
        checkpoint_id = progress.save_checkpoint(
            hand_number=checkpoint,
            model_data={
                "agent_stats": agent.get_stats(),
                "bet_amount": agent.get_bet_amount(),
                "checkpoint": checkpoint
            },
            metrics={"win_rate": win_rate},
            notes=f"Checkpoint at hand {checkpoint}"
        )
        
        print(f"  Checkpoint saved: {checkpoint_id} (Hand {checkpoint}, Win Rate: {win_rate:.3f})")
    
    # End session
    progress.end_session(
        total_hands=500,
        final_metrics={
            "final_win_rate": 0.445,
            "final_roi": -0.009
        }
    )
    
    print(f"Session completed: {session_id}")
    
    # List sessions and checkpoints
    sessions = progress.list_sessions()
    print(f"\nTotal sessions: {len(sessions)}")
    
    checkpoints = progress.list_checkpoints(session_id)
    print(f"Checkpoints for this session: {len(checkpoints)}")
    
    # Export session summary
    summary_path = progress.export_session_summary(session_id)
    print(f"Session summary exported to: {summary_path}")


def demo_basic_strategy_charts():
    """Demonstrate Basic Strategy chart usage."""
    print("\n" + "=" * 60)
    print("DEMO 4: Basic Strategy Chart Examples")
    print("=" * 60)
    
    from utils.basic_strategy_charts import BasicStrategyCharts
    from core.hand import Hand
    from core.card import Card, Suit, Rank
    
    charts = BasicStrategyCharts()
    
    # Test cases demonstrating different scenarios
    test_cases = [
        # (hand_description, cards, dealer_upcard, expected_action)
        ("Hard 16 vs Dealer 10", [Rank.TEN, Rank.SIX], 10, "Should HIT"),
        ("Hard 12 vs Dealer 6", [Rank.TEN, Rank.TWO], 6, "Should STAND"),
        ("Soft 18 vs Dealer 6", [Rank.ACE, Rank.SEVEN], 6, "Should DOUBLE"),
        ("Soft 18 vs Dealer 9", [Rank.ACE, Rank.SEVEN], 9, "Should HIT"),
        ("Pair of 8s vs Dealer A", [Rank.EIGHT, Rank.EIGHT], 11, "Should SPLIT"),
        ("Pair of 5s vs Dealer 6", [Rank.FIVE, Rank.FIVE], 6, "Should DOUBLE (not split)"),
        ("Hard 11 vs Dealer 6", [Rank.FIVE, Rank.SIX], 6, "Should DOUBLE"),
    ]
    
    print("Basic Strategy Examples:")
    for description, ranks, dealer_up, expected in test_cases:
        hand = Hand()
        for rank in ranks:
            hand.add_card(Card(Suit.HEARTS, rank))
        
        action = charts.get_action(hand, dealer_upcard=dealer_up, can_double=True, can_split=True)
        
        print(f"\n  {description}:")
        print(f"    Hand: {hand}")
        print(f"    Dealer upcard: {dealer_up}")
        print(f"    Basic Strategy: {action.value}")
        print(f"    Expected: {expected}")


def main():
    """Run all demonstrations."""
    print("BlackJack Bot ML - Demonstration")
    print("Phase 1: Basic Strategy Implementation")
    print("=====================================")
    
    try:
        # Run all demos
        demo_single_hand()
        demo_simulation()
        demo_progress_tracking()
        demo_basic_strategy_charts()
        
        print("\n" + "=" * 60)
        print("All demonstrations completed successfully!")
        print("=" * 60)
        print("\nNext Steps:")
        print("1. Run tests: python -m pytest tests/ -v")
        print("2. Explore the code in core/, agents/, and utils/")
        print("3. Try modifying simulation parameters")
        print("4. Check out the comprehensive README.md")
        print("\nPhase 2 (Human Personas) coming next!")
        
    except Exception as e:
        print(f"\nError during demonstration: {e}")
        print("Please check your installation and try again.")
        raise


if __name__ == "__main__":
    main()
