"""
Card representation for BlackJack Bot ML.

This module defines the Card class and related enums for suits and ranks,
following standard blackjack rules and values.
"""

from enum import Enum
from typing import Union


class Suit(Enum):
    """Card suits enumeration."""
    HEARTS = "♥"
    DIAMONDS = "♦"
    CLUBS = "♣"
    SPADES = "♠"
    
    def __str__(self) -> str:
        return self.value


class Rank(Enum):
    """Card ranks enumeration with blackjack values."""
    ACE = ("A", [1, 11])
    TWO = ("2", [2])
    THREE = ("3", [3])
    FOUR = ("4", [4])
    FIVE = ("5", [5])
    SIX = ("6", [6])
    SEVEN = ("7", [7])
    EIGHT = ("8", [8])
    NINE = ("9", [9])
    TEN = ("10", [10])
    JACK = ("J", [10])
    QUEEN = ("Q", [10])
    KING = ("K", [10])
    
    def __init__(self, symbol: str, values: list):
        self.symbol = symbol
        self.values = values
    
    def __str__(self) -> str:
        return self.symbol
    
    @property
    def is_ace(self) -> bool:
        """Check if this rank is an Ace."""
        return self == Rank.ACE
    
    @property
    def blackjack_values(self) -> list:
        """Get possible blackjack values for this rank."""
        return self.values.copy()


class Card:
    """
    Represents a playing card with suit and rank.
    
    Provides blackjack-specific functionality including value calculation
    and proper string representation.
    """
    
    def __init__(self, suit: Suit, rank: Rank):
        """
        Initialize a card with suit and rank.
        
        Args:
            suit: The card's suit
            rank: The card's rank
        """
        self.suit = suit
        self.rank = rank
    
    def __str__(self) -> str:
        """String representation of the card."""
        return f"{self.rank}{self.suit}"
    
    def __repr__(self) -> str:
        """Detailed string representation for debugging."""
        return f"Card({self.suit.name}, {self.rank.name})"
    
    def __eq__(self, other) -> bool:
        """Check equality with another card."""
        if not isinstance(other, Card):
            return False
        return self.suit == other.suit and self.rank == other.rank
    
    def __hash__(self) -> int:
        """Hash function for use in sets and dictionaries."""
        return hash((self.suit, self.rank))
    
    @property
    def is_ace(self) -> bool:
        """Check if this card is an Ace."""
        return self.rank.is_ace
    
    @property
    def blackjack_values(self) -> list:
        """Get possible blackjack values for this card."""
        return self.rank.blackjack_values
    
    @property
    def min_value(self) -> int:
        """Get the minimum blackjack value for this card."""
        return min(self.blackjack_values)
    
    @property
    def max_value(self) -> int:
        """Get the maximum blackjack value for this card."""
        return max(self.blackjack_values)
    
    def get_value(self, count_ace_as_eleven: bool = True) -> int:
        """
        Get the blackjack value of this card.
        
        Args:
            count_ace_as_eleven: If True and card is Ace, return 11; otherwise return 1
            
        Returns:
            The blackjack value of the card
        """
        if self.is_ace:
            return 11 if count_ace_as_eleven else 1
        return self.blackjack_values[0]
