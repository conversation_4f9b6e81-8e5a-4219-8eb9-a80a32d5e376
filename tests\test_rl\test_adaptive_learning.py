"""
Unit tests for Adaptive Learning System.

Tests P3_T3 implementation: Adaptive Learning System.
"""

import unittest
import sys
import os
import numpy as np

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from rl.adaptive_learning import (
    AdaptationConfig, AdaptationTrigger, AdaptationMetrics,
    LearningRateAdaptation, ExplorationAdaptation, PersonaAdaptation,
    EvasionAdaptation, ConsistencyAdaptation, AdaptiveLearningSystem
)
from rl.evasive_dqn_agent import EvasiveDQNAgent
from rl.dqn_agent import DQNConfig
from rl.evasion_strategies import EvasionConfig
from personas.cautious_persona import CautiousPersona


class MockRLAgent:
    """Mock RL agent for testing adaptive learning."""
    
    def __init__(self):
        self.training_step = 100
        self.epsilon = 0.3
        self.consistency_scores = [0.8, 0.85, 0.9, 0.95]
        self.persona = None
        
        # Mock optimizer
        self.optimizer = type('MockOptimizer', (), {
            'param_groups': [{'lr': 0.001}]
        })()


class TestAdaptationConfig(unittest.TestCase):
    """Test cases for AdaptationConfig."""
    
    def test_config_creation(self):
        """Test adaptation config creation with defaults."""
        config = AdaptationConfig()
        
        # Check default values
        self.assertEqual(config.performance_window, 100)
        self.assertEqual(config.performance_threshold, -0.1)
        self.assertTrue(config.lr_adaptation_enabled)
        self.assertTrue(config.exploration_adaptation_enabled)
        self.assertEqual(config.target_consistency_range, (0.7, 0.85))
    
    def test_config_customization(self):
        """Test custom adaptation config."""
        config = AdaptationConfig(
            performance_window=50,
            lr_increase_factor=1.2,
            epsilon_increase_factor=1.3,
            persona_adaptation_enabled=False
        )
        
        self.assertEqual(config.performance_window, 50)
        self.assertEqual(config.lr_increase_factor, 1.2)
        self.assertEqual(config.epsilon_increase_factor, 1.3)
        self.assertFalse(config.persona_adaptation_enabled)


class TestLearningRateAdaptation(unittest.TestCase):
    """Test cases for LearningRateAdaptation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = AdaptationConfig(performance_window=10)
        self.strategy = LearningRateAdaptation(self.config)
        self.agent = MockRLAgent()
    
    def test_strategy_initialization(self):
        """Test strategy initialization."""
        self.assertEqual(self.strategy.config, self.config)
        self.assertEqual(self.strategy.activation_count, 0)
    
    def test_should_adapt_performance_decline(self):
        """Test adaptation trigger for performance decline."""
        # Create declining performance
        declining_performance = [1.0, 0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1]
        metrics = {"performance_history": declining_performance}
        
        # Should trigger adaptation
        self.assertTrue(self.strategy.should_adapt(self.agent, metrics))
    
    def test_should_not_adapt_improving_performance(self):
        """Test no adaptation for improving performance."""
        # Create improving performance
        improving_performance = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
        metrics = {"performance_history": improving_performance}
        
        # Should not trigger adaptation
        self.assertFalse(self.strategy.should_adapt(self.agent, metrics))
    
    def test_apply_adaptation(self):
        """Test learning rate adaptation application."""
        initial_lr = self.agent.optimizer.param_groups[0]['lr']
        
        # Apply adaptation for performance decline
        result = self.strategy.apply_adaptation(self.agent, AdaptationTrigger.PERFORMANCE_DECLINE)
        
        # Check that learning rate was increased
        new_lr = self.agent.optimizer.param_groups[0]['lr']
        self.assertGreater(new_lr, initial_lr)
        
        # Check result metadata
        self.assertEqual(result["strategy"], "learning_rate_adaptation")
        self.assertEqual(result["old_lr"], initial_lr)
        self.assertEqual(result["new_lr"], new_lr)
        self.assertEqual(result["trigger"], AdaptationTrigger.PERFORMANCE_DECLINE.value)
        
        # Check activation tracking
        self.assertEqual(self.strategy.activation_count, 1)


class TestExplorationAdaptation(unittest.TestCase):
    """Test cases for ExplorationAdaptation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = AdaptationConfig()
        self.strategy = ExplorationAdaptation(self.config)
        self.agent = MockRLAgent()
    
    def test_should_adapt_high_detection_risk(self):
        """Test adaptation trigger for high detection risk."""
        metrics = {
            "detection_risk": 0.8,
            "performance_variance": 0.2
        }
        
        # Should trigger adaptation
        self.assertTrue(self.strategy.should_adapt(self.agent, metrics))
    
    def test_should_adapt_low_variance(self):
        """Test adaptation trigger for low performance variance."""
        metrics = {
            "detection_risk": 0.3,
            "performance_variance": 0.05  # Low variance
        }
        
        # Should trigger adaptation
        self.assertTrue(self.strategy.should_adapt(self.agent, metrics))
    
    def test_apply_adaptation_increase_exploration(self):
        """Test exploration increase adaptation."""
        initial_epsilon = self.agent.epsilon
        
        # Apply adaptation for detection risk
        result = self.strategy.apply_adaptation(self.agent, AdaptationTrigger.DETECTION_RISK)
        
        # Check that epsilon was increased
        self.assertGreater(self.agent.epsilon, initial_epsilon)
        
        # Check result metadata
        self.assertEqual(result["strategy"], "exploration_adaptation")
        self.assertEqual(result["trigger"], AdaptationTrigger.DETECTION_RISK.value)


class TestPersonaAdaptation(unittest.TestCase):
    """Test cases for PersonaAdaptation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = AdaptationConfig(persona_mismatch_threshold=0.2)
        self.strategy = PersonaAdaptation(self.config)
        self.agent = MockRLAgent()
        self.agent.persona = CautiousPersona()
    
    def test_should_adapt_with_mismatch(self):
        """Test adaptation trigger for persona mismatch."""
        metrics = {"persona_rl_mismatch": 0.3}  # Above threshold
        
        # Should trigger adaptation
        self.assertTrue(self.strategy.should_adapt(self.agent, metrics))
    
    def test_should_not_adapt_without_persona(self):
        """Test no adaptation without persona."""
        agent_no_persona = MockRLAgent()
        metrics = {"persona_rl_mismatch": 0.5}
        
        # Should not trigger adaptation
        self.assertFalse(self.strategy.should_adapt(agent_no_persona, metrics))
    
    def test_apply_adaptation(self):
        """Test persona adaptation application."""
        initial_accuracy = self.agent.persona.current_accuracy
        
        # Apply adaptation
        result = self.strategy.apply_adaptation(self.agent, AdaptationTrigger.PERSONA_MISMATCH)
        
        # Check that accuracy was increased
        self.assertGreater(self.agent.persona.current_accuracy, initial_accuracy)
        
        # Check result metadata
        self.assertEqual(result["strategy"], "persona_adaptation")
        self.assertEqual(result["trigger"], AdaptationTrigger.PERSONA_MISMATCH.value)


class TestAdaptiveLearningSystem(unittest.TestCase):
    """Test cases for AdaptiveLearningSystem."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = AdaptationConfig(
            performance_window=10,
            min_adaptation_interval=5
        )
        self.system = AdaptiveLearningSystem(self.config)
        self.agent = MockRLAgent()
    
    def test_system_initialization(self):
        """Test system initialization."""
        self.assertEqual(self.system.config, self.config)
        self.assertIsInstance(self.system.metrics, AdaptationMetrics)
        
        # Should have all strategies
        expected_strategies = ["learning_rate", "exploration", "persona", "evasion", "consistency"]
        for strategy_name in expected_strategies:
            self.assertIn(strategy_name, self.system.strategies)
    
    def test_adaptation_trigger_identification(self):
        """Test adaptation trigger identification."""
        # Create metrics that should trigger adaptations
        metrics = {
            "performance_history": [1.0, 0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1],  # Declining
            "detection_risk": 0.9,  # High risk
            "performance_variance": 0.05,  # Low variance
            "current_consistency": 0.95  # High consistency
        }
        
        triggers = self.system._identify_adaptation_triggers(self.agent, metrics)
        
        # Should identify multiple triggers
        self.assertGreater(len(triggers), 0)
        self.assertIn(AdaptationTrigger.PERFORMANCE_DECLINE, triggers)
        self.assertIn(AdaptationTrigger.DETECTION_RISK, triggers)
    
    def test_update_with_adaptations(self):
        """Test system update with adaptations."""
        # Set up agent for adaptation
        self.agent.training_step = 100
        
        # Create metrics that trigger adaptations
        metrics = {
            "performance_history": [1.0, 0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1],
            "detection_risk": 0.9,
            "performance_variance": 0.05,
            "current_consistency": 0.95,
            "persona_rl_mismatch": 0.0,
            "learning_efficiency": 1.0,
            "evasion_effectiveness": 0.5
        }
        
        # Apply adaptations
        adaptations = self.system.update(self.agent, metrics)
        
        # Should have applied some adaptations
        self.assertGreater(len(adaptations), 0)
        
        # Check that metrics were updated
        self.assertGreater(self.system.metrics.total_adaptations, 0)
        self.assertGreater(len(self.system.adaptation_history), 0)
    
    def test_minimum_interval_enforcement(self):
        """Test minimum adaptation interval enforcement."""
        # Set recent adaptation
        self.system.metrics.last_adaptation_step = 98
        self.agent.training_step = 100  # Only 2 steps since last adaptation
        
        metrics = {
            "performance_history": [1.0, 0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1],
            "detection_risk": 0.9
        }
        
        # Should not adapt due to minimum interval
        adaptations = self.system.update(self.agent, metrics)
        self.assertEqual(len(adaptations), 0)
    
    def test_metrics_tracking(self):
        """Test metrics tracking."""
        initial_metrics = self.system.get_adaptation_metrics()
        self.assertEqual(initial_metrics.total_adaptations, 0)
        
        # Force an adaptation
        self.agent.training_step = 100
        metrics = {
            "performance_history": [1.0, 0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1],
            "detection_risk": 0.9,
            "performance_variance": 0.05,
            "current_consistency": 0.95,
            "persona_rl_mismatch": 0.0,
            "learning_efficiency": 1.0,
            "evasion_effectiveness": 0.5
        }
        
        self.system.update(self.agent, metrics)
        
        # Check updated metrics
        updated_metrics = self.system.get_adaptation_metrics()
        self.assertGreater(updated_metrics.total_adaptations, 0)
    
    def test_strategy_effectiveness_tracking(self):
        """Test strategy effectiveness tracking."""
        effectiveness = self.system.get_strategy_effectiveness()
        
        # Should have effectiveness scores for all strategies
        for strategy_name in self.system.strategies:
            self.assertIn(strategy_name, effectiveness)
            self.assertIsInstance(effectiveness[strategy_name], float)


class TestEvasiveDQNAgentWithAdaptiveLearning(unittest.TestCase):
    """Test cases for EvasiveDQNAgent with adaptive learning."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.dqn_config = DQNConfig(
            hidden_layers=[16, 8],
            batch_size=4,
            min_buffer_size=5
        )
        self.evasion_config = EvasionConfig()
        self.adaptation_config = AdaptationConfig(
            performance_window=10,
            min_adaptation_interval=5
        )
        
        self.agent = EvasiveDQNAgent(
            "Test Adaptive Agent",
            self.dqn_config,
            self.evasion_config,
            self.adaptation_config
        )
    
    def test_agent_initialization_with_adaptation(self):
        """Test agent initialization with adaptive learning."""
        self.assertEqual(self.agent.name, "Test Adaptive Agent")
        self.assertIsNotNone(self.agent.adaptive_learning)
        self.assertEqual(self.agent.adaptation_config, self.adaptation_config)
        self.assertEqual(len(self.agent.adaptation_history), 0)
    
    def test_metrics_calculation(self):
        """Test current metrics calculation."""
        # Add some learning performance history
        for i in range(10):
            self.agent.learning_performance_history.append({
                "training_step": i,
                "original_reward": float(i),
                "adjusted_reward": float(i) * 1.1,
                "detection_risk": 0.5
            })
        
        # Calculate metrics
        metrics = self.agent._calculate_current_metrics()
        
        # Check metrics structure
        self.assertIn("performance_history", metrics)
        self.assertIn("avg_performance", metrics)
        self.assertIn("performance_variance", metrics)
        self.assertIn("detection_risk", metrics)
        self.assertIn("current_consistency", metrics)
        self.assertIn("learning_efficiency", metrics)
    
    def test_comprehensive_stats_with_adaptation(self):
        """Test comprehensive statistics with adaptive learning."""
        # Make some decisions to generate data
        from core.game_logic import GameState
        from core.hand import Hand
        from core.card import Card, Suit, Rank
        
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Generate some data
        for _ in range(5):
            self.agent.get_action(game_state)
        
        # Get comprehensive stats
        stats = self.agent.get_comprehensive_stats()
        
        # Check adaptive learning sections
        self.assertIn("adaptive_learning_metrics", stats)
        self.assertIn("strategy_effectiveness", stats)
        self.assertIn("current_learning_metrics", stats)
        
        # Check adaptive learning metrics structure
        adaptive_metrics = stats["adaptive_learning_metrics"]
        self.assertIn("total_adaptations", adaptive_metrics)
        self.assertIn("adaptation_triggers", adaptive_metrics)
        self.assertIn("adaptation_effectiveness", adaptive_metrics)


if __name__ == '__main__':
    unittest.main()
