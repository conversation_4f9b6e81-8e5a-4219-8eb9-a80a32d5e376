"""
Unit tests for Persona Switcher.

Tests P2_T3 implementation: Persona Switcher Bot.
"""

import unittest
import sys
import os
import time

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from personas.persona_switcher import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>witch<PERSON>onfig, Switch<PERSON>rigger
from personas.persona_switcher_agent import PersonaSwitcherAgent
from core.card import Card, Suit, Rank
from core.hand import Hand
from core.game_logic import GameState, GameAction
from utils.simulation import BlackjackSimulator, SimulationConfig


class TestPersonaSwitcher(unittest.TestCase):
    """Test cases for the PersonaSwitcher class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create test configuration with shorter intervals for testing
        self.config = SwitchConfig(
            min_switch_time=1.0,  # 1 second for testing
            max_switch_time=5.0,  # 5 seconds for testing
            min_hands_per_persona=5,
            max_hands_per_persona=15,
            switch_on_loss_streak=3,
            switch_on_win_streak=4,
            random_switch_probability=0.1  # Higher for testing
        )
        
        self.switcher = PersonaSwitcher(self.config)
    
    def test_switcher_initialization(self):
        """Test switcher initialization."""
        self.assertIsNotNone(self.switcher.current_persona)
        self.assertIn(self.switcher.current_persona_name, self.switcher.personas.keys())
        self.assertEqual(len(self.switcher.personas), 3)  # cautious, aggressive, intuitive
        self.assertEqual(len(self.switcher.switch_history), 0)
    
    def test_persona_availability(self):
        """Test that all expected personas are available."""
        expected_personas = {"cautious", "aggressive", "intuitive"}
        available_personas = set(self.switcher.personas.keys())
        self.assertEqual(available_personas, expected_personas)
        
        # Test that all personas are properly initialized
        for persona in self.switcher.personas.values():
            self.assertIsNotNone(persona.config)
            self.assertIsNotNone(persona.charts)
    
    def test_basic_action_delegation(self):
        """Test that actions are properly delegated to current persona."""
        # Create test game state
        game_state = GameState()
        
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Get action
        action = self.switcher.get_action(game_state)
        
        # Should return a valid GameAction
        self.assertIsInstance(action, GameAction)
        
        # Should have updated current persona state
        self.assertGreater(self.switcher.current_persona.hands_played, 0)
    
    def test_hand_based_switching(self):
        """Test switching based on hand count."""
        initial_persona = self.switcher.current_persona_name
        
        # Create test game state
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Play enough hands to trigger switch
        for _ in range(self.config.max_hands_per_persona + 1):
            self.switcher.get_action(game_state)
        
        # Should have switched personas
        self.assertNotEqual(self.switcher.current_persona_name, initial_persona)
        self.assertGreater(len(self.switcher.switch_history), 0)
        
        # Check switch record
        last_switch = self.switcher.switch_history[-1]
        self.assertEqual(last_switch["trigger"], SwitchTrigger.HAND_COUNT.value)
        self.assertEqual(last_switch["old_persona"], initial_persona)
    
    def test_result_based_switching(self):
        """Test switching based on win/loss streaks."""
        initial_persona = self.switcher.current_persona_name
        
        # Simulate losing streak
        for _ in range(self.config.switch_on_loss_streak):
            self.switcher.update_result("loss")
        
        # Create test game state and make a decision (should trigger switch)
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        self.switcher.get_action(game_state)
        
        # Should have switched due to losing streak
        self.assertNotEqual(self.switcher.current_persona_name, initial_persona)
        
        # Check switch record
        if self.switcher.switch_history:
            last_switch = self.switcher.switch_history[-1]
            self.assertEqual(last_switch["trigger"], SwitchTrigger.RESULT_STREAK.value)
    
    def test_time_based_switching(self):
        """Test switching based on time."""
        initial_persona = self.switcher.current_persona_name
        
        # Manually set last switch time to trigger time-based switch
        self.switcher.last_switch_time = time.time() - self.config.max_switch_time - 1
        
        # Create test game state and make a decision
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        self.switcher.get_action(game_state)
        
        # Should have switched due to time
        self.assertNotEqual(self.switcher.current_persona_name, initial_persona)
        
        # Check switch record
        if self.switcher.switch_history:
            last_switch = self.switcher.switch_history[-1]
            self.assertEqual(last_switch["trigger"], SwitchTrigger.TIME_BASED.value)
    
    def test_switching_statistics(self):
        """Test switching statistics tracking."""
        # Force some switches
        initial_persona = self.switcher.current_persona_name
        
        # Create test game state
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Trigger hand-based switch
        for _ in range(self.config.max_hands_per_persona + 1):
            self.switcher.get_action(game_state)
        
        # Get statistics
        stats = self.switcher.get_switching_stats()
        
        # Check statistics structure
        self.assertIn("total_switches", stats)
        self.assertIn("trigger_distribution", stats)
        self.assertIn("persona_usage", stats)
        self.assertIn("current_persona", stats)
        
        # Should have at least one switch
        self.assertGreaterEqual(stats["total_switches"], 1)
        self.assertIn(SwitchTrigger.HAND_COUNT.value, stats["trigger_distribution"])
    
    def test_state_transfer(self):
        """Test that relevant state is transferred between personas."""
        # Set up win streak
        for _ in range(3):
            self.switcher.update_result("win")
        
        old_wins = self.switcher.current_persona.consecutive_wins
        old_persona_name = self.switcher.current_persona_name
        
        # Force a switch
        self.switcher.hands_with_current_persona = self.config.max_hands_per_persona + 1
        
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        self.switcher.get_action(game_state)
        
        # Should have switched
        self.assertNotEqual(self.switcher.current_persona_name, old_persona_name)
        
        # Win streak should be transferred
        self.assertEqual(self.switcher.current_persona.consecutive_wins, old_wins)
    
    def test_pattern_tracking(self):
        """Test decision pattern tracking for detection avoidance."""
        # Make several decisions
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        for _ in range(10):
            self.switcher.get_action(game_state)
        
        # Should have tracked decision patterns
        self.assertGreater(len(self.switcher.decision_patterns), 0)
        
        # Check pattern structure
        if self.switcher.decision_patterns:
            pattern = self.switcher.decision_patterns[0]
            self.assertIn("hand_value", pattern)
            self.assertIn("dealer_upcard", pattern)
            self.assertIn("action", pattern)
            self.assertIn("persona", pattern)
    
    def test_consistency_calculation(self):
        """Test consistency calculation for detection avoidance."""
        # Make consistent decisions (same situation, same action)
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Make many decisions in same situation
        for _ in range(20):
            self.switcher.get_action(game_state)
        
        # Calculate consistency
        consistency = self.switcher._calculate_recent_consistency()
        
        # Should be a valid consistency score
        self.assertIsInstance(consistency, float)
        self.assertGreaterEqual(consistency, 0.0)
        self.assertLessEqual(consistency, 1.0)
    
    def test_session_reset(self):
        """Test session reset functionality."""
        # Make some decisions and switches
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        for _ in range(self.config.max_hands_per_persona + 1):
            self.switcher.get_action(game_state)
        
        # Should have some history
        self.assertGreater(len(self.switcher.switch_history), 0)
        self.assertGreater(len(self.switcher.decision_patterns), 0)
        
        # Reset session
        self.switcher.reset_session()
        
        # Should be reset
        self.assertEqual(len(self.switcher.switch_history), 0)
        self.assertEqual(len(self.switcher.decision_patterns), 0)
        self.assertEqual(self.switcher.hands_with_current_persona, 0)


class TestPersonaSwitcherAgent(unittest.TestCase):
    """Test cases for the PersonaSwitcherAgent class."""
    
    def setUp(self):
        """Set up test fixtures."""
        config = SwitchConfig(
            min_hands_per_persona=5,
            max_hands_per_persona=15
        )
        self.agent = PersonaSwitcherAgent("Test Switcher Agent", config, bet_amount=10.0)
    
    def test_agent_initialization(self):
        """Test agent initialization."""
        self.assertEqual(self.agent.name, "Test Switcher Agent")
        self.assertEqual(self.agent.bet_amount, 10.0)
        self.assertIsNotNone(self.agent.switcher)
        self.assertIn(self.agent.get_current_persona_name(), ["cautious", "aggressive", "intuitive"])
    
    def test_betting_functionality(self):
        """Test betting functionality."""
        bet = self.agent.get_bet_amount()
        self.assertEqual(bet, 10.0)
        
        # Test with constraints
        bet = self.agent.get_bet_amount(min_bet=15.0, max_bet=50.0)
        self.assertEqual(bet, 15.0)
        
        # Test setting bet amount
        self.agent.set_bet_amount(25.0)
        bet = self.agent.get_bet_amount()
        self.assertEqual(bet, 25.0)
    
    def test_action_delegation_and_switching(self):
        """Test action delegation and switching behavior."""
        initial_persona = self.agent.get_current_persona_name()
        
        # Create test game state
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Make enough decisions to trigger switch
        for _ in range(20):
            action = self.agent.get_action(game_state)
            self.assertIsInstance(action, GameAction)
        
        # Should have switched at some point
        final_persona = self.agent.get_current_persona_name()
        switch_history = self.agent.get_switch_history()
        
        # Either switched or at least tracked decisions
        self.assertTrue(final_persona != initial_persona or len(switch_history) == 0)
    
    def test_statistics_integration(self):
        """Test statistics integration."""
        # Make some decisions and update results
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        for i in range(5):
            self.agent.get_action(game_state)
            result = "win" if i % 2 == 0 else "loss"
            self.agent.update_stats(result, 10.0, 10.0 if result == "win" else -10.0)
        
        # Get comprehensive stats
        stats = self.agent.get_stats()
        
        # Should have all expected sections
        self.assertIn("current_persona", stats)
        self.assertIn("current_persona_config", stats)
        self.assertIn("switcher_metrics", stats)
        self.assertIn("switching_stats", stats)
        self.assertIn("behavioral_stats", stats)
        
        # Check persona config
        persona_config = stats["current_persona_config"]
        self.assertIn("name", persona_config)
        self.assertIn("base_accuracy", persona_config)
        self.assertIn("decision_speed", persona_config)
    
    def test_detection_risk_assessment(self):
        """Test detection risk assessment."""
        # Make some decisions
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        for _ in range(10):
            self.agent.get_action(game_state)
        
        # Get risk assessment
        risk_assessment = self.agent.get_detection_risk_assessment()
        
        # Check assessment structure
        self.assertIn("overall_risk", risk_assessment)
        self.assertIn("consistency_risk", risk_assessment)
        self.assertIn("switch_frequency", risk_assessment)
        self.assertIn("recommendations", risk_assessment)
        
        # Risk level should be valid
        self.assertIn(risk_assessment["overall_risk"], ["LOW", "MEDIUM", "HIGH"])
        
        # Recommendations should be a list
        self.assertIsInstance(risk_assessment["recommendations"], list)
    
    def test_forced_switching(self):
        """Test forced persona switching."""
        initial_persona = self.agent.get_current_persona_name()
        
        # Get list of other personas
        all_personas = ["cautious", "aggressive", "intuitive"]
        other_personas = [p for p in all_personas if p != initial_persona]
        
        # Force switch to specific persona
        target_persona = other_personas[0]
        success = self.agent.force_persona_switch(target_persona)
        
        self.assertTrue(success)
        self.assertEqual(self.agent.get_current_persona_name(), target_persona)
        
        # Test random switch
        success = self.agent.force_persona_switch()
        self.assertTrue(success)
    
    def test_data_export(self):
        """Test comprehensive data export."""
        # Make some decisions
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        for _ in range(10):
            self.agent.get_action(game_state)
        
        # Export data
        data = self.agent.export_switching_data()
        
        # Check data structure
        self.assertIn("agent_stats", data)
        self.assertIn("switch_history", data)
        self.assertIn("switching_config", data)
        self.assertIn("detection_risk", data)
        self.assertIn("decision_patterns", data)
        self.assertIn("persona_configs", data)
        
        # Check persona configs
        persona_configs = data["persona_configs"]
        self.assertEqual(len(persona_configs), 3)
        for config in persona_configs.values():
            self.assertIn("name", config)
            self.assertIn("base_accuracy", config)
    
    def test_string_representations(self):
        """Test string representations."""
        # Test __str__
        str_repr = str(self.agent)
        self.assertIn("PersonaSwitcherAgent", str_repr)
        self.assertIn("Test Switcher Agent", str_repr)
        
        # Test __repr__
        repr_str = repr(self.agent)
        self.assertIn("PersonaSwitcherAgent", repr_str)
        self.assertIn("Test Switcher Agent", repr_str)


if __name__ == '__main__':
    unittest.main()
