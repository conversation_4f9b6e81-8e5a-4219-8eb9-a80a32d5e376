"""
Unit tests for RL Environment.

Tests P3_T1 implementation: RL Environment for blackjack training.
"""

import unittest
import sys
import os
import numpy as np

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from rl.rl_environment import Blackjack<PERSON><PERSON>nvironment, RLState, RLReward, RLRewardType
from core.game_logic import GameAction
from personas.cautious_persona import CautiousPersona


class TestRLState(unittest.TestCase):
    """Test cases for RLState class."""
    
    def test_rl_state_creation(self):
        """Test RLState creation and conversion."""
        state = RLState(
            player_value=16,
            player_soft=False,
            player_pairs=False,
            player_can_double=True,
            player_can_split=False,
            dealer_upcard=7,
            dealer_soft=False,
            deck_penetration=0.5,
            bet_amount=10.0,
            persona_context="cautious",
            decision_context="normal",
            consistency_score=0.8,
            hands_since_switch=25
        )
        
        # Test basic properties
        self.assertEqual(state.player_value, 16)
        self.assertFalse(state.player_soft)
        self.assertTrue(state.player_can_double)
        self.assertEqual(state.dealer_upcard, 7)
        
        # Test array conversion
        array = state.to_array()
        self.assertIsInstance(array, np.ndarray)
        self.assertEqual(len(array), 18)
        
        # Check normalized values
        self.assertAlmostEqual(array[0], 16.0 / 21.0, places=3)  # Player value
        self.assertEqual(array[1], 0.0)  # Player soft
        self.assertEqual(array[3], 1.0)  # Can double
        self.assertAlmostEqual(array[5], 7.0 / 11.0, places=3)  # Dealer upcard
        
        # Check persona encoding
        self.assertEqual(array[11], 1.0)  # Cautious persona
        self.assertEqual(array[12], 0.0)  # Not aggressive
        self.assertEqual(array[13], 0.0)  # Not intuitive
    
    def test_feature_size(self):
        """Test feature size property."""
        state = RLState(
            player_value=10,
            player_soft=False,
            player_pairs=False,
            player_can_double=False,
            player_can_split=False,
            dealer_upcard=5,
            dealer_soft=False,
            deck_penetration=0.3,
            bet_amount=5.0
        )
        
        self.assertEqual(state.feature_size, 18)
        self.assertEqual(len(state.to_array()), state.feature_size)


class TestRLReward(unittest.TestCase):
    """Test cases for RLReward class."""
    
    def test_reward_creation(self):
        """Test reward creation and validation."""
        components = {
            RLRewardType.GAME_OUTCOME: 10.0,
            RLRewardType.DETECTION_PENALTY: -2.0,
            RLRewardType.PERSONA_BONUS: 1.0
        }
        
        reward = RLReward(total=9.0, components=components)
        
        self.assertEqual(reward.total, 9.0)
        self.assertEqual(len(reward.components), 3)
        self.assertEqual(reward.components[RLRewardType.GAME_OUTCOME], 10.0)
    
    def test_reward_auto_calculation(self):
        """Test automatic total calculation."""
        components = {
            RLRewardType.GAME_OUTCOME: 5.0,
            RLRewardType.DETECTION_PENALTY: -1.0
        }
        
        # Total doesn't match sum - should be corrected
        reward = RLReward(total=10.0, components=components)
        
        self.assertEqual(reward.total, 4.0)  # Should be corrected to sum


class TestBlackjackRLEnvironment(unittest.TestCase):
    """Test cases for BlackjackRLEnvironment class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.env = BlackjackRLEnvironment(num_decks=6)
        self.persona_env = BlackjackRLEnvironment(
            num_decks=6, 
            persona=CautiousPersona(),
            detection_penalty=-5.0
        )
    
    def test_environment_initialization(self):
        """Test environment initialization."""
        self.assertIsNotNone(self.env.game)
        self.assertEqual(self.env.detection_penalty, -10.0)
        self.assertEqual(self.env.consistency_threshold, 0.95)
        self.assertIsNone(self.env.persona)
        
        # Test persona environment
        self.assertIsNotNone(self.persona_env.persona)
        self.assertEqual(self.persona_env.detection_penalty, -5.0)
    
    def test_environment_reset(self):
        """Test environment reset."""
        state = self.env.reset(bet_amount=15.0)
        
        # Should return RLState
        self.assertIsInstance(state, RLState)
        
        # Should have reset episode tracking
        self.assertEqual(self.env.episode_step, 0)
        self.assertEqual(self.env.total_reward, 0.0)
        
        # Should have valid game state
        self.assertIsNotNone(self.env.game_state)
        self.assertEqual(self.env.game_state.bet_amount, 15.0)
    
    def test_environment_step(self):
        """Test environment step function."""
        # Reset environment
        initial_state = self.env.reset()
        
        # Take action
        action = GameAction.HIT
        next_state, reward, done, info = self.env.step(action)
        
        # Check return types
        self.assertIsInstance(next_state, RLState)
        self.assertIsInstance(reward, RLReward)
        self.assertIsInstance(done, bool)
        self.assertIsInstance(info, dict)
        
        # Check info structure
        self.assertIn('episode_step', info)
        self.assertIn('game_state', info)
        self.assertIn('available_actions', info)
        self.assertIn('episode_stats', info)
        
        # Episode step should increment
        self.assertEqual(self.env.episode_step, 1)
    
    def test_reward_calculation(self):
        """Test reward calculation."""
        # Reset environment
        self.env.reset()
        
        # Test step reward (should be small negative)
        _, reward, done, _ = self.env.step(GameAction.STAND)
        
        self.assertIsInstance(reward, RLReward)
        self.assertIn(RLRewardType.GAME_OUTCOME, reward.components)
        
        # If not done, should have small negative reward
        if not done:
            self.assertEqual(reward.components[RLRewardType.GAME_OUTCOME], -0.01)
    
    def test_game_outcome_rewards(self):
        """Test game outcome reward calculation."""
        # Test different outcomes
        test_cases = [
            ("blackjack", 15.0),
            ("win", 10.0),
            ("push", 0.0),
            ("loss", -10.0),
            ("bust", -10.0)
        ]
        
        for outcome, expected_reward in test_cases:
            reward = self.env._calculate_game_reward()
            # Mock the game state result
            self.env.game_state.results = [outcome]
            actual_reward = self.env._calculate_game_reward()
            
            if outcome == "blackjack":
                self.assertEqual(actual_reward, 15.0)
            elif outcome == "win":
                self.assertEqual(actual_reward, 10.0)
            elif outcome == "push":
                self.assertEqual(actual_reward, 0.0)
            elif outcome in ["loss", "bust"]:
                self.assertEqual(actual_reward, -10.0)
    
    def test_detection_penalty(self):
        """Test detection penalty calculation."""
        # Add consistent decisions to trigger penalty
        for i in range(15):
            self.env.decision_history.append({
                'player_value': 16,
                'dealer_upcard': 10,
                'action': 'S',  # Always stand
                'episode_step': i
            })
        
        # Calculate consistency (should be high)
        consistency = self.env._calculate_current_consistency()
        self.assertGreater(consistency, 0.9)
        
        # Reset and test penalty
        self.env.reset()
        
        # Mock high consistency
        self.env.decision_history = [
            {'player_value': 16, 'dealer_upcard': 10, 'action': 'S', 'episode_step': i}
            for i in range(10)
        ]
        
        # Take action that should trigger penalty
        _, reward, _, _ = self.env.step(GameAction.STAND)
        
        # Should have detection penalty if consistency is high
        consistency = self.env._calculate_current_consistency()
        if consistency >= self.env.consistency_threshold:
            self.assertIn(RLRewardType.DETECTION_PENALTY, reward.components)
    
    def test_persona_bonus(self):
        """Test persona bonus calculation."""
        # Test with persona environment
        self.persona_env.reset()
        
        # Mock persona action alignment
        bonus = self.persona_env._calculate_persona_bonus(GameAction.STAND)
        
        # Should return some bonus value (0 or positive)
        self.assertGreaterEqual(bonus, 0.0)
    
    def test_exploration_bonus(self):
        """Test exploration bonus calculation."""
        # Test early exploration bonus
        bonus = self.env._calculate_exploration_bonus(GameAction.HIT)
        self.assertEqual(bonus, 0.1)  # Early exploration bonus
        
        # Add diverse decisions
        for action in ['H', 'S', 'D', 'P']:
            self.env.decision_history.append({
                'player_value': 10,
                'dealer_upcard': 5,
                'action': action,
                'episode_step': len(self.env.decision_history)
            })
        
        # Should get diversity bonus
        bonus = self.env._calculate_exploration_bonus(GameAction.HIT)
        self.assertGreaterEqual(bonus, 0.0)
    
    def test_consistency_calculation(self):
        """Test consistency calculation."""
        # Test with no history
        consistency = self.env._calculate_current_consistency()
        self.assertEqual(consistency, 0.0)
        
        # Add consistent decisions
        for i in range(12):
            self.env.decision_history.append({
                'player_value': 16,
                'dealer_upcard': 10,
                'action': 'S',  # Always stand
                'episode_step': i
            })
        
        consistency = self.env._calculate_current_consistency()
        self.assertEqual(consistency, 1.0)  # Perfect consistency
        
        # Add some variation
        self.env.decision_history.append({
            'player_value': 16,
            'dealer_upcard': 10,
            'action': 'H',  # Different action
            'episode_step': 12
        })
        
        consistency = self.env._calculate_current_consistency()
        self.assertLess(consistency, 1.0)  # Should be less consistent
    
    def test_decision_tracking(self):
        """Test decision tracking."""
        # Reset and create state
        state = self.env.reset()
        
        # Track decision
        self.env.track_decision(GameAction.HIT)
        
        # Should have recorded decision
        self.assertEqual(len(self.env.decision_history), 1)
        
        decision = self.env.decision_history[0]
        self.assertEqual(decision['action'], 'H')
        self.assertIn('player_value', decision)
        self.assertIn('dealer_upcard', decision)
    
    def test_available_actions(self):
        """Test available actions."""
        self.env.reset()
        actions = self.env.get_available_actions()
        
        # Should return list of GameActions
        self.assertIsInstance(actions, list)
        for action in actions:
            self.assertIsInstance(action, GameAction)
    
    def test_episode_stats(self):
        """Test episode statistics tracking."""
        self.env.reset()
        
        # Take some actions
        for _ in range(3):
            _, reward, done, _ = self.env.step(GameAction.HIT)
            if done:
                break
        
        # Get episode stats
        stats = self.env.get_episode_stats()
        
        self.assertIn('hands_played', stats)
        self.assertIn('total_reward', stats)
        self.assertIn('game_rewards', stats)
        self.assertIn('detection_penalties', stats)
    
    def test_persona_switching(self):
        """Test persona switching functionality."""
        # Start without persona
        self.assertIsNone(self.env.persona)
        self.assertEqual(self.env.hands_since_switch, 0)
        
        # Set persona
        persona = CautiousPersona()
        self.env.set_persona(persona)
        
        self.assertEqual(self.env.persona, persona)
        self.assertEqual(self.env.hands_since_switch, 0)
        
        # Remove persona
        self.env.set_persona(None)
        self.assertIsNone(self.env.persona)
        self.assertEqual(self.env.hands_since_switch, 1)


if __name__ == '__main__':
    unittest.main()
