"""
Basic Strategy charts for 6-deck H17 DAS blackjack.

This module provides complete Basic Strategy lookup tables for:
- Hard hands (no Aces or Aces counted as 1)
- Soft hands (Ace counted as 11)
- Pairs (splitting decisions)

Rules: 6 decks, Dealer hits soft 17, Double after split allowed
"""

from enum import Enum
from typing import Dict, Tu<PERSON>, Optional
import json
import os


class BasicStrategyAction(Enum):
    """Basic Strategy actions."""
    HIT = "H"
    STAND = "S"
    DOUBLE = "D"
    DOUBLE_OR_HIT = "Dh"  # Double if allowed, otherwise hit
    SPLIT = "P"
    SPLIT_OR_HIT = "Ph"  # Split if allowed, otherwise hit
    SURRENDER = "R"
    SURRENDER_OR_HIT = "Rh"  # Surrender if allowed, otherwise hit


class BasicStrategyCharts:
    """
    Complete Basic Strategy charts for 6-deck H17 DAS blackjack.
    
    Provides lookup functions for optimal play decisions based on:
    - Player hand value/composition
    - Dealer upcard
    - Game rules and constraints
    """
    
    def __init__(self):
        """Initialize Basic Strategy charts."""
        self._hard_totals = self._create_hard_totals_chart()
        self._soft_totals = self._create_soft_totals_chart()
        self._pairs = self._create_pairs_chart()
    
    def _create_hard_totals_chart(self) -> Dict[Tuple[int, int], BasicStrategyAction]:
        """
        Create hard totals Basic Strategy chart.
        
        Returns:
            Dictionary mapping (player_total, dealer_upcard) to action
        """
        chart = {}
        
        # Hard totals chart for 6-deck H17 DAS
        # Format: (player_total, dealer_upcard) -> action
        
        # 5-8: Always hit
        for total in range(5, 9):
            for dealer_up in range(2, 12):  # 2-10, A(11)
                chart[(total, dealer_up)] = BasicStrategyAction.HIT
        
        # 9: Double vs 3-6, otherwise hit
        for dealer_up in range(2, 12):
            if 3 <= dealer_up <= 6:
                chart[(9, dealer_up)] = BasicStrategyAction.DOUBLE_OR_HIT
            else:
                chart[(9, dealer_up)] = BasicStrategyAction.HIT
        
        # 10: Double vs 2-9, otherwise hit
        for dealer_up in range(2, 12):
            if 2 <= dealer_up <= 9:
                chart[(10, dealer_up)] = BasicStrategyAction.DOUBLE_OR_HIT
            else:
                chart[(10, dealer_up)] = BasicStrategyAction.HIT
        
        # 11: Double vs 2-10, hit vs A
        for dealer_up in range(2, 12):
            if 2 <= dealer_up <= 10:
                chart[(11, dealer_up)] = BasicStrategyAction.DOUBLE_OR_HIT
            else:  # vs A
                chart[(11, dealer_up)] = BasicStrategyAction.HIT
        
        # 12: Stand vs 4-6, otherwise hit
        for dealer_up in range(2, 12):
            if 4 <= dealer_up <= 6:
                chart[(12, dealer_up)] = BasicStrategyAction.STAND
            else:
                chart[(12, dealer_up)] = BasicStrategyAction.HIT
        
        # 13-16: Stand vs 2-6, otherwise hit
        for total in range(13, 17):
            for dealer_up in range(2, 12):
                if 2 <= dealer_up <= 6:
                    chart[(total, dealer_up)] = BasicStrategyAction.STAND
                else:
                    chart[(total, dealer_up)] = BasicStrategyAction.HIT
        
        # 17-21: Always stand
        for total in range(17, 22):
            for dealer_up in range(2, 12):
                chart[(total, dealer_up)] = BasicStrategyAction.STAND
        
        return chart
    
    def _create_soft_totals_chart(self) -> Dict[Tuple[int, int], BasicStrategyAction]:
        """
        Create soft totals Basic Strategy chart.
        
        Returns:
            Dictionary mapping (soft_total, dealer_upcard) to action
        """
        chart = {}
        
        # Soft totals chart for 6-deck H17 DAS
        # Format: (soft_total, dealer_upcard) -> action
        
        # A,2 (soft 13): Double vs 5-6, otherwise hit
        for dealer_up in range(2, 12):
            if 5 <= dealer_up <= 6:
                chart[(13, dealer_up)] = BasicStrategyAction.DOUBLE_OR_HIT
            else:
                chart[(13, dealer_up)] = BasicStrategyAction.HIT
        
        # A,3 (soft 14): Double vs 5-6, otherwise hit
        for dealer_up in range(2, 12):
            if 5 <= dealer_up <= 6:
                chart[(14, dealer_up)] = BasicStrategyAction.DOUBLE_OR_HIT
            else:
                chart[(14, dealer_up)] = BasicStrategyAction.HIT
        
        # A,4 (soft 15): Double vs 4-6, otherwise hit
        for dealer_up in range(2, 12):
            if 4 <= dealer_up <= 6:
                chart[(15, dealer_up)] = BasicStrategyAction.DOUBLE_OR_HIT
            else:
                chart[(15, dealer_up)] = BasicStrategyAction.HIT
        
        # A,5 (soft 16): Double vs 4-6, otherwise hit
        for dealer_up in range(2, 12):
            if 4 <= dealer_up <= 6:
                chart[(16, dealer_up)] = BasicStrategyAction.DOUBLE_OR_HIT
            else:
                chart[(16, dealer_up)] = BasicStrategyAction.HIT
        
        # A,6 (soft 17): Double vs 3-6, otherwise hit
        for dealer_up in range(2, 12):
            if 3 <= dealer_up <= 6:
                chart[(17, dealer_up)] = BasicStrategyAction.DOUBLE_OR_HIT
            else:
                chart[(17, dealer_up)] = BasicStrategyAction.HIT
        
        # A,7 (soft 18): Double vs 3-6, stand vs 2,7,8, hit vs 9,10,A
        for dealer_up in range(2, 12):
            if 3 <= dealer_up <= 6:
                chart[(18, dealer_up)] = BasicStrategyAction.DOUBLE_OR_HIT
            elif dealer_up in [2, 7, 8]:
                chart[(18, dealer_up)] = BasicStrategyAction.STAND
            else:  # 9, 10, A
                chart[(18, dealer_up)] = BasicStrategyAction.HIT
        
        # A,8 (soft 19): Always stand
        for dealer_up in range(2, 12):
            chart[(19, dealer_up)] = BasicStrategyAction.STAND
        
        # A,9 (soft 20): Always stand
        for dealer_up in range(2, 12):
            chart[(20, dealer_up)] = BasicStrategyAction.STAND
        
        # A,10 (soft 21/blackjack): Always stand
        for dealer_up in range(2, 12):
            chart[(21, dealer_up)] = BasicStrategyAction.STAND
        
        return chart
    
    def _create_pairs_chart(self) -> Dict[Tuple[str, int], BasicStrategyAction]:
        """
        Create pairs Basic Strategy chart.
        
        Returns:
            Dictionary mapping (pair_rank, dealer_upcard) to action
        """
        chart = {}
        
        # Pairs chart for 6-deck H17 DAS
        # Format: (pair_rank, dealer_upcard) -> action
        
        # A,A: Always split
        for dealer_up in range(2, 12):
            chart[("A", dealer_up)] = BasicStrategyAction.SPLIT
        
        # 2,2: Split vs 2-7, otherwise hit
        for dealer_up in range(2, 12):
            if 2 <= dealer_up <= 7:
                chart[("2", dealer_up)] = BasicStrategyAction.SPLIT
            else:
                chart[("2", dealer_up)] = BasicStrategyAction.HIT
        
        # 3,3: Split vs 2-7, otherwise hit
        for dealer_up in range(2, 12):
            if 2 <= dealer_up <= 7:
                chart[("3", dealer_up)] = BasicStrategyAction.SPLIT
            else:
                chart[("3", dealer_up)] = BasicStrategyAction.HIT
        
        # 4,4: Hit vs all (don't split)
        for dealer_up in range(2, 12):
            chart[("4", dealer_up)] = BasicStrategyAction.HIT
        
        # 5,5: Never split, treat as 10 (double vs 2-9, hit vs 10,A)
        for dealer_up in range(2, 12):
            if 2 <= dealer_up <= 9:
                chart[("5", dealer_up)] = BasicStrategyAction.DOUBLE_OR_HIT
            else:
                chart[("5", dealer_up)] = BasicStrategyAction.HIT
        
        # 6,6: Split vs 2-6, otherwise hit
        for dealer_up in range(2, 12):
            if 2 <= dealer_up <= 6:
                chart[("6", dealer_up)] = BasicStrategyAction.SPLIT
            else:
                chart[("6", dealer_up)] = BasicStrategyAction.HIT
        
        # 7,7: Split vs 2-7, otherwise hit
        for dealer_up in range(2, 12):
            if 2 <= dealer_up <= 7:
                chart[("7", dealer_up)] = BasicStrategyAction.SPLIT
            else:
                chart[("7", dealer_up)] = BasicStrategyAction.HIT
        
        # 8,8: Always split
        for dealer_up in range(2, 12):
            chart[("8", dealer_up)] = BasicStrategyAction.SPLIT
        
        # 9,9: Split vs 2-9 except 7, stand vs 7,10,A
        for dealer_up in range(2, 12):
            if dealer_up in [2, 3, 4, 5, 6, 8, 9]:
                chart[("9", dealer_up)] = BasicStrategyAction.SPLIT
            else:  # 7, 10, A
                chart[("9", dealer_up)] = BasicStrategyAction.STAND
        
        # 10,10: Never split, always stand
        for dealer_up in range(2, 12):
            chart[("10", dealer_up)] = BasicStrategyAction.STAND
        
        return chart

    def get_action(self, player_hand, dealer_upcard: int, can_double: bool = True,
                   can_split: bool = True) -> BasicStrategyAction:
        """
        Get the optimal Basic Strategy action for a given situation.

        Args:
            player_hand: Hand object representing player's cards
            dealer_upcard: Dealer's upcard value (2-11, where 11 = Ace)
            can_double: Whether doubling is allowed
            can_split: Whether splitting is allowed

        Returns:
            The optimal Basic Strategy action
        """
        # Convert Ace to 11 for lookup
        if dealer_upcard == 1:
            dealer_upcard = 11

        # Check for pairs first
        if player_hand.is_pair() and can_split:
            pair_rank = self._get_pair_rank(player_hand)
            if pair_rank and (pair_rank, dealer_upcard) in self._pairs:
                action = self._pairs[(pair_rank, dealer_upcard)]
                if action == BasicStrategyAction.SPLIT:
                    return action

        # Check for soft hands
        if player_hand.is_soft():
            soft_total = player_hand.get_value()
            if (soft_total, dealer_upcard) in self._soft_totals:
                action = self._soft_totals[(soft_total, dealer_upcard)]
                return self._resolve_conditional_action(action, can_double)

        # Hard hands
        hard_total = player_hand.get_value()
        if (hard_total, dealer_upcard) in self._hard_totals:
            action = self._hard_totals[(hard_total, dealer_upcard)]
            return self._resolve_conditional_action(action, can_double)

        # Default to hit if no specific rule found
        return BasicStrategyAction.HIT

    def _get_pair_rank(self, hand) -> Optional[str]:
        """Get the rank string for a pair."""
        if not hand.is_pair():
            return None

        rank = hand.cards[0].rank
        if rank.symbol == "A":
            return "A"
        elif rank.symbol in ["J", "Q", "K"]:
            return "10"
        else:
            return rank.symbol

    def _resolve_conditional_action(self, action: BasicStrategyAction,
                                  can_double: bool) -> BasicStrategyAction:
        """Resolve conditional actions based on game constraints."""
        if action == BasicStrategyAction.DOUBLE_OR_HIT:
            return BasicStrategyAction.DOUBLE if can_double else BasicStrategyAction.HIT
        elif action == BasicStrategyAction.SURRENDER_OR_HIT:
            # Surrender not implemented yet, default to hit
            return BasicStrategyAction.HIT
        else:
            return action

    def save_to_file(self, filepath: str) -> None:
        """Save Basic Strategy charts to JSON file."""
        data = {
            "hard_totals": {f"{k[0]},{k[1]}": v.value for k, v in self._hard_totals.items()},
            "soft_totals": {f"{k[0]},{k[1]}": v.value for k, v in self._soft_totals.items()},
            "pairs": {f"{k[0]},{k[1]}": v.value for k, v in self._pairs.items()}
        }

        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)

    @classmethod
    def load_from_file(cls, filepath: str) -> 'BasicStrategyCharts':
        """Load Basic Strategy charts from JSON file."""
        with open(filepath, 'r') as f:
            data = json.load(f)

        charts = cls.__new__(cls)  # Create instance without calling __init__

        # Parse hard totals
        charts._hard_totals = {}
        for key_str, action_str in data["hard_totals"].items():
            total, dealer = map(int, key_str.split(','))
            charts._hard_totals[(total, dealer)] = BasicStrategyAction(action_str)

        # Parse soft totals
        charts._soft_totals = {}
        for key_str, action_str in data["soft_totals"].items():
            total, dealer = map(int, key_str.split(','))
            charts._soft_totals[(total, dealer)] = BasicStrategyAction(action_str)

        # Parse pairs
        charts._pairs = {}
        for key_str, action_str in data["pairs"].items():
            rank, dealer = key_str.split(',')
            dealer = int(dealer)
            charts._pairs[(rank, dealer)] = BasicStrategyAction(action_str)

        return charts
