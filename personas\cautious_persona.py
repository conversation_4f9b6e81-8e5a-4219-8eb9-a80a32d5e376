"""
Cautious Basic Strategy Persona for BlackJack Bot ML.

This persona represents a careful, methodical player who:
- Follows Basic Strategy with 95% accuracy
- Takes longer to make decisions (slower, more deliberate)
- Tends toward conservative play when uncertain
- Rarely makes aggressive moves
"""

import random
from .base_persona import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>fig, <PERSON><PERSON><PERSON><PERSON>, Decision<PERSON>ontext, ErrorType
from core.hand import Hand
from core.game_logic import GameState
from utils.basic_strategy_charts import BasicStrategyAction


def create_cautious_persona_config() -> PersonaConfig:
    """Create configuration for the Cautious Basic Strategy persona."""
    
    decision_pattern = DecisionPattern(
        base_accuracy=0.95,  # High accuracy - follows BS closely
        decision_speed=0.7,  # Slower decisions - more deliberate
        consistency=0.95,    # Very consistent
        aggression_bias=-0.3,  # Conservative bias
        risk_tolerance=0.3,  # Low risk tolerance
        emotional_volatility=0.05,  # Very stable emotionally
        fatigue_rate=0.0005,  # Slow fatigue accumulation
        
        # Timing parameters - slower, more deliberate
        min_decision_time=1.0,
        max_decision_time=12.0,
        avg_decision_time=4.5,
        timing_variance=0.2  # Low variance - consistent timing
    )
    
    # Error patterns - mostly strategy deviations toward conservative play
    error_patterns = {
        ErrorType.STRATEGY_DEVIATION: 0.04,  # 4% strategy errors
        ErrorType.TIMING_INCONSISTENCY: 0.05,  # Occasional timing variation
        ErrorType.EMOTIONAL_DECISION: 0.005,  # Very rare emotional decisions
        ErrorType.FATIGUE_ERROR: 0.003,  # Rare fatigue errors
        ErrorType.DISTRACTION_ERROR: 0.002  # Very rare distractions
    }
    
    # Context modifiers - less affected by pressure
    context_modifiers = {
        DecisionContext.PRESSURE: {
            "accuracy_modifier": -0.05,  # Slight accuracy drop under pressure
            "speed_modifier": 1.2,  # Slightly slower when pressured
            "aggression_modifier": -0.1  # Even more conservative
        },
        DecisionContext.CONFIDENT: {
            "accuracy_modifier": 0.02,  # Slight improvement when confident
            "speed_modifier": 0.9,  # Slightly faster
            "aggression_modifier": 0.05  # Slightly less conservative
        },
        DecisionContext.TIRED: {
            "accuracy_modifier": -0.08,  # More affected by fatigue
            "speed_modifier": 1.3,  # Slower when tired
            "aggression_modifier": -0.15  # Much more conservative
        },
        DecisionContext.DISTRACTED: {
            "accuracy_modifier": -0.1,  # Significantly affected by distractions
            "speed_modifier": 1.4,  # Much slower when distracted
            "aggression_modifier": -0.05  # Slightly more conservative
        }
    }
    
    return PersonaConfig(
        name="Cautious Basic Strategy",
        description="A careful, methodical player who follows Basic Strategy with high accuracy but tends toward conservative play",
        decision_pattern=decision_pattern,
        error_patterns=error_patterns,
        context_modifiers=context_modifiers
    )


class CautiousPersona(BasePersona):
    """
    Cautious Basic Strategy persona implementation.
    
    This persona represents a player who:
    - Studies Basic Strategy carefully and follows it closely
    - Takes time to think through decisions
    - Errs on the side of caution when uncertain
    - Avoids risky plays even when mathematically correct
    """
    
    def __init__(self):
        """Initialize the Cautious persona."""
        config = create_cautious_persona_config()
        super().__init__(config)
    
    def _apply_persona_bias(self, optimal_action: BasicStrategyAction,
                          player_hand: Hand, dealer_upcard: int,
                          game_state: GameState) -> BasicStrategyAction:
        """
        Apply cautious bias to decision making.
        
        The cautious persona tends to:
        - Stand instead of hit on borderline decisions
        - Hit instead of double on marginal doubles
        - Avoid splitting in uncertain situations
        """
        hand_value = player_hand.get_value()
        
        # Conservative bias on borderline hitting decisions
        if optimal_action == BasicStrategyAction.HIT:
            # Sometimes stand on 12-16 vs weak dealer (overly conservative)
            if (hand_value in [12, 13, 14, 15, 16] and 
                dealer_upcard in [4, 5, 6] and 
                random.random() < 0.1):  # 10% chance to be overly conservative
                return BasicStrategyAction.STAND
        
        # Conservative bias on doubling decisions
        elif optimal_action == BasicStrategyAction.DOUBLE:
            # Sometimes just hit instead of double (risk aversion)
            if random.random() < 0.15:  # 15% chance to avoid doubling
                return BasicStrategyAction.HIT
            
            # Especially conservative on soft doubles
            if player_hand.is_soft() and random.random() < 0.25:
                return BasicStrategyAction.HIT
        
        # Conservative bias on splitting decisions
        elif optimal_action == BasicStrategyAction.SPLIT:
            # Sometimes avoid splitting (complexity aversion)
            if random.random() < 0.1:  # 10% chance to avoid splitting
                # Treat as regular hand
                if hand_value <= 11:
                    return BasicStrategyAction.HIT
                elif hand_value >= 17:
                    return BasicStrategyAction.STAND
                else:
                    # Use basic strategy for the hand value
                    return self._get_conservative_action(hand_value, dealer_upcard)
        
        # Apply additional conservative bias based on context
        if self.current_context == DecisionContext.PRESSURE:
            return self._apply_pressure_conservatism(optimal_action, player_hand, dealer_upcard)
        
        return optimal_action
    
    def _get_conservative_action(self, hand_value: int, dealer_upcard: int) -> BasicStrategyAction:
        """Get a conservative action for a given hand value."""
        # Conservative decision matrix
        if hand_value <= 11:
            return BasicStrategyAction.HIT
        elif hand_value >= 17:
            return BasicStrategyAction.STAND
        elif hand_value in [12, 13, 14, 15, 16]:
            # Conservative: stand vs weak dealer, hit vs strong dealer
            if dealer_upcard in [2, 3, 4, 5, 6]:
                return BasicStrategyAction.STAND
            else:
                return BasicStrategyAction.HIT
        else:
            return BasicStrategyAction.STAND
    
    def _apply_pressure_conservatism(self, optimal_action: BasicStrategyAction,
                                   player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """Apply extra conservative bias under pressure."""
        hand_value = player_hand.get_value()
        
        # Under pressure, be even more conservative
        if optimal_action == BasicStrategyAction.HIT and hand_value >= 12:
            # More likely to stand on stiff hands
            if dealer_upcard in [2, 3, 4, 5, 6] and random.random() < 0.3:
                return BasicStrategyAction.STAND
        
        elif optimal_action == BasicStrategyAction.DOUBLE:
            # Much more likely to just hit instead of double
            if random.random() < 0.4:
                return BasicStrategyAction.HIT
        
        elif optimal_action == BasicStrategyAction.SPLIT:
            # Avoid splitting under pressure
            if random.random() < 0.3:
                return self._get_conservative_action(hand_value, dealer_upcard)
        
        return optimal_action
    
    def _strategy_deviation_error(self, optimal_action: BasicStrategyAction,
                                player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """
        Generate strategy deviation errors with cautious bias.
        
        Cautious players' errors tend to be toward more conservative play.
        """
        hand_value = player_hand.get_value()
        
        # Cautious errors are typically toward conservative play
        if optimal_action == BasicStrategyAction.HIT:
            # Error: stand when should hit (fear of busting)
            if hand_value >= 12:
                return BasicStrategyAction.STAND
        
        elif optimal_action == BasicStrategyAction.DOUBLE:
            # Error: hit instead of double (risk aversion)
            return BasicStrategyAction.HIT
        
        elif optimal_action == BasicStrategyAction.SPLIT:
            # Error: don't split (complexity/risk aversion)
            return BasicStrategyAction.HIT
        
        elif optimal_action == BasicStrategyAction.STAND:
            # Rare error: hit when should stand (second-guessing)
            if hand_value in [12, 13] and random.random() < 0.3:
                return BasicStrategyAction.HIT
        
        return optimal_action
    
    def _emotional_decision_error(self, optimal_action: BasicStrategyAction,
                                player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """
        Generate emotional decision errors (rare for cautious persona).
        
        Cautious players rarely make emotional decisions, but when they do,
        they tend toward even more conservative play.
        """
        # Cautious players' emotional decisions are toward safety
        if self.consecutive_losses >= 2:
            # After losses, become even more conservative
            if optimal_action == BasicStrategyAction.HIT and player_hand.get_value() >= 12:
                return BasicStrategyAction.STAND
            elif optimal_action == BasicStrategyAction.DOUBLE:
                return BasicStrategyAction.HIT
        
        return optimal_action
