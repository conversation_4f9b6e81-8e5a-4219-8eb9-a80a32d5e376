"""
Unit tests for Basic Strategy agent.

Tests P1_T5 implementation: Perfect Basic Strategy agent implementation.
"""

import unittest
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from core.card import Card, Suit, Rank
from core.hand import Hand
from core.game_logic import BlackjackGame, GameAction, GameState
from agents.basic_strategy_agent import BasicStrategyAgent


class TestBasicStrategyAgent(unittest.TestCase):
    """Test cases for the BasicStrategyAgent class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.agent = BasicStrategyAgent("Test BS Agent")
        self.game = BlackjackGame(num_decks=1)
    
    def test_agent_initialization(self):
        """Test agent initialization."""
        self.assertEqual(self.agent.name, "Test BS Agent")
        self.assertIsNotNone(self.agent.charts)
        self.assertEqual(self.agent.bet_amount, 1.0)
        self.assertEqual(self.agent.get_strategy_accuracy(None), 1.0)
    
    def test_flat_betting(self):
        """Test flat betting behavior."""
        # Default bet
        bet = self.agent.get_bet_amount()
        self.assertEqual(bet, 1.0)
        
        # Respect min/max constraints
        bet = self.agent.get_bet_amount(min_bet=5.0, max_bet=100.0)
        self.assertEqual(bet, 5.0)
        
        # Set custom bet amount
        self.agent.set_bet_amount(10.0)
        bet = self.agent.get_bet_amount(min_bet=1.0, max_bet=100.0)
        self.assertEqual(bet, 10.0)
    
    def test_basic_strategy_decisions(self):
        """Test that agent makes correct Basic Strategy decisions."""
        # Create a controlled game state
        game_state = GameState()
        
        # Test hard 16 vs dealer 10 (should hit)
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        player_hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        dealer_hand.add_card(Card(Suit.DIAMONDS, Rank.FIVE))
        
        game_state.player_hands = [player_hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        action = self.agent.get_action(game_state)
        self.assertEqual(action, GameAction.HIT)
    
    def test_doubling_decisions(self):
        """Test doubling decisions."""
        game_state = GameState()
        
        # Test hard 11 vs dealer 6 (should double)
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.FIVE))
        player_hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SIX))
        dealer_hand.add_card(Card(Suit.DIAMONDS, Rank.FIVE))
        
        game_state.player_hands = [player_hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [True]
        game_state.can_split = [False]
        
        action = self.agent.get_action(game_state)
        self.assertEqual(action, GameAction.DOUBLE)
        
        # Same situation but can't double (should hit)
        game_state.can_double = [False]
        action = self.agent.get_action(game_state)
        self.assertEqual(action, GameAction.HIT)
    
    def test_splitting_decisions(self):
        """Test splitting decisions."""
        game_state = GameState()
        
        # Test 8,8 vs dealer 10 (should split)
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.EIGHT))
        player_hand.add_card(Card(Suit.SPADES, Rank.EIGHT))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        dealer_hand.add_card(Card(Suit.DIAMONDS, Rank.FIVE))
        
        game_state.player_hands = [player_hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [True]
        game_state.can_split = [True]
        
        action = self.agent.get_action(game_state)
        self.assertEqual(action, GameAction.SPLIT)
    
    def test_soft_hand_decisions(self):
        """Test soft hand decisions."""
        game_state = GameState()
        
        # Test A,7 vs dealer 6 (should double)
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.ACE))
        player_hand.add_card(Card(Suit.SPADES, Rank.SEVEN))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SIX))
        dealer_hand.add_card(Card(Suit.DIAMONDS, Rank.FIVE))
        
        game_state.player_hands = [player_hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [True]
        game_state.can_split = [False]
        
        action = self.agent.get_action(game_state)
        self.assertEqual(action, GameAction.DOUBLE)
    
    def test_dealer_ace_handling(self):
        """Test proper handling of dealer Ace."""
        game_state = GameState()
        
        # Test hard 16 vs dealer A (should hit)
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        player_hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.ACE))
        dealer_hand.add_card(Card(Suit.DIAMONDS, Rank.FIVE))
        
        game_state.player_hands = [player_hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        action = self.agent.get_action(game_state)
        self.assertEqual(action, GameAction.HIT)
    
    def test_statistics_tracking(self):
        """Test statistics tracking."""
        # Initial stats
        stats = self.agent.get_stats()
        self.assertEqual(stats['hands_played'], 0)
        self.assertEqual(stats['win_rate'], 0.0)
        
        # Update with a win
        self.agent.update_stats('win', 10.0, 10.0)
        stats = self.agent.get_stats()
        self.assertEqual(stats['hands_played'], 1)
        self.assertEqual(stats['hands_won'], 1)
        self.assertEqual(stats['win_rate'], 1.0)
        self.assertEqual(stats['total_bet'], 10.0)
        self.assertEqual(stats['total_winnings'], 10.0)
        self.assertEqual(stats['return_on_investment'], 1.0)
        
        # Update with a loss
        self.agent.update_stats('loss', 10.0, -10.0)
        stats = self.agent.get_stats()
        self.assertEqual(stats['hands_played'], 2)
        self.assertEqual(stats['win_rate'], 0.5)
        self.assertEqual(stats['loss_rate'], 0.5)
        self.assertEqual(stats['total_winnings'], 0.0)
        self.assertEqual(stats['return_on_investment'], 0.0)
    
    def test_multiple_hands_support(self):
        """Test support for multiple hands (after splitting)."""
        game_state = GameState()
        
        # Create two hands (after split)
        hand1 = Hand()
        hand1.add_card(Card(Suit.HEARTS, Rank.EIGHT))
        hand1.add_card(Card(Suit.SPADES, Rank.THREE))
        
        hand2 = Hand()
        hand2.add_card(Card(Suit.CLUBS, Rank.EIGHT))
        hand2.add_card(Card(Suit.DIAMONDS, Rank.SEVEN))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.HEARTS, Rank.SIX))
        dealer_hand.add_card(Card(Suit.SPADES, Rank.FIVE))
        
        game_state.player_hands = [hand1, hand2]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [True, True]
        game_state.can_split = [False, False]
        
        # Test first hand (11 vs 6, should double)
        action1 = self.agent.get_action(game_state, hand_index=0)
        self.assertEqual(action1, GameAction.DOUBLE)
        
        # Test second hand (15 vs 6, should stand)
        action2 = self.agent.get_action(game_state, hand_index=1)
        self.assertEqual(action2, GameAction.STAND)
    
    def test_error_handling(self):
        """Test error handling for invalid inputs."""
        game_state = GameState()
        game_state.player_hands = [Hand()]
        game_state.dealer_hand = Hand()
        
        # Test invalid hand index
        with self.assertRaises(ValueError):
            self.agent.get_action(game_state, hand_index=5)
        
        # Test empty dealer hand
        with self.assertRaises(ValueError):
            self.agent.get_action(game_state, hand_index=0)
    
    def test_decision_timing(self):
        """Test decision timing characteristics."""
        decision_time = self.agent.get_decision_time()
        self.assertIsInstance(decision_time, float)
        self.assertGreater(decision_time, 0)
        self.assertLess(decision_time, 1.0)  # Should be very fast
    
    def test_charts_persistence(self):
        """Test saving and loading charts."""
        import tempfile
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_path = f.name
        
        try:
            # Save charts
            self.agent.save_charts(temp_path)
            
            # Create new agent with saved charts
            new_agent = BasicStrategyAgent("Test Agent 2", charts_file=temp_path)
            
            # Both agents should make same decisions
            game_state = GameState()
            
            player_hand = Hand()
            player_hand.add_card(Card(Suit.HEARTS, Rank.TEN))
            player_hand.add_card(Card(Suit.SPADES, Rank.SIX))
            
            dealer_hand = Hand()
            dealer_hand.add_card(Card(Suit.CLUBS, Rank.SIX))
            dealer_hand.add_card(Card(Suit.DIAMONDS, Rank.FIVE))
            
            game_state.player_hands = [player_hand]
            game_state.dealer_hand = dealer_hand
            game_state.can_double = [False]
            game_state.can_split = [False]
            
            action1 = self.agent.get_action(game_state)
            action2 = new_agent.get_action(game_state)
            self.assertEqual(action1, action2)
            
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)


if __name__ == '__main__':
    unittest.main()
