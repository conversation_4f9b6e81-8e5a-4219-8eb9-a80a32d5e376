["tests/test_rl/test_base_rl_agent.py::TestBaseRLAgent::test_action_conversion", "tests/test_rl/test_base_rl_agent.py::TestBaseRLAgent::test_agent_initialization", "tests/test_rl/test_base_rl_agent.py::TestBaseRLAgent::test_config_validation", "tests/test_rl/test_base_rl_agent.py::TestBaseRLAgent::test_consistency_tracking", "tests/test_rl/test_base_rl_agent.py::TestBaseRLAgent::test_detection_risk_assessment", "tests/test_rl/test_base_rl_agent.py::TestBaseRLAgent::test_episode_management", "tests/test_rl/test_base_rl_agent.py::TestBaseRLAgent::test_experience_buffer", "tests/test_rl/test_base_rl_agent.py::TestBaseRLAgent::test_experience_update", "tests/test_rl/test_base_rl_agent.py::TestBaseRLAgent::test_get_action", "tests/test_rl/test_base_rl_agent.py::TestBaseRLAgent::test_persona_context_tracking", "tests/test_rl/test_base_rl_agent.py::TestBaseRLAgent::test_persona_integration", "tests/test_rl/test_base_rl_agent.py::TestBaseRLAgent::test_state_representation", "tests/test_rl/test_base_rl_agent.py::TestBaseRLAgent::test_training_metrics", "tests/test_rl/test_dqn_agent.py::TestDQNAgent::test_action_selection", "tests/test_rl/test_dqn_agent.py::TestDQNAgent::test_agent_initialization", "tests/test_rl/test_dqn_agent.py::TestDQNAgent::test_double_dqn_vs_standard", "tests/test_rl/test_dqn_agent.py::TestDQNAgent::test_epsilon_decay", "tests/test_rl/test_dqn_agent.py::TestDQNAgent::test_model_save_load", "tests/test_rl/test_dqn_agent.py::TestDQNAgent::test_model_update", "tests/test_rl/test_dqn_agent.py::TestDQNAgent::test_network_info", "tests/test_rl/test_dqn_agent.py::TestDQNAgent::test_persona_integration", "tests/test_rl/test_dqn_agent.py::TestDQNAgent::test_q_values", "tests/test_rl/test_dqn_agent.py::TestDQNAgent::test_rl_state_creation", "tests/test_rl/test_dqn_agent.py::TestDQNAgent::test_state_representation", "tests/test_rl/test_dqn_agent.py::TestDQNAgent::test_target_network_update", "tests/test_rl/test_dqn_agent.py::TestDQNAgent::test_training_mode", "tests/test_rl/test_dqn_agent.py::TestDQNNetwork::test_activation_functions", "tests/test_rl/test_dqn_agent.py::TestDQNNetwork::test_dueling_vs_standard_dqn", "tests/test_rl/test_dqn_agent.py::TestDQNNetwork::test_network_forward_pass", "tests/test_rl/test_dqn_agent.py::TestDQNNetwork::test_network_initialization", "tests/test_rl/test_evasion_strategies.py::TestBehavioralNoiseStrategy::test_apply_evasion", "tests/test_rl/test_evasion_strategies.py::TestBehavioralNoiseStrategy::test_noise_decay", "tests/test_rl/test_evasion_strategies.py::TestBehavioralNoiseStrategy::test_should_activate", "tests/test_rl/test_evasion_strategies.py::TestBehavioralNoiseStrategy::test_strategy_initialization", "tests/test_rl/test_evasion_strategies.py::TestEvasionConfig::test_config_creation", "tests/test_rl/test_evasion_strategies.py::TestEvasionConfig::test_config_customization", "tests/test_rl/test_evasion_strategies.py::TestEvasionManager::test_detection_risk_assessment", "tests/test_rl/test_evasion_strategies.py::TestEvasionManager::test_evasion_application", "tests/test_rl/test_evasion_strategies.py::TestEvasionManager::test_manager_initialization", "tests/test_rl/test_evasion_strategies.py::TestEvasionManager::test_metrics_tracking", "tests/test_rl/test_evasion_strategies.py::TestEvasionManager::test_strategy_stats", "tests/test_rl/test_evasion_strategies.py::TestEvasiveDQNAgent::test_action_with_evasion", "tests/test_rl/test_evasion_strategies.py::TestEvasiveDQNAgent::test_agent_initialization", "tests/test_rl/test_evasion_strategies.py::TestEvasiveDQNAgent::test_comprehensive_stats", "tests/test_rl/test_evasion_strategies.py::TestEvasiveDQNAgent::test_persona_integration", "tests/test_rl/test_evasion_strategies.py::TestEvasiveDQNAgent::test_reward_adjustment", "tests/test_rl/test_evasion_strategies.py::TestPersonaSwitchingStrategy::test_activation_conditions", "tests/test_rl/test_evasion_strategies.py::TestPersonaSwitchingStrategy::test_persona_switching", "tests/test_rl/test_evasion_strategies.py::TestPersonaSwitchingStrategy::test_strategy_initialization", "tests/test_rl/test_evasion_strategies.py::TestTimingVariationStrategy::test_always_activates", "tests/test_rl/test_evasion_strategies.py::TestTimingVariationStrategy::test_timing_history_tracking", "tests/test_rl/test_evasion_strategies.py::TestTimingVariationStrategy::test_timing_variation", "tests/test_rl/test_rl_environment.py::TestBlackjackRLEnvironment::test_available_actions", "tests/test_rl/test_rl_environment.py::TestBlackjackRLEnvironment::test_consistency_calculation", "tests/test_rl/test_rl_environment.py::TestBlackjackRLEnvironment::test_decision_tracking", "tests/test_rl/test_rl_environment.py::TestBlackjackRLEnvironment::test_detection_penalty", "tests/test_rl/test_rl_environment.py::TestBlackjackRLEnvironment::test_environment_initialization", "tests/test_rl/test_rl_environment.py::TestBlackjackRLEnvironment::test_environment_reset", "tests/test_rl/test_rl_environment.py::TestBlackjackRLEnvironment::test_environment_step", "tests/test_rl/test_rl_environment.py::TestBlackjackRLEnvironment::test_episode_stats", "tests/test_rl/test_rl_environment.py::TestBlackjackRLEnvironment::test_exploration_bonus", "tests/test_rl/test_rl_environment.py::TestBlackjackRLEnvironment::test_game_outcome_rewards", "tests/test_rl/test_rl_environment.py::TestBlackjackRLEnvironment::test_persona_bonus", "tests/test_rl/test_rl_environment.py::TestBlackjackRLEnvironment::test_persona_switching", "tests/test_rl/test_rl_environment.py::TestBlackjackRLEnvironment::test_reward_calculation", "tests/test_rl/test_rl_environment.py::TestRLReward::test_reward_auto_calculation", "tests/test_rl/test_rl_environment.py::TestRLReward::test_reward_creation", "tests/test_rl/test_rl_environment.py::TestRLState::test_feature_size", "tests/test_rl/test_rl_environment.py::TestRLState::test_rl_state_creation"]