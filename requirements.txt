# BlackJack Bot ML - Requirements
# 
# Core dependencies for Phase 1 (Basic Strategy Implementation)
# No external dependencies required - uses only Python standard library
#
# Optional dependencies for enhanced functionality:

# For data analysis and visualization (optional)
# numpy>=1.21.0
# matplotlib>=3.5.0
# pandas>=1.3.0

# For performance monitoring (optional)
# psutil>=5.8.0

# For Jupyter notebook analysis (optional)
# jupyter>=1.0.0
# ipykernel>=6.0.0

# For future ML phases (Phase 3)
# tensorflow>=2.8.0
# torch>=1.11.0
# scikit-learn>=1.0.0

# For testing and development
# pytest>=6.2.0
# pytest-cov>=3.0.0

# Note: Phase 1 implementation requires only Python 3.8+ standard library
# All optional dependencies can be installed as needed:
# pip install numpy matplotlib pandas  # For data analysis
# pip install psutil                   # For performance monitoring
# pip install jupyter                  # For notebook analysis
# pip install tensorflow torch         # For future ML phases
# pip install pytest pytest-cov       # For testing
