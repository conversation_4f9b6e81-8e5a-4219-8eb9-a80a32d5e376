# BlackJack Bot ML - Requirements
#
# Dependencies organized by project phase for clarity
# Python 3.8+ required

# =============================================================================
# PHASE 1: Basic Strategy Foundation
# =============================================================================
# Core Phase 1 uses only Python standard library - no external dependencies

# =============================================================================
# PHASE 2: Human Persona Simulation
# =============================================================================
# Core Phase 2 uses only Python standard library - no external dependencies

# =============================================================================
# PHASE 3: Reinforcement Learning with Evasion
# =============================================================================

# Core numerical computing
numpy>=1.21.0,<2.0.0

# PyTorch for deep learning (DQN implementation)
torch>=1.11.0,<3.0.0
torchvision>=0.12.0,<1.0.0

# =============================================================================
# OPTIONAL DEPENDENCIES
# =============================================================================

# Data analysis and visualization
matplotlib>=3.5.0,<4.0.0
pandas>=1.3.0,<3.0.0
seaborn>=0.11.0,<1.0.0

# Performance monitoring
psutil>=5.8.0,<6.0.0

# Scientific computing (additional utilities)
scipy>=1.7.0,<2.0.0

# Progress bars for long training runs
tqdm>=4.62.0,<5.0.0

# =============================================================================
# DEVELOPMENT AND TESTING
# =============================================================================

# Testing framework
pytest>=6.2.0,<8.0.0
pytest-cov>=3.0.0,<5.0.0
pytest-mock>=3.6.0,<4.0.0

# Code quality
flake8>=4.0.0,<7.0.0
black>=22.0.0,<24.0.0

# Jupyter notebook support
jupyter>=1.0.0,<2.0.0
ipykernel>=6.0.0,<7.0.0
notebook>=6.4.0,<8.0.0

# =============================================================================
# ALTERNATIVE ML FRAMEWORKS (Optional)
# =============================================================================

# TensorFlow (alternative to PyTorch)
# tensorflow>=2.8.0,<3.0.0
# tensorflow-probability>=0.15.0,<1.0.0

# Scikit-learn for classical ML
# scikit-learn>=1.0.0,<2.0.0

# =============================================================================
# INSTALLATION INSTRUCTIONS
# =============================================================================
#
# Install core dependencies (required for Phase 3):
#   pip install -r requirements.txt
#
# Install only Phase 1 & 2 dependencies (minimal):
#   # No external dependencies needed
#
# Install with optional dependencies:
#   pip install -r requirements.txt
#   # All optional dependencies are included above
#
# Install for development:
#   pip install -r requirements.txt
#   # Includes testing and code quality tools
#
# GPU Support (optional, for faster training):
#   # For CUDA 11.8:
#   pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
#
#   # For CUDA 12.1:
#   pip install torch torchvision --index-url https://download.pytorch.org/whl/cu121
#
# =============================================================================
