"""
Verification script for Phase 3 fixes.

This script verifies that all identified issues have been resolved:
1. Missing get_bet_amount method in BaseRLAgent
2. Missing detection_penalty in EvasionConfig
3. Missing get_penetration method in Deck
4. Missing methods in PersonaSwitcher
5. Import and integration issues
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, '.')

def verify_base_rl_agent_fixes():
    """Verify BaseRLAgent fixes."""
    print("1. Verifying BaseRLAgent fixes...")
    
    try:
        from rl.base_rl_agent import BaseRLAgent
        from rl.dqn_agent import DQNAgent, DQNConfig
        
        # Test that get_bet_amount method exists
        config = DQNConfig(hidden_layers=[8, 4])
        agent = DQNAgent("Test Agent", config)
        
        # Test get_bet_amount method
        bet_amount = agent.get_bet_amount(1.0, 100.0)
        assert isinstance(bet_amount, (int, float)), "get_bet_amount should return a number"
        assert bet_amount >= 1.0, "Bet amount should be at least minimum"
        
        print("   ✅ get_bet_amount method implemented and working")
        
        # Test state_dict and load_state_dict methods
        state_dict = agent.state_dict()
        assert isinstance(state_dict, dict), "state_dict should return a dictionary"
        assert 'q_network_state_dict' in state_dict, "state_dict should contain network state"
        
        print("   ✅ state_dict method implemented and working")
        
        # Test load_state_dict
        agent.load_state_dict(state_dict)
        print("   ✅ load_state_dict method implemented and working")
        
        return True
        
    except Exception as e:
        print(f"   ❌ BaseRLAgent fixes failed: {e}")
        return False

def verify_evasion_config_fixes():
    """Verify EvasionConfig fixes."""
    print("\\n2. Verifying EvasionConfig fixes...")
    
    try:
        from rl.evasion_strategies import EvasionConfig
        
        # Test that detection_penalty exists
        config = EvasionConfig()
        assert hasattr(config, 'detection_penalty'), "EvasionConfig should have detection_penalty"
        assert isinstance(config.detection_penalty, (int, float)), "detection_penalty should be a number"
        
        print(f"   ✅ detection_penalty implemented: {config.detection_penalty}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ EvasionConfig fixes failed: {e}")
        return False

def verify_deck_fixes():
    """Verify Deck fixes."""
    print("\\n3. Verifying Deck fixes...")
    
    try:
        from core.deck import Deck
        
        # Test that get_penetration method exists
        deck = Deck(num_decks=1)
        penetration = deck.get_penetration()
        assert isinstance(penetration, (int, float)), "get_penetration should return a number"
        assert 0.0 <= penetration <= 1.0, "Penetration should be between 0 and 1"
        
        print(f"   ✅ get_penetration method implemented: {penetration}")
        
        # Test that it changes when cards are dealt
        initial_penetration = deck.get_penetration()
        deck.deal_card()
        new_penetration = deck.get_penetration()
        assert new_penetration > initial_penetration, "Penetration should increase after dealing cards"
        
        print(f"   ✅ get_penetration updates correctly: {initial_penetration} → {new_penetration}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Deck fixes failed: {e}")
        return False

def verify_persona_switcher_fixes():
    """Verify PersonaSwitcher fixes."""
    print("\\n4. Verifying PersonaSwitcher fixes...")
    
    try:
        from personas.persona_switcher import PersonaSwitcher, SwitchConfig
        from personas.cautious_persona import CautiousPersona
        from core.game_logic import GameAction
        
        # Test PersonaSwitcher methods
        config = SwitchConfig()
        switcher = PersonaSwitcher(config)
        
        # Test add_persona method
        persona = CautiousPersona()
        switcher.add_persona("test", persona)
        assert "test" in switcher.personas, "add_persona should add persona to collection"
        
        print("   ✅ add_persona method implemented and working")
        
        # Test track_decision method
        switcher.track_decision(GameAction.HIT)
        print("   ✅ track_decision method implemented and working")
        
        # Test get_detection_risk_assessment method
        risk_assessment = switcher.get_detection_risk_assessment()
        assert isinstance(risk_assessment, dict), "get_detection_risk_assessment should return dict"
        assert 'risk_level' in risk_assessment, "Risk assessment should contain risk_level"
        
        print(f"   ✅ get_detection_risk_assessment implemented: {risk_assessment['risk_level']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ PersonaSwitcher fixes failed: {e}")
        return False

def verify_evasive_agent_creation():
    """Verify EvasiveDQNAgent can be created."""
    print("\\n5. Verifying EvasiveDQNAgent creation...")
    
    try:
        from rl.evasive_dqn_agent import EvasiveDQNAgent
        from rl.dqn_agent import DQNConfig
        from rl.evasion_strategies import EvasionConfig
        from rl.adaptive_learning import AdaptationConfig
        
        # Create configurations
        dqn_config = DQNConfig(hidden_layers=[8, 4])
        evasion_config = EvasionConfig()
        adaptation_config = AdaptationConfig()
        
        # Create agent
        agent = EvasiveDQNAgent("Test Agent", dqn_config, evasion_config, adaptation_config)
        
        print(f"   ✅ EvasiveDQNAgent created successfully: {agent.name}")
        print(f"   ✅ Device: {agent.device}")
        
        # Test key methods
        bet_amount = agent.get_bet_amount()
        print(f"   ✅ get_bet_amount works: ${bet_amount}")
        
        network_info = agent.get_network_info()
        print(f"   ✅ Network info: {network_info['total_parameters']} parameters")
        
        return True
        
    except Exception as e:
        print(f"   ❌ EvasiveDQNAgent creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_training_pipeline_initialization():
    """Verify TrainingPipeline can be initialized."""
    print("\\n6. Verifying TrainingPipeline initialization...")
    
    try:
        from rl.training_pipeline import TrainingPipeline, TrainingConfig
        from rl.dqn_agent import DQNConfig
        from rl.evasion_strategies import EvasionConfig
        from rl.adaptive_learning import AdaptationConfig
        
        # Create configurations
        dqn_config = DQNConfig(hidden_layers=[8, 4])
        evasion_config = EvasionConfig()
        adaptation_config = AdaptationConfig()
        training_config = TrainingConfig(total_episodes=5)
        
        # Create pipeline
        pipeline = TrainingPipeline(dqn_config, evasion_config, adaptation_config, training_config)
        
        print("   ✅ TrainingPipeline created successfully")
        
        # Test initialization
        pipeline.initialize_training()
        
        print(f"   ✅ TrainingPipeline initialized: {pipeline.agent.name}")
        print(f"   ✅ Environment created: {type(pipeline.environment).__name__}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ TrainingPipeline initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main verification function."""
    print("🔍 PHASE 3 FIXES VERIFICATION")
    print("=" * 40)
    
    verifications = [
        ("BaseRLAgent Fixes", verify_base_rl_agent_fixes),
        ("EvasionConfig Fixes", verify_evasion_config_fixes),
        ("Deck Fixes", verify_deck_fixes),
        ("PersonaSwitcher Fixes", verify_persona_switcher_fixes),
        ("EvasiveDQNAgent Creation", verify_evasive_agent_creation),
        ("TrainingPipeline Initialization", verify_training_pipeline_initialization)
    ]
    
    passed = 0
    total = len(verifications)
    
    for name, verify_func in verifications:
        if verify_func():
            passed += 1
        else:
            print(f"\\n❌ {name} verification failed!")
    
    print("\\n" + "=" * 40)
    print(f"📊 VERIFICATION RESULTS: {passed}/{total} verifications passed")
    
    if passed == total:
        print("\\n🎉 ALL FIXES VERIFIED SUCCESSFULLY!")
        print("Phase 3: Reinforcement Learning with Evasion is fully functional!")
        print("\\n✅ Key fixes implemented:")
        print("   • get_bet_amount method in BaseRLAgent")
        print("   • detection_penalty in EvasionConfig")
        print("   • get_penetration method in Deck")
        print("   • Missing methods in PersonaSwitcher")
        print("   • state_dict/load_state_dict in DQNAgent")
        print("   • All import and integration issues resolved")
        return True
    else:
        print(f"\\n❌ {total - passed} verification(s) failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
