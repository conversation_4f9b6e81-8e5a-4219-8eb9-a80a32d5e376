"""
Persona Switcher for BlackJack Bot ML.

This module implements a dynamic persona switcher that can change between
different personas to avoid detection patterns and simulate realistic
human behavior variation.
"""

import random
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .base_persona import <PERSON><PERSON><PERSON><PERSON>, PersonaConfig, DecisionContext
from .cautious_persona import Cautious<PERSON>erson<PERSON>
from .aggressive_persona import Aggressive<PERSON>ersona
from .intuitive_persona import Intuitive<PERSON>ersona
from core.game_logic import GameAction, GameState


class SwitchTrigger(Enum):
    """Triggers that can cause persona switching."""
    TIME_BASED = "time_based"
    HAND_COUNT = "hand_count"
    RESULT_STREAK = "result_streak"
    CONTEXT_CHANGE = "context_change"
    RANDOM = "random"
    DETECTION_AVOIDANCE = "detection_avoidance"


@dataclass
class SwitchConfig:
    """Configuration for persona switching behavior."""
    # Time-based switching
    min_switch_time: float = 300.0  # 5 minutes minimum
    max_switch_time: float = 1800.0  # 30 minutes maximum
    
    # Hand-based switching
    min_hands_per_persona: int = 50
    max_hands_per_persona: int = 200
    
    # Result-based switching
    switch_on_loss_streak: int = 5
    switch_on_win_streak: int = 8
    
    # Random switching probability
    random_switch_probability: float = 0.02  # 2% chance per hand
    
    # Detection avoidance
    pattern_detection_threshold: int = 100  # Hands before considering pattern detection
    consistency_threshold: float = 0.95  # If too consistent, switch
    
    # Persona preferences (weights for selection)
    persona_weights: Dict[str, float] = None
    
    def __post_init__(self):
        """Initialize default persona weights."""
        if self.persona_weights is None:
            self.persona_weights = {
                "cautious": 0.3,
                "aggressive": 0.4,
                "intuitive": 0.3
            }


class PersonaSwitcher:
    """
    Dynamic persona switcher for detection avoidance.
    
    This class manages multiple personas and switches between them
    based on various triggers to avoid creating detectable patterns.
    """
    
    def __init__(self, config: SwitchConfig = None):
        """
        Initialize the persona switcher.
        
        Args:
            config: Switching configuration
        """
        self.config = config or SwitchConfig()
        
        # Create available personas
        self.personas = {
            "cautious": CautiousPersona(),
            "aggressive": AggressivePersona(),
            "intuitive": IntuitivePersona()
        }
        
        # Current state
        self.current_persona_name = self._select_initial_persona()
        self.current_persona = self.personas[self.current_persona_name]
        
        # Switching tracking
        self.switch_history: List[Dict[str, Any]] = []
        self.last_switch_time = time.time()
        self.last_switch_hand = 0
        self.hands_with_current_persona = 0
        
        # Pattern detection tracking
        self.decision_patterns: List[Dict[str, Any]] = []
        self.consistency_scores: List[float] = []
    
    def get_action(self, game_state: GameState, hand_index: int = 0) -> GameAction:
        """
        Get action using current persona, with potential switching.
        
        Args:
            game_state: Current game state
            hand_index: Index of hand to decide for
            
        Returns:
            Action from current persona
        """
        # Check for switching triggers before making decision
        self._check_switching_triggers(game_state)
        
        # Get action from current persona
        action = self.current_persona.get_action(game_state, hand_index)
        
        # Track decision for pattern analysis
        self._track_decision_pattern(game_state, action, hand_index)
        
        # Update counters
        self.hands_with_current_persona += 1
        
        return action
    
    def _check_switching_triggers(self, game_state: GameState) -> None:
        """Check if any switching triggers are activated."""
        current_time = time.time()
        
        # Time-based switching
        if self._should_switch_time_based(current_time):
            self._switch_persona(SwitchTrigger.TIME_BASED)
            return
        
        # Hand count-based switching
        if self._should_switch_hand_based():
            self._switch_persona(SwitchTrigger.HAND_COUNT)
            return
        
        # Result streak-based switching
        if self._should_switch_result_based():
            self._switch_persona(SwitchTrigger.RESULT_STREAK)
            return
        
        # Context change-based switching
        if self._should_switch_context_based():
            self._switch_persona(SwitchTrigger.CONTEXT_CHANGE)
            return
        
        # Detection avoidance switching
        if self._should_switch_detection_avoidance():
            self._switch_persona(SwitchTrigger.DETECTION_AVOIDANCE)
            return
        
        # Random switching
        if self._should_switch_random():
            self._switch_persona(SwitchTrigger.RANDOM)
            return
    
    def _should_switch_time_based(self, current_time: float) -> bool:
        """Check if should switch based on time."""
        time_since_switch = current_time - self.last_switch_time
        
        # Must switch if max time exceeded
        if time_since_switch >= self.config.max_switch_time:
            return True
        
        # Can switch if min time exceeded and random chance
        if (time_since_switch >= self.config.min_switch_time and
            random.random() < 0.1):  # 10% chance per check after min time
            return True
        
        return False
    
    def _should_switch_hand_based(self) -> bool:
        """Check if should switch based on hand count."""
        # Must switch if max hands exceeded
        if self.hands_with_current_persona >= self.config.max_hands_per_persona:
            return True
        
        # Can switch if min hands exceeded and random chance
        if (self.hands_with_current_persona >= self.config.min_hands_per_persona and
            random.random() < 0.05):  # 5% chance per hand after min hands
            return True
        
        return False
    
    def _should_switch_result_based(self) -> bool:
        """Check if should switch based on result streaks."""
        # Check loss streak
        if (self.current_persona.consecutive_losses >= self.config.switch_on_loss_streak):
            return True
        
        # Check win streak
        if (self.current_persona.consecutive_wins >= self.config.switch_on_win_streak):
            return True
        
        return False
    
    def _should_switch_context_based(self) -> bool:
        """Check if should switch based on context changes."""
        # Switch if context has been non-normal for a while
        if (self.current_persona.current_context != DecisionContext.NORMAL and
            self.hands_with_current_persona >= 20):  # 20 hands in non-normal context
            return True
        
        return False
    
    def _should_switch_detection_avoidance(self) -> bool:
        """Check if should switch to avoid pattern detection."""
        if len(self.decision_patterns) < self.config.pattern_detection_threshold:
            return False
        
        # Calculate recent consistency
        recent_consistency = self._calculate_recent_consistency()
        
        # Switch if too consistent (might be detected as bot)
        if recent_consistency >= self.config.consistency_threshold:
            return True
        
        return False
    
    def _should_switch_random(self) -> bool:
        """Check for random switching."""
        return random.random() < self.config.random_switch_probability
    
    def _switch_persona(self, trigger: SwitchTrigger) -> None:
        """
        Switch to a different persona.
        
        Args:
            trigger: What triggered the switch
        """
        old_persona_name = self.current_persona_name
        
        # Select new persona (different from current)
        available_personas = [name for name in self.personas.keys() 
                            if name != self.current_persona_name]
        
        # Weight selection based on configuration and context
        weights = self._calculate_persona_weights(available_personas, trigger)
        new_persona_name = random.choices(available_personas, weights=weights)[0]
        
        # Perform the switch
        self.current_persona_name = new_persona_name
        self.current_persona = self.personas[new_persona_name]
        
        # Record the switch
        switch_record = {
            "timestamp": time.time(),
            "hand_number": self.current_persona.hands_played,
            "trigger": trigger.value,
            "old_persona": old_persona_name,
            "new_persona": new_persona_name,
            "hands_with_old_persona": self.hands_with_current_persona
        }
        self.switch_history.append(switch_record)
        
        # Reset counters
        self.last_switch_time = time.time()
        self.last_switch_hand = self.current_persona.hands_played
        self.hands_with_current_persona = 0
        
        # Optionally transfer some state between personas for continuity
        self._transfer_persona_state(old_persona_name, new_persona_name)
    
    def _calculate_persona_weights(self, available_personas: List[str], 
                                 trigger: SwitchTrigger) -> List[float]:
        """Calculate weights for persona selection based on context."""
        base_weights = [self.config.persona_weights.get(name, 1.0) 
                       for name in available_personas]
        
        # Adjust weights based on trigger and context
        if trigger == SwitchTrigger.RESULT_STREAK:
            # If losing, prefer cautious; if winning, prefer aggressive
            if self.current_persona.consecutive_losses > 0:
                # Boost cautious weight
                for i, name in enumerate(available_personas):
                    if name == "cautious":
                        base_weights[i] *= 2.0
            elif self.current_persona.consecutive_wins > 0:
                # Boost aggressive weight
                for i, name in enumerate(available_personas):
                    if name == "aggressive":
                        base_weights[i] *= 1.5
        
        elif trigger == SwitchTrigger.DETECTION_AVOIDANCE:
            # Prefer more variable persona (intuitive) for detection avoidance
            for i, name in enumerate(available_personas):
                if name == "intuitive":
                    base_weights[i] *= 2.0
        
        elif trigger == SwitchTrigger.CONTEXT_CHANGE:
            # Adjust based on current context
            context = self.current_persona.current_context
            if context == DecisionContext.PRESSURE:
                # Prefer cautious under pressure
                for i, name in enumerate(available_personas):
                    if name == "cautious":
                        base_weights[i] *= 1.5
            elif context == DecisionContext.CONFIDENT:
                # Prefer aggressive when confident
                for i, name in enumerate(available_personas):
                    if name == "aggressive":
                        base_weights[i] *= 1.5
        
        return base_weights
    
    def _transfer_persona_state(self, old_persona_name: str, new_persona_name: str) -> None:
        """Transfer relevant state between personas for continuity."""
        old_persona = self.personas[old_persona_name]
        new_persona = self.personas[new_persona_name]
        
        # Transfer win/loss streaks for emotional continuity
        new_persona.consecutive_wins = old_persona.consecutive_wins
        new_persona.consecutive_losses = old_persona.consecutive_losses
        
        # Transfer some recent emotional state
        if hasattr(new_persona, 'recent_emotions') and hasattr(old_persona, 'recent_emotions'):
            new_persona.recent_emotions = old_persona.recent_emotions.copy()
    
    def _track_decision_pattern(self, game_state: GameState, action: GameAction, 
                              hand_index: int) -> None:
        """Track decision patterns for detection avoidance analysis."""
        if not game_state.player_hands or hand_index >= len(game_state.player_hands):
            return
        
        player_hand = game_state.player_hands[hand_index]
        dealer_upcard = game_state.dealer_hand.cards[0].get_value() if game_state.dealer_hand.cards else 0
        
        pattern = {
            "hand_value": player_hand.get_value(),
            "hand_soft": player_hand.is_soft(),
            "dealer_upcard": dealer_upcard,
            "action": action.value,
            "persona": self.current_persona_name,
            "timestamp": time.time()
        }
        
        self.decision_patterns.append(pattern)
        
        # Keep only recent patterns
        if len(self.decision_patterns) > 500:
            self.decision_patterns.pop(0)
    
    def _calculate_recent_consistency(self, lookback: int = 50) -> float:
        """Calculate consistency of recent decisions."""
        if len(self.decision_patterns) < lookback:
            return 0.0
        
        recent_patterns = self.decision_patterns[-lookback:]
        
        # Group by situation and check consistency
        situation_actions = {}
        for pattern in recent_patterns:
            situation = (pattern["hand_value"], pattern["hand_soft"], pattern["dealer_upcard"])
            if situation not in situation_actions:
                situation_actions[situation] = []
            situation_actions[situation].append(pattern["action"])
        
        # Calculate consistency score
        total_situations = 0
        consistent_situations = 0
        
        for situation, actions in situation_actions.items():
            if len(actions) >= 2:  # Need at least 2 decisions for consistency check
                total_situations += 1
                # Check if all actions are the same
                if len(set(actions)) == 1:
                    consistent_situations += 1
        
        if total_situations == 0:
            return 0.0
        
        return consistent_situations / total_situations
    
    def _select_initial_persona(self) -> str:
        """Select initial persona based on weights."""
        personas = list(self.personas.keys())
        weights = [self.config.persona_weights.get(name, 1.0) for name in personas]
        return random.choices(personas, weights=weights)[0]
    
    def update_result(self, result: str) -> None:
        """Update current persona with game result."""
        self.current_persona.update_result(result)
    
    def get_current_persona_name(self) -> str:
        """Get the name of the current persona."""
        return self.current_persona_name
    
    def get_current_persona(self) -> BasePersona:
        """Get the current persona object."""
        return self.current_persona
    
    def get_switch_history(self) -> List[Dict[str, Any]]:
        """Get the history of persona switches."""
        return self.switch_history.copy()
    
    def get_switching_stats(self) -> Dict[str, Any]:
        """Get statistics about persona switching behavior."""
        if not self.switch_history:
            return {
                "total_switches": 0,
                "current_persona": self.current_persona_name,
                "hands_with_current": self.hands_with_current_persona
            }
        
        # Calculate switch statistics
        trigger_counts = {}
        persona_usage = {}
        
        for switch in self.switch_history:
            trigger = switch["trigger"]
            trigger_counts[trigger] = trigger_counts.get(trigger, 0) + 1
            
            old_persona = switch["old_persona"]
            persona_usage[old_persona] = persona_usage.get(old_persona, 0) + switch["hands_with_old_persona"]
        
        # Add current persona usage
        persona_usage[self.current_persona_name] = persona_usage.get(self.current_persona_name, 0) + self.hands_with_current_persona
        
        return {
            "total_switches": len(self.switch_history),
            "trigger_distribution": trigger_counts,
            "persona_usage": persona_usage,
            "current_persona": self.current_persona_name,
            "hands_with_current": self.hands_with_current_persona,
            "avg_hands_per_persona": sum(persona_usage.values()) / len(persona_usage) if persona_usage else 0,
            "recent_consistency": self._calculate_recent_consistency()
        }
    
    def reset_session(self) -> None:
        """Reset switcher state for new session."""
        # Reset all personas
        for persona in self.personas.values():
            persona.reset_session()
        
        # Reset switcher state
        self.current_persona_name = self._select_initial_persona()
        self.current_persona = self.personas[self.current_persona_name]
        self.switch_history = []
        self.last_switch_time = time.time()
        self.last_switch_hand = 0
        self.hands_with_current_persona = 0
        self.decision_patterns = []
        self.consistency_scores = []
