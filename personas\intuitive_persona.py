"""
Intuitive/Emotional Persona for BlackJack Bot ML.

This persona represents an emotional, inconsistent player who:
- Follows Basic Strategy with only 70% accuracy
- Has highly variable decision timing
- Makes decisions based on "feel" and emotions
- Is heavily influenced by recent results and context
"""

import random
from .base_persona import <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, DecisionContext, ErrorType
from core.hand import Hand
from core.game_logic import GameState
from utils.basic_strategy_charts import BasicStrategyAction


def create_intuitive_persona_config() -> PersonaConfig:
    """Create configuration for the Intuitive/Emotional persona."""
    
    decision_pattern = DecisionPattern(
        base_accuracy=0.70,  # Lower accuracy - relies on intuition over strategy
        decision_speed=1.0,  # Variable speed
        consistency=0.60,    # Very inconsistent
        aggression_bias=0.0,  # Neutral bias but highly variable
        risk_tolerance=0.5,  # Moderate but variable risk tolerance
        emotional_volatility=0.4,  # High emotional volatility
        fatigue_rate=0.0012,  # Faster fatigue accumulation
        
        # Timing parameters - highly variable
        min_decision_time=0.2,
        max_decision_time=15.0,
        avg_decision_time=3.0,
        timing_variance=0.8  # Very high variance - unpredictable timing
    )
    
    # Error patterns - high across all categories
    error_patterns = {
        ErrorType.STRATEGY_DEVIATION: 0.20,  # 20% strategy errors
        ErrorType.TIMING_INCONSISTENCY: 0.25,  # Very inconsistent timing
        ErrorType.EMOTIONAL_DECISION: 0.08,  # Frequent emotional decisions
        ErrorType.FATIGUE_ERROR: 0.01,  # Some fatigue errors
        ErrorType.DISTRACTION_ERROR: 0.01  # Some distraction errors
    }
    
    # Context modifiers - heavily affected by all contexts
    context_modifiers = {
        DecisionContext.PRESSURE: {
            "accuracy_modifier": -0.2,  # Significant accuracy drop under pressure
            "speed_modifier": 2.0,  # Much faster when pressured (panic)
            "aggression_modifier": 0.3  # More aggressive under pressure
        },
        DecisionContext.CONFIDENT: {
            "accuracy_modifier": 0.1,  # Better when confident
            "speed_modifier": 0.6,  # Faster when confident
            "aggression_modifier": 0.4  # Much more aggressive when winning
        },
        DecisionContext.TIRED: {
            "accuracy_modifier": -0.25,  # Very affected by fatigue
            "speed_modifier": 1.5,  # Inconsistent speed when tired
            "aggression_modifier": -0.2  # More conservative when tired
        },
        DecisionContext.DISTRACTED: {
            "accuracy_modifier": -0.3,  # Extremely affected by distractions
            "speed_modifier": 0.5,  # Very fast when distracted (impulsive)
            "aggression_modifier": 0.2  # More aggressive when distracted
        }
    }
    
    return PersonaConfig(
        name="Intuitive/Emotional",
        description="An emotional, inconsistent player who relies on intuition and is heavily influenced by recent results",
        decision_pattern=decision_pattern,
        error_patterns=error_patterns,
        context_modifiers=context_modifiers
    )


class IntuitivePersona(BasePersona):
    """
    Intuitive/Emotional persona implementation.
    
    This persona represents a player who:
    - Has limited Basic Strategy knowledge
    - Makes decisions based on "feel" and recent results
    - Is highly emotional and inconsistent
    - Changes behavior dramatically based on context
    """
    
    def __init__(self):
        """Initialize the Intuitive persona."""
        config = create_intuitive_persona_config()
        super().__init__(config)
        
        # Additional state for emotional decisions
        self.recent_emotions = []  # Track recent emotional states
        self.superstitions = self._generate_superstitions()
    
    def _generate_superstitions(self) -> dict:
        """Generate random superstitions that affect play."""
        superstitions = {}
        
        # Random superstitions about dealer upcards
        if random.random() < 0.3:
            unlucky_card = random.choice([4, 5, 6, 7])
            superstitions['unlucky_dealer_card'] = unlucky_card
        
        if random.random() < 0.3:
            lucky_card = random.choice([2, 3, 8, 9])
            superstitions['lucky_dealer_card'] = lucky_card
        
        # Superstitions about hand totals
        if random.random() < 0.2:
            unlucky_total = random.choice([13, 14, 15, 16])
            superstitions['unlucky_total'] = unlucky_total
        
        return superstitions
    
    def _apply_persona_bias(self, optimal_action: BasicStrategyAction,
                          player_hand: Hand, dealer_upcard: int,
                          game_state: GameState) -> BasicStrategyAction:
        """
        Apply intuitive/emotional bias to decision making.
        
        The intuitive persona:
        - Makes decisions based on "feel"
        - Is heavily influenced by recent results
        - Has superstitions and hunches
        - Changes strategy based on emotions
        """
        hand_value = player_hand.get_value()
        
        # Apply superstitions
        action = self._apply_superstitions(optimal_action, player_hand, dealer_upcard)
        if action != optimal_action:
            return action
        
        # Apply emotional state
        action = self._apply_emotional_state(optimal_action, player_hand, dealer_upcard)
        if action != optimal_action:
            return action
        
        # Apply "intuitive" modifications
        return self._apply_intuitive_modifications(optimal_action, player_hand, dealer_upcard)
    
    def _apply_superstitions(self, optimal_action: BasicStrategyAction,
                           player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """Apply superstitious modifications to decisions."""
        hand_value = player_hand.get_value()
        
        # Unlucky dealer card superstition
        if 'unlucky_dealer_card' in self.superstitions:
            if dealer_upcard == self.superstitions['unlucky_dealer_card']:
                # Be more conservative vs "unlucky" dealer card
                if optimal_action == BasicStrategyAction.HIT and hand_value >= 12:
                    return BasicStrategyAction.STAND
                elif optimal_action == BasicStrategyAction.DOUBLE:
                    return BasicStrategyAction.HIT
        
        # Lucky dealer card superstition
        if 'lucky_dealer_card' in self.superstitions:
            if dealer_upcard == self.superstitions['lucky_dealer_card']:
                # Be more aggressive vs "lucky" dealer card
                if optimal_action == BasicStrategyAction.STAND and hand_value <= 16:
                    return BasicStrategyAction.HIT
                elif optimal_action == BasicStrategyAction.HIT and hand_value >= 9:
                    if player_hand.can_double():
                        return BasicStrategyAction.DOUBLE
        
        # Unlucky total superstition
        if 'unlucky_total' in self.superstitions:
            if hand_value == self.superstitions['unlucky_total']:
                # Always hit "unlucky" totals
                if optimal_action == BasicStrategyAction.STAND:
                    return BasicStrategyAction.HIT
        
        return optimal_action
    
    def _apply_emotional_state(self, optimal_action: BasicStrategyAction,
                             player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """Apply current emotional state to decisions."""
        hand_value = player_hand.get_value()
        
        # Emotional decisions based on streaks
        if self.consecutive_losses >= 2:
            # Frustrated/desperate - make irrational decisions
            if random.random() < 0.3:
                if optimal_action == BasicStrategyAction.STAND:
                    return BasicStrategyAction.HIT  # "I need to do something!"
                elif optimal_action == BasicStrategyAction.HIT and player_hand.can_double():
                    return BasicStrategyAction.DOUBLE  # "Go big or go home!"
        
        elif self.consecutive_wins >= 3:
            # Overconfident - take unnecessary risks
            if random.random() < 0.25:
                if optimal_action == BasicStrategyAction.STAND and hand_value <= 15:
                    return BasicStrategyAction.HIT  # "I'm feeling lucky!"
                elif optimal_action == BasicStrategyAction.HIT and hand_value >= 9:
                    if player_hand.can_double():
                        return BasicStrategyAction.DOUBLE  # "Let's double down!"
        
        # Recent hand influence
        if len(self.decision_history) > 0:
            last_decision = self.decision_history[-1]
            if last_decision.get('is_error', False):
                # Last decision was wrong - second guess everything
                if random.random() < 0.2:
                    return self._opposite_action(optimal_action, player_hand)
        
        return optimal_action
    
    def _apply_intuitive_modifications(self, optimal_action: BasicStrategyAction,
                                     player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """Apply "intuitive" (often incorrect) modifications."""
        hand_value = player_hand.get_value()
        
        # "Intuitive" rules that are often wrong
        
        # "Never bust" mentality
        if optimal_action == BasicStrategyAction.HIT and hand_value >= 12:
            if random.random() < 0.15:  # 15% chance to avoid hitting stiff hands
                return BasicStrategyAction.STAND
        
        # "Always double on 11" (even when wrong)
        if hand_value == 11 and player_hand.can_double():
            if random.random() < 0.2:  # 20% chance to always double 11
                return BasicStrategyAction.DOUBLE
        
        # "Mimic the dealer" strategy
        if random.random() < 0.1:  # 10% chance to mimic dealer
            if hand_value <= 16:
                return BasicStrategyAction.HIT
            else:
                return BasicStrategyAction.STAND
        
        # "Follow the trend" - if dealer has been winning, play differently
        if self.consecutive_losses >= 2:
            if random.random() < 0.15:
                # Change strategy when losing
                if optimal_action == BasicStrategyAction.HIT:
                    return BasicStrategyAction.STAND
                elif optimal_action == BasicStrategyAction.STAND:
                    return BasicStrategyAction.HIT
        
        return optimal_action
    
    def _opposite_action(self, action: BasicStrategyAction, player_hand: Hand) -> BasicStrategyAction:
        """Get the opposite of an action (for second-guessing)."""
        if action == BasicStrategyAction.HIT:
            return BasicStrategyAction.STAND
        elif action == BasicStrategyAction.STAND:
            return BasicStrategyAction.HIT
        elif action == BasicStrategyAction.DOUBLE:
            return BasicStrategyAction.HIT
        elif action == BasicStrategyAction.SPLIT:
            return BasicStrategyAction.HIT
        else:
            return action
    
    def _strategy_deviation_error(self, optimal_action: BasicStrategyAction,
                                player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """
        Generate strategy deviation errors for intuitive persona.
        
        Intuitive players make many different types of errors.
        """
        hand_value = player_hand.get_value()
        
        # Random strategy errors
        error_type = random.choice(['conservative', 'aggressive', 'random'])
        
        if error_type == 'conservative':
            if optimal_action == BasicStrategyAction.HIT and hand_value >= 12:
                return BasicStrategyAction.STAND
            elif optimal_action == BasicStrategyAction.DOUBLE:
                return BasicStrategyAction.HIT
            elif optimal_action == BasicStrategyAction.SPLIT:
                return BasicStrategyAction.HIT
        
        elif error_type == 'aggressive':
            if optimal_action == BasicStrategyAction.STAND and hand_value <= 16:
                return BasicStrategyAction.HIT
            elif optimal_action == BasicStrategyAction.HIT and player_hand.can_double():
                return BasicStrategyAction.DOUBLE
        
        else:  # random
            # Completely random decision
            possible_actions = [BasicStrategyAction.HIT, BasicStrategyAction.STAND]
            if player_hand.can_double():
                possible_actions.append(BasicStrategyAction.DOUBLE)
            if player_hand.can_split():
                possible_actions.append(BasicStrategyAction.SPLIT)
            
            return random.choice(possible_actions)
        
        return optimal_action
    
    def _emotional_decision_error(self, optimal_action: BasicStrategyAction,
                                player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """
        Generate emotional decision errors for intuitive persona.
        
        These are frequent and highly variable.
        """
        # Emotional decisions are very random for intuitive players
        emotions = ['angry', 'excited', 'nervous', 'confident', 'desperate']
        current_emotion = random.choice(emotions)
        
        if current_emotion == 'angry':
            # Angry - hit everything
            return BasicStrategyAction.HIT
        elif current_emotion == 'excited':
            # Excited - double when possible
            if player_hand.can_double():
                return BasicStrategyAction.DOUBLE
            else:
                return BasicStrategyAction.HIT
        elif current_emotion == 'nervous':
            # Nervous - stand on everything
            return BasicStrategyAction.STAND
        elif current_emotion == 'confident':
            # Confident - take risks
            if player_hand.get_value() <= 16:
                return BasicStrategyAction.HIT
            else:
                return BasicStrategyAction.STAND
        elif current_emotion == 'desperate':
            # Desperate - random action
            actions = [BasicStrategyAction.HIT, BasicStrategyAction.STAND]
            if player_hand.can_double():
                actions.append(BasicStrategyAction.DOUBLE)
            return random.choice(actions)
        
        return optimal_action
    
    def update_result(self, result: str) -> None:
        """Update persona state and emotional history based on result."""
        super().update_result(result)
        
        # Track emotional responses to results
        if result in ["win", "blackjack"]:
            self.recent_emotions.append("happy")
        elif result in ["loss", "bust"]:
            self.recent_emotions.append("frustrated")
        else:
            self.recent_emotions.append("neutral")
        
        # Keep only recent emotions
        if len(self.recent_emotions) > 5:
            self.recent_emotions.pop(0)
        
        # Occasionally change superstitions based on results
        if len(self.recent_emotions) >= 3:
            if all(emotion == "frustrated" for emotion in self.recent_emotions[-3:]):
                # Three bad results in a row - change superstitions
                if random.random() < 0.3:
                    self.superstitions = self._generate_superstitions()
