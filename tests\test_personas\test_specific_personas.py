"""
Unit tests for specific persona implementations.

Tests P2_T2 implementation: Three Specific Human Personas.
"""

import unittest
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from personas.cautious_persona import Cautious<PERSON>ersona, create_cautious_persona_config
from personas.aggressive_persona import AggressivePersona, create_aggressive_persona_config
from personas.intuitive_persona import Intuitive<PERSON>erson<PERSON>, create_intuitive_persona_config
from personas.human_persona_agent import HumanPersonaAgent
from core.card import Card, Suit, Rank
from core.hand import Hand
from core.game_logic import GameState, GameAction
from utils.simulation import BlackjackSimulator, SimulationConfig


class TestSpecificPersonas(unittest.TestCase):
    """Test cases for the three specific persona implementations."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.cautious_persona = CautiousPersona()
        self.aggressive_persona = AggressivePersona()
        self.intuitive_persona = IntuitivePersona()
        
        # Create agents for testing
        self.cautious_agent = HumanPersonaAgent("Cautious Agent", self.cautious_persona)
        self.aggressive_agent = HumanPersonaAgent("Aggressive Agent", self.aggressive_persona)
        self.intuitive_agent = HumanPersonaAgent("Intuitive Agent", self.intuitive_persona)
    
    def test_persona_configurations(self):
        """Test that persona configurations match specifications."""
        # Test Cautious persona
        cautious_config = self.cautious_persona.config
        self.assertEqual(cautious_config.name, "Cautious Basic Strategy")
        self.assertEqual(cautious_config.decision_pattern.base_accuracy, 0.95)
        self.assertLess(cautious_config.decision_pattern.decision_speed, 1.0)  # Slower
        self.assertLess(cautious_config.decision_pattern.aggression_bias, 0.0)  # Conservative
        
        # Test Aggressive persona
        aggressive_config = self.aggressive_persona.config
        self.assertEqual(aggressive_config.name, "Slightly Aggressive")
        self.assertEqual(aggressive_config.decision_pattern.base_accuracy, 0.90)
        self.assertGreater(aggressive_config.decision_pattern.decision_speed, 1.0)  # Faster
        self.assertGreater(aggressive_config.decision_pattern.aggression_bias, 0.0)  # Aggressive
        
        # Test Intuitive persona
        intuitive_config = self.intuitive_persona.config
        self.assertEqual(intuitive_config.name, "Intuitive/Emotional")
        self.assertEqual(intuitive_config.decision_pattern.base_accuracy, 0.70)
        self.assertGreater(intuitive_config.decision_pattern.emotional_volatility, 0.3)  # High volatility
    
    def test_decision_timing_differences(self):
        """Test that personas have different decision timing patterns."""
        # Create test hand
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        # Test timing simulation multiple times for statistical significance
        cautious_times = []
        aggressive_times = []
        intuitive_times = []
        
        for _ in range(20):
            cautious_times.append(self.cautious_persona._simulate_decision_timing(hand, 10))
            aggressive_times.append(self.aggressive_persona._simulate_decision_timing(hand, 10))
            intuitive_times.append(self.intuitive_persona._simulate_decision_timing(hand, 10))
        
        # Calculate averages
        avg_cautious = sum(cautious_times) / len(cautious_times)
        avg_aggressive = sum(aggressive_times) / len(aggressive_times)
        avg_intuitive = sum(intuitive_times) / len(intuitive_times)
        
        # Cautious should be slower than aggressive
        self.assertGreater(avg_cautious, avg_aggressive)
        
        # Check that timing is within expected ranges
        self.assertGreater(avg_cautious, 2.0)  # Should be relatively slow
        self.assertLess(avg_aggressive, 3.0)   # Should be relatively fast
    
    def test_accuracy_differences(self):
        """Test that personas have different accuracy levels."""
        # Test effective accuracy
        cautious_accuracy = self.cautious_persona._get_effective_accuracy()
        aggressive_accuracy = self.aggressive_persona._get_effective_accuracy()
        intuitive_accuracy = self.intuitive_persona._get_effective_accuracy()
        
        # Should match configured base accuracies
        self.assertAlmostEqual(cautious_accuracy, 0.95, places=2)
        self.assertAlmostEqual(aggressive_accuracy, 0.90, places=2)
        self.assertAlmostEqual(intuitive_accuracy, 0.70, places=2)
        
        # Order should be: cautious > aggressive > intuitive
        self.assertGreater(cautious_accuracy, aggressive_accuracy)
        self.assertGreater(aggressive_accuracy, intuitive_accuracy)
    
    def test_behavioral_differences_simulation(self):
        """Test behavioral differences through simulation."""
        # Run short simulations for each persona
        config = SimulationConfig(
            num_hands=100,
            verbose=False,
            random_seed=42
        )
        
        simulator = BlackjackSimulator(config)
        
        # Run simulations
        cautious_results = simulator.run_simulation(self.cautious_agent)
        aggressive_results = simulator.run_simulation(self.aggressive_agent)
        intuitive_results = simulator.run_simulation(self.intuitive_agent)
        
        # Extract behavioral stats
        cautious_stats = cautious_results['agent_stats']['behavioral_stats']
        aggressive_stats = aggressive_results['agent_stats']['behavioral_stats']
        intuitive_stats = intuitive_results['agent_stats']['behavioral_stats']
        
        # Check accuracy differences
        if cautious_stats and aggressive_stats and intuitive_stats:
            self.assertGreater(cautious_stats['accuracy'], aggressive_stats['accuracy'])
            self.assertGreater(aggressive_stats['accuracy'], intuitive_stats['accuracy'])
        
        # Check timing differences
        cautious_timing = cautious_results['agent_stats']['persona_metrics']['avg_decision_time']
        aggressive_timing = aggressive_results['agent_stats']['persona_metrics']['avg_decision_time']
        
        # Cautious should generally be slower
        self.assertGreater(cautious_timing, aggressive_timing * 0.8)  # Allow some variance
    
    def test_cautious_conservative_bias(self):
        """Test that cautious persona shows conservative bias."""
        # Create borderline hitting situation (hard 12 vs dealer 3)
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.TWO))
        
        game_state = GameState()
        game_state.player_hands = [hand]
        game_state.dealer_hand = Hand()
        game_state.dealer_hand.add_card(Card(Suit.CLUBS, Rank.THREE))
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Test multiple decisions to see bias
        actions = []
        for _ in range(20):
            action = self.cautious_persona.get_action(game_state, 0)
            actions.append(action)
        
        # Should show some conservative bias (more stands than optimal)
        stand_count = sum(1 for a in actions if a == GameAction.STAND)
        hit_count = sum(1 for a in actions if a == GameAction.HIT)
        
        # Cautious persona should stand more often than hit in borderline situations
        # (This is actually incorrect strategy, but shows conservative bias)
        self.assertGreaterEqual(stand_count, hit_count * 0.5)  # Allow for some variation
    
    def test_aggressive_action_bias(self):
        """Test that aggressive persona shows action-oriented bias."""
        # Create borderline standing situation (hard 16 vs dealer 10)
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        game_state = GameState()
        game_state.player_hands = [hand]
        game_state.dealer_hand = Hand()
        game_state.dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Test multiple decisions
        actions = []
        for _ in range(20):
            action = self.aggressive_persona.get_action(game_state, 0)
            actions.append(action)
        
        # Should show action bias (more hits than stands in this situation)
        hit_count = sum(1 for a in actions if a == GameAction.HIT)
        stand_count = sum(1 for a in actions if a == GameAction.STAND)
        
        # Aggressive persona should hit more often in borderline situations
        self.assertGreaterEqual(hit_count, stand_count * 0.8)  # Allow for some variation
    
    def test_intuitive_inconsistency(self):
        """Test that intuitive persona shows high inconsistency."""
        # Create same situation multiple times
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.FIVE))
        
        game_state = GameState()
        game_state.player_hands = [hand]
        game_state.dealer_hand = Hand()
        game_state.dealer_hand.add_card(Card(Suit.CLUBS, Rank.SIX))
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Test many decisions
        actions = []
        for _ in range(30):
            # Reset persona state to avoid learning effects
            self.intuitive_persona.hands_played = 0
            action = self.intuitive_persona.get_action(game_state, 0)
            actions.append(action)
        
        # Should show high variability
        unique_actions = set(actions)
        self.assertGreaterEqual(len(unique_actions), 2)  # Should have at least 2 different actions
        
        # Should have more variation than other personas
        action_counts = {}
        for action in actions:
            action_counts[action] = action_counts.get(action, 0) + 1
        
        # No single action should dominate completely (showing inconsistency)
        max_count = max(action_counts.values())
        self.assertLess(max_count / len(actions), 0.9)  # No action should be >90% of decisions
    
    def test_context_sensitivity(self):
        """Test that personas respond differently to context changes."""
        # Test pressure context (losing streak)
        for persona in [self.cautious_persona, self.aggressive_persona, self.intuitive_persona]:
            # Simulate losing streak
            for _ in range(4):
                persona.update_result("loss")
            
            # Create test situation
            hand = Hand()
            hand.add_card(Card(Suit.HEARTS, Rank.TEN))
            hand.add_card(Card(Suit.SPADES, Rank.SIX))
            
            game_state = GameState()
            game_state.player_hands = [hand]
            game_state.dealer_hand = Hand()
            game_state.dealer_hand.add_card(Card(Suit.CLUBS, Rank.TEN))
            game_state.can_double = [False]
            game_state.can_split = [False]
            
            # Get action under pressure
            action_under_pressure = persona.get_action(game_state, 0)
            
            # Reset and test normal context
            persona.reset_session()
            action_normal = persona.get_action(game_state, 0)
            
            # Actions might be different (though not guaranteed due to randomness)
            # At minimum, the context should be tracked
            self.assertIsNotNone(persona.current_context)
    
    def test_error_pattern_differences(self):
        """Test that personas have different error patterns."""
        # Check error pattern configurations
        cautious_errors = self.cautious_persona.config.error_patterns
        aggressive_errors = self.aggressive_persona.config.error_patterns
        intuitive_errors = self.intuitive_persona.config.error_patterns
        
        # Intuitive should have highest error rates
        from personas.base_persona import ErrorType
        
        intuitive_strategy_errors = intuitive_errors[ErrorType.STRATEGY_DEVIATION]
        aggressive_strategy_errors = aggressive_errors[ErrorType.STRATEGY_DEVIATION]
        cautious_strategy_errors = cautious_errors[ErrorType.STRATEGY_DEVIATION]
        
        # Order should be: intuitive > aggressive > cautious
        self.assertGreater(intuitive_strategy_errors, aggressive_strategy_errors)
        self.assertGreater(aggressive_strategy_errors, cautious_strategy_errors)
        
        # Intuitive should have highest emotional decision rate
        intuitive_emotional = intuitive_errors[ErrorType.EMOTIONAL_DECISION]
        aggressive_emotional = aggressive_errors[ErrorType.EMOTIONAL_DECISION]
        cautious_emotional = cautious_errors[ErrorType.EMOTIONAL_DECISION]
        
        self.assertGreater(intuitive_emotional, aggressive_emotional)
        self.assertGreater(aggressive_emotional, cautious_emotional)
    
    def test_agent_integration(self):
        """Test that personas integrate properly with agent framework."""
        # Test that all agents can be created and used
        agents = [self.cautious_agent, self.aggressive_agent, self.intuitive_agent]
        
        for agent in agents:
            # Test basic agent functionality
            self.assertIsNotNone(agent.name)
            self.assertIsNotNone(agent.persona)
            
            # Test betting
            bet = agent.get_bet_amount()
            self.assertGreater(bet, 0)
            
            # Test stats
            stats = agent.get_stats()
            self.assertIn('persona_name', stats)
            self.assertIn('behavioral_stats', stats)
            
            # Test string representations
            self.assertIn('HumanPersonaAgent', str(agent))
            self.assertIn('HumanPersonaAgent', repr(agent))
    
    def test_persona_names_and_descriptions(self):
        """Test that personas have correct names and descriptions."""
        # Test names
        self.assertEqual(self.cautious_persona.config.name, "Cautious Basic Strategy")
        self.assertEqual(self.aggressive_persona.config.name, "Slightly Aggressive")
        self.assertEqual(self.intuitive_persona.config.name, "Intuitive/Emotional")
        
        # Test descriptions exist and are meaningful
        self.assertGreater(len(self.cautious_persona.config.description), 20)
        self.assertGreater(len(self.aggressive_persona.config.description), 20)
        self.assertGreater(len(self.intuitive_persona.config.description), 20)
        
        # Test descriptions contain relevant keywords
        self.assertIn("careful", self.cautious_persona.config.description.lower())
        self.assertIn("aggressive", self.aggressive_persona.config.description.lower())
        self.assertIn("emotional", self.intuitive_persona.config.description.lower())


if __name__ == '__main__':
    unittest.main()
