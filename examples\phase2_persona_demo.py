"""
Phase 2 Human Persona Simulation Demo

This script demonstrates the Phase 2 implementation:
- P2_T1: Base persona framework with configurable error rates and timing
- P2_T2: Three specific human personas (Cautious, Aggressive, Intuitive)
- P2_T3: Persona switcher for detection avoidance

Usage:
    python examples/phase2_persona_demo.py
"""

import sys
import os
import time

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from personas import (
    CautiousPersona, AggressivePersona, IntuitivePersona,
    HumanPersonaAgent, PersonaSwitcherAgent, SwitchConfig
)
from utils.simulation import BlackjackSimulator, SimulationConfig
from utils.progress_manager import ProgressManager


def demonstrate_individual_personas():
    """Demonstrate the three individual personas."""
    print("=" * 60)
    print("PHASE 2 DEMONSTRATION: INDIVIDUAL PERSONAS")
    print("=" * 60)
    
    # Create the three personas
    cautious_persona = CautiousPersona()
    aggressive_persona = AggressivePersona()
    intuitive_persona = IntuitivePersona()
    
    # Create agents
    cautious_agent = HumanPersonaAgent("Cautious Player", cautious_persona, bet_amount=5.0)
    aggressive_agent = HumanPersonaAgent("Aggressive Player", aggressive_persona, bet_amount=10.0)
    intuitive_agent = HumanPersonaAgent("Intuitive Player", intuitive_persona, bet_amount=7.5)
    
    agents = [cautious_agent, aggressive_agent, intuitive_agent]
    
    print("\n📊 PERSONA CONFIGURATIONS:")
    print("-" * 40)
    
    for agent in agents:
        config = agent.persona.config
        pattern = config.decision_pattern
        
        print(f"\n{config.name}:")
        print(f"  Description: {config.description}")
        print(f"  Base Accuracy: {pattern.base_accuracy:.1%}")
        print(f"  Decision Speed: {pattern.decision_speed:.1f}x")
        print(f"  Aggression Bias: {pattern.aggression_bias:+.2f}")
        print(f"  Risk Tolerance: {pattern.risk_tolerance:.1%}")
        print(f"  Avg Decision Time: {pattern.avg_decision_time:.1f}s")
    
    print("\n🎮 RUNNING SHORT SIMULATIONS:")
    print("-" * 40)
    
    # Run short simulations for each persona
    sim_config = SimulationConfig(
        num_hands=200,
        verbose=False,
        random_seed=42
    )
    
    simulator = BlackjackSimulator(sim_config)
    results = {}
    
    for agent in agents:
        print(f"\nSimulating {agent.persona.config.name}...")
        result = simulator.run_simulation(agent)
        results[agent.name] = result
        
        # Display key metrics
        stats = result['agent_stats']
        behavioral = stats.get('behavioral_stats', {})
        persona_metrics = stats.get('persona_metrics', {})
        
        print(f"  Hands Played: {stats['hands_played']}")
        print(f"  Win Rate: {stats['win_rate']:.1%}")
        print(f"  Actual Accuracy: {behavioral.get('accuracy', 0):.1%}")
        print(f"  Avg Decision Time: {persona_metrics.get('avg_decision_time', 0):.2f}s")
        print(f"  Context Switches: {persona_metrics.get('context_switches', 0)}")
    
    print("\n📈 BEHAVIORAL COMPARISON:")
    print("-" * 40)
    
    # Compare behavioral differences
    for agent_name, result in results.items():
        stats = result['agent_stats']
        behavioral = stats.get('behavioral_stats', {})
        
        if behavioral:
            print(f"\n{agent_name}:")
            print(f"  Error Rate: {1 - behavioral.get('accuracy', 1):.1%}")
            print(f"  Timing Variance: {behavioral.get('timing_variance', 0):.2f}s")
            
            context_dist = behavioral.get('context_distribution', {})
            if context_dist:
                print(f"  Context Distribution:")
                for context, pct in context_dist.items():
                    print(f"    {context}: {pct:.1%}")


def demonstrate_persona_switcher():
    """Demonstrate the persona switcher functionality."""
    print("\n\n" + "=" * 60)
    print("PHASE 2 DEMONSTRATION: PERSONA SWITCHER")
    print("=" * 60)
    
    # Create switcher configuration
    switch_config = SwitchConfig(
        min_switch_time=30.0,  # 30 seconds minimum
        max_switch_time=180.0,  # 3 minutes maximum
        min_hands_per_persona=25,
        max_hands_per_persona=75,
        switch_on_loss_streak=4,
        switch_on_win_streak=6,
        random_switch_probability=0.02,
        persona_weights={
            "cautious": 0.3,
            "aggressive": 0.4,
            "intuitive": 0.3
        }
    )
    
    # Create switcher agent
    switcher_agent = PersonaSwitcherAgent("Adaptive Player", switch_config, bet_amount=10.0)
    
    print(f"\n🔄 SWITCHER CONFIGURATION:")
    print("-" * 40)
    print(f"Min/Max Switch Time: {switch_config.min_switch_time:.0f}s - {switch_config.max_switch_time:.0f}s")
    print(f"Min/Max Hands per Persona: {switch_config.min_hands_per_persona} - {switch_config.max_hands_per_persona}")
    print(f"Loss Streak Trigger: {switch_config.switch_on_loss_streak} losses")
    print(f"Win Streak Trigger: {switch_config.switch_on_win_streak} wins")
    print(f"Random Switch Probability: {switch_config.random_switch_probability:.1%}")
    print(f"Persona Weights: {switch_config.persona_weights}")
    
    print(f"\n🎯 INITIAL STATE:")
    print("-" * 40)
    print(f"Starting Persona: {switcher_agent.get_current_persona_name()}")
    
    # Run longer simulation to see switching behavior
    print(f"\n🎮 RUNNING EXTENDED SIMULATION:")
    print("-" * 40)
    
    sim_config = SimulationConfig(
        num_hands=500,
        verbose=False,
        random_seed=123
    )
    
    simulator = BlackjackSimulator(sim_config)
    print("Simulating 500 hands with persona switching...")
    
    result = simulator.run_simulation(switcher_agent)
    
    # Analyze switching behavior
    stats = result['agent_stats']
    switching_stats = stats.get('switching_stats', {})
    
    print(f"\n📊 SWITCHING RESULTS:")
    print("-" * 40)
    print(f"Total Switches: {switching_stats.get('total_switches', 0)}")
    print(f"Final Persona: {stats.get('current_persona', 'unknown')}")
    print(f"Hands with Final Persona: {switching_stats.get('hands_with_current', 0)}")
    print(f"Avg Hands per Persona: {switching_stats.get('avg_hands_per_persona', 0):.1f}")
    
    # Show trigger distribution
    trigger_dist = switching_stats.get('trigger_distribution', {})
    if trigger_dist:
        print(f"\nSwitch Triggers:")
        for trigger, count in trigger_dist.items():
            print(f"  {trigger}: {count}")
    
    # Show persona usage
    persona_usage = switching_stats.get('persona_usage', {})
    if persona_usage:
        total_hands = sum(persona_usage.values())
        print(f"\nPersona Usage:")
        for persona, hands in persona_usage.items():
            pct = hands / total_hands if total_hands > 0 else 0
            print(f"  {persona}: {hands} hands ({pct:.1%})")
    
    # Detection risk assessment
    risk_assessment = switcher_agent.get_detection_risk_assessment()
    print(f"\n🛡️ DETECTION RISK ASSESSMENT:")
    print("-" * 40)
    print(f"Overall Risk: {risk_assessment['overall_risk']}")
    print(f"Consistency Risk: {risk_assessment['consistency_risk']:.1%}")
    print(f"Switch Frequency: {risk_assessment['switch_frequency']:.3f}")
    
    recommendations = risk_assessment.get('recommendations', [])
    if recommendations:
        print(f"\nRecommendations:")
        for rec in recommendations:
            print(f"  • {rec}")


def demonstrate_behavioral_differences():
    """Demonstrate measurable behavioral differences between personas."""
    print("\n\n" + "=" * 60)
    print("PHASE 2 DEMONSTRATION: BEHAVIORAL ANALYSIS")
    print("=" * 60)
    
    # Create all agent types
    cautious_agent = HumanPersonaAgent("Cautious", CautiousPersona())
    aggressive_agent = HumanPersonaAgent("Aggressive", AggressivePersona())
    intuitive_agent = HumanPersonaAgent("Intuitive", IntuitivePersona())
    
    switch_config = SwitchConfig(min_hands_per_persona=50, max_hands_per_persona=100)
    switcher_agent = PersonaSwitcherAgent("Switcher", switch_config)
    
    agents = [cautious_agent, aggressive_agent, intuitive_agent, switcher_agent]
    
    print("\n🧪 BEHAVIORAL ANALYSIS (100 hands each):")
    print("-" * 50)
    
    # Run analysis simulation
    sim_config = SimulationConfig(
        num_hands=100,
        verbose=False,
        random_seed=456
    )
    
    simulator = BlackjackSimulator(sim_config)
    
    analysis_results = []
    
    for agent in agents:
        result = simulator.run_simulation(agent)
        stats = result['agent_stats']
        
        # Extract key behavioral metrics
        behavioral = stats.get('behavioral_stats', {})
        persona_metrics = stats.get('persona_metrics', {})
        
        analysis_data = {
            'name': agent.name,
            'type': type(agent).__name__,
            'win_rate': stats.get('win_rate', 0),
            'accuracy': behavioral.get('accuracy', 0),
            'avg_decision_time': persona_metrics.get('avg_decision_time', 0),
            'timing_variance': behavioral.get('timing_variance', 0),
            'error_count': behavioral.get('error_count', 0),
            'context_switches': persona_metrics.get('context_switches', 0)
        }
        
        if hasattr(agent, 'get_current_persona_name'):
            analysis_data['final_persona'] = agent.get_current_persona_name()
            switching_stats = stats.get('switching_stats', {})
            analysis_data['total_switches'] = switching_stats.get('total_switches', 0)
        
        analysis_results.append(analysis_data)
    
    # Display analysis
    print(f"{'Agent':<12} {'Type':<20} {'Win%':<6} {'Acc%':<6} {'Time':<6} {'Var':<6} {'Err':<4}")
    print("-" * 70)
    
    for data in analysis_results:
        print(f"{data['name']:<12} {data['type']:<20} "
              f"{data['win_rate']:<6.1%} {data['accuracy']:<6.1%} "
              f"{data['avg_decision_time']:<6.2f} {data['timing_variance']:<6.2f} "
              f"{data['error_count']:<4}")
    
    print("\n📋 BEHAVIORAL SUMMARY:")
    print("-" * 40)
    
    # Find patterns
    accuracies = [d['accuracy'] for d in analysis_results[:3]]  # Exclude switcher
    times = [d['avg_decision_time'] for d in analysis_results[:3]]
    
    print(f"Accuracy Range: {min(accuracies):.1%} - {max(accuracies):.1%}")
    print(f"Decision Time Range: {min(times):.2f}s - {max(times):.2f}s")
    
    # Switcher specific analysis
    switcher_data = analysis_results[3]
    if 'total_switches' in switcher_data:
        print(f"\nSwitcher Analysis:")
        print(f"  Total Switches: {switcher_data['total_switches']}")
        print(f"  Final Persona: {switcher_data.get('final_persona', 'unknown')}")
        print(f"  Switch Rate: {switcher_data['total_switches']/100:.1%} per hand")


def main():
    """Main demonstration function."""
    print("🎭 BLACKJACK BOT ML - PHASE 2 HUMAN PERSONA SIMULATION")
    print("=" * 70)
    print("Demonstrating realistic human-like playing patterns with:")
    print("• Configurable error rates and decision timing")
    print("• Three distinct personas (Cautious, Aggressive, Intuitive)")
    print("• Dynamic persona switching for detection avoidance")
    print("=" * 70)
    
    try:
        # Demonstrate individual personas
        demonstrate_individual_personas()
        
        # Demonstrate persona switcher
        demonstrate_persona_switcher()
        
        # Demonstrate behavioral analysis
        demonstrate_behavioral_differences()
        
        print("\n\n" + "=" * 60)
        print("✅ PHASE 2 DEMONSTRATION COMPLETE")
        print("=" * 60)
        print("\nPhase 2 Implementation Summary:")
        print("• ✅ P2_T1: Base persona framework with timing and error simulation")
        print("• ✅ P2_T2: Three specific personas with distinct characteristics")
        print("• ✅ P2_T3: Persona switcher with detection avoidance")
        print("\nKey Features Demonstrated:")
        print("• Realistic decision timing with human-like variance")
        print("• Configurable error rates and behavioral patterns")
        print("• Context-sensitive decision making")
        print("• Dynamic persona switching based on multiple triggers")
        print("• Pattern detection avoidance mechanisms")
        print("• Comprehensive behavioral statistics and analysis")
        
    except Exception as e:
        print(f"\n❌ Error during demonstration: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
