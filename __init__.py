"""
BlackJack Bot ML - A comprehensive machine learning blackjack bot with evasion capabilities.

This package implements a multi-phase blackjack bot that progresses from basic strategy
to human-like personas to reinforcement learning with detection evasion.

Phases:
1. Basic Strategy Implementation
2. Human Persona Simulation  
3. Reinforcement Learning with Evasion
4. CLI Interface

Author: Augment Agent
"""

__version__ = "1.0.0"
__author__ = "Augment Agent"
