"""
Deck management for BlackJack Bot ML.

This module implements deck functionality including:
- Standard 52-card deck creation
- Multi-deck support (6-deck for this project)
- Continuous Shuffle Machine (CSM) simulation
- Proper shuffling mechanics
"""

import random
from typing import List, Optional
from .card import Card, Suit, Rank


class Deck:
    """
    Represents a deck of playing cards with blackjack-specific functionality.
    
    Supports multi-deck configurations and Continuous Shuffle Machine (CSM)
    simulation where cards are shuffled after each hand.
    """
    
    def __init__(self, num_decks: int = 6, shuffle_after_each_hand: bool = True):
        """
        Initialize a deck with specified number of standard 52-card decks.
        
        Args:
            num_decks: Number of standard decks to combine (default: 6 for blackjack)
            shuffle_after_each_hand: If True, simulates CSM behavior
        """
        self.num_decks = num_decks
        self.shuffle_after_each_hand = shuffle_after_each_hand
        self.cards: List[Card] = []
        self.dealt_cards: List[Card] = []
        self._create_deck()
        self.shuffle()
    
    def _create_deck(self) -> None:
        """Create the deck with all cards."""
        self.cards = []
        for _ in range(self.num_decks):
            for suit in Suit:
                for rank in Rank:
                    self.cards.append(Card(suit, rank))
    
    def shuffle(self) -> None:
        """
        Shuffle the deck using Fisher-<PERSON> algorithm.
        
        In CSM mode, this also returns dealt cards to the deck.
        """
        if self.shuffle_after_each_hand:
            # Return dealt cards to deck (CSM simulation)
            self.cards.extend(self.dealt_cards)
            self.dealt_cards = []
        
        # Fisher-Yates shuffle for cryptographically secure randomization
        for i in range(len(self.cards) - 1, 0, -1):
            j = random.randint(0, i)
            self.cards[i], self.cards[j] = self.cards[j], self.cards[i]
    
    def deal_card(self) -> Optional[Card]:
        """
        Deal one card from the top of the deck.
        
        Returns:
            The dealt card, or None if deck is empty
        """
        if not self.cards:
            if self.shuffle_after_each_hand and self.dealt_cards:
                # In CSM mode, reshuffle if no cards left
                self.shuffle()
            else:
                return None
        
        if self.cards:
            card = self.cards.pop()
            self.dealt_cards.append(card)
            return card
        return None
    
    def deal_cards(self, count: int) -> List[Card]:
        """
        Deal multiple cards from the deck.
        
        Args:
            count: Number of cards to deal
            
        Returns:
            List of dealt cards (may be fewer than requested if deck runs out)
        """
        dealt = []
        for _ in range(count):
            card = self.deal_card()
            if card is None:
                break
            dealt.append(card)
        return dealt
    
    def cards_remaining(self) -> int:
        """Get the number of cards remaining in the deck."""
        return len(self.cards)
    
    def cards_dealt(self) -> int:
        """Get the number of cards that have been dealt."""
        return len(self.dealt_cards)
    
    def total_cards(self) -> int:
        """Get the total number of cards in the deck system."""
        return self.num_decks * 52
    
    def reset(self) -> None:
        """Reset the deck to initial state with all cards."""
        self._create_deck()
        self.dealt_cards = []
        self.shuffle()
    
    def penetration(self) -> float:
        """
        Calculate deck penetration (percentage of cards dealt).

        Returns:
            Penetration as a float between 0.0 and 1.0
        """
        total = self.total_cards()
        if total == 0:
            return 0.0
        return self.cards_dealt() / total

    def get_penetration(self) -> float:
        """
        Get deck penetration (alias for penetration method).

        Returns:
            Penetration as a float between 0.0 and 1.0
        """
        return self.penetration()
    
    def __len__(self) -> int:
        """Return number of cards remaining in deck."""
        return len(self.cards)
    
    def __str__(self) -> str:
        """String representation of deck status."""
        return f"Deck({self.num_decks} decks, {len(self.cards)} cards remaining)"
    
    def __repr__(self) -> str:
        """Detailed string representation for debugging."""
        return (f"Deck(num_decks={self.num_decks}, "
                f"shuffle_after_each_hand={self.shuffle_after_each_hand}, "
                f"cards_remaining={len(self.cards)}, "
                f"cards_dealt={len(self.dealt_cards)})")
