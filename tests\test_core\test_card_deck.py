"""
Unit tests for Card and Deck classes.

Tests P1_T1 implementation: Card and Deck classes with proper shuffle mechanics.
"""

import unittest
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from core.card import Card, Suit, Rank
from core.deck import Deck
from core.game_logic import BlackjackGame, GameAction, GameResult


class TestCard(unittest.TestCase):
    """Test cases for the Card class."""
    
    def test_card_creation(self):
        """Test basic card creation."""
        card = Card(Suit.HEARTS, Rank.ACE)
        self.assertEqual(card.suit, Suit.HEARTS)
        self.assertEqual(card.rank, Rank.ACE)
    
    def test_card_string_representation(self):
        """Test card string representation."""
        card = Card(Suit.SPADES, Rank.KING)
        self.assertEqual(str(card), "K♠")
    
    def test_ace_identification(self):
        """Test Ace identification."""
        ace = Card(Suit.HEARTS, Rank.ACE)
        king = Card(Suit.SPADES, Rank.KING)
        self.assertTrue(ace.is_ace)
        self.assertFalse(king.is_ace)
    
    def test_card_values(self):
        """Test blackjack value calculation."""
        ace = Card(Suit.HEARTS, Rank.ACE)
        king = Card(Suit.SPADES, Rank.KING)
        five = Card(Suit.DIAMONDS, Rank.FIVE)
        
        # Test Ace values
        self.assertEqual(ace.get_value(count_ace_as_eleven=True), 11)
        self.assertEqual(ace.get_value(count_ace_as_eleven=False), 1)
        
        # Test face card values
        self.assertEqual(king.get_value(), 10)
        
        # Test number card values
        self.assertEqual(five.get_value(), 5)
    
    def test_card_equality(self):
        """Test card equality comparison."""
        card1 = Card(Suit.HEARTS, Rank.ACE)
        card2 = Card(Suit.HEARTS, Rank.ACE)
        card3 = Card(Suit.SPADES, Rank.ACE)
        
        self.assertEqual(card1, card2)
        self.assertNotEqual(card1, card3)


class TestDeck(unittest.TestCase):
    """Test cases for the Deck class."""
    
    def test_deck_creation(self):
        """Test basic deck creation."""
        deck = Deck(num_decks=1, shuffle_after_each_hand=False)
        self.assertEqual(len(deck), 52)
        self.assertEqual(deck.total_cards(), 52)
    
    def test_multi_deck_creation(self):
        """Test multi-deck creation."""
        deck = Deck(num_decks=6, shuffle_after_each_hand=False)
        self.assertEqual(len(deck), 312)  # 6 * 52
        self.assertEqual(deck.total_cards(), 312)
    
    def test_card_dealing(self):
        """Test card dealing functionality."""
        deck = Deck(num_decks=1, shuffle_after_each_hand=False)
        initial_count = len(deck)
        
        card = deck.deal_card()
        self.assertIsNotNone(card)
        self.assertIsInstance(card, Card)
        self.assertEqual(len(deck), initial_count - 1)
        self.assertEqual(deck.cards_dealt(), 1)
    
    def test_multiple_card_dealing(self):
        """Test dealing multiple cards."""
        deck = Deck(num_decks=1, shuffle_after_each_hand=False)
        cards = deck.deal_cards(5)
        
        self.assertEqual(len(cards), 5)
        self.assertEqual(len(deck), 47)  # 52 - 5
        self.assertEqual(deck.cards_dealt(), 5)
    
    def test_csm_simulation(self):
        """Test Continuous Shuffle Machine simulation."""
        deck = Deck(num_decks=1, shuffle_after_each_hand=True)
        
        # Deal some cards
        cards = deck.deal_cards(10)
        self.assertEqual(len(cards), 10)
        self.assertEqual(deck.cards_dealt(), 10)
        
        # Shuffle should return dealt cards to deck
        deck.shuffle()
        self.assertEqual(len(deck), 52)
        self.assertEqual(deck.cards_dealt(), 0)
    
    def test_deck_penetration(self):
        """Test deck penetration calculation."""
        deck = Deck(num_decks=1, shuffle_after_each_hand=False)
        
        # Initially no penetration
        self.assertEqual(deck.penetration(), 0.0)
        
        # Deal half the deck
        deck.deal_cards(26)
        self.assertAlmostEqual(deck.penetration(), 0.5, places=2)
        
        # Deal entire deck
        deck.deal_cards(26)
        self.assertEqual(deck.penetration(), 1.0)
    
    def test_deck_reset(self):
        """Test deck reset functionality."""
        deck = Deck(num_decks=1, shuffle_after_each_hand=False)
        
        # Deal some cards
        deck.deal_cards(20)
        self.assertEqual(len(deck), 32)
        
        # Reset deck
        deck.reset()
        self.assertEqual(len(deck), 52)
        self.assertEqual(deck.cards_dealt(), 0)


if __name__ == '__main__':
    unittest.main()
