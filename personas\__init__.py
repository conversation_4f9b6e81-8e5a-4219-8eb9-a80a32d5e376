"""
Human Persona Simulation for BlackJack Bot ML.

This module implements realistic human-like playing patterns with:
- Configurable error rates and strategy deviations
- Realistic decision timing simulation
- Behavioral pattern tracking
- Multiple persona types with distinct characteristics

Phase 2 Implementation:
- Base persona framework
- Three specific personas (Cautious, Aggressive, Intuitive)
- Persona switcher for detection avoidance
"""

from .base_persona import Base<PERSON>ersona, PersonaConfig, DecisionPattern, DecisionContext, ErrorType
from .human_persona_agent import HumanPersonaAgent
from .cautious_persona import CautiousPersona, create_cautious_persona_config
from .aggressive_persona import AggressivePersona, create_aggressive_persona_config
from .intuitive_persona import Intuitive<PERSON>ersona, create_intuitive_persona_config
from .persona_switcher import Persona<PERSON>witcher, SwitchConfig, SwitchTrigger
from .persona_switcher_agent import PersonaSwitcherAgent

__all__ = [
    'BasePersona', 'PersonaConfig', 'DecisionPattern', 'DecisionContext', 'ErrorType',
    'HumanPersonaAgent',
    'CautiousPersona', 'create_cautious_persona_config',
    'AggressivePersona', 'create_aggressive_persona_config',
    'IntuitivePersona', 'create_intuitive_persona_config',
    'PersonaSwitcher', 'SwitchConfig', 'SwitchTrigger',
    'PersonaSwitcherAgent'
]
