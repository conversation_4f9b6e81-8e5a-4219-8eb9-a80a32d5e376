"""
Hand representation for BlackJack Bot ML.

This module implements hand management with accurate Ace handling,
including soft/hard hand detection and optimal value calculation.
"""

from typing import List, Tuple, Optional
from .card import Card


class Hand:
    """
    Represents a blackjack hand with proper Ace handling.
    
    Manages cards in a hand and calculates optimal blackjack values,
    handling the dual nature of Aces (1 or 11) automatically.
    """
    
    def __init__(self, cards: Optional[List[Card]] = None):
        """
        Initialize a hand with optional starting cards.
        
        Args:
            cards: Optional list of cards to start with
        """
        self.cards: List[Card] = cards.copy() if cards else []
        self._is_split_hand = False
        self._split_from_aces = False
    
    def add_card(self, card: Card) -> None:
        """
        Add a card to the hand.
        
        Args:
            card: The card to add
        """
        self.cards.append(card)
    
    def add_cards(self, cards: List[Card]) -> None:
        """
        Add multiple cards to the hand.
        
        Args:
            cards: List of cards to add
        """
        self.cards.extend(cards)
    
    def clear(self) -> None:
        """Clear all cards from the hand."""
        self.cards = []
        self._is_split_hand = False
        self._split_from_aces = False
    
    def get_values(self) -> Tuple[int, int]:
        """
        Get the minimum and maximum possible values for this hand.
        
        Returns:
            Tuple of (min_value, max_value) considering Ace flexibility
        """
        if not self.cards:
            return (0, 0)
        
        min_value = 0
        aces_count = 0
        
        # Calculate minimum value (all Aces as 1)
        for card in self.cards:
            if card.is_ace:
                aces_count += 1
                min_value += 1
            else:
                min_value += card.get_value()
        
        # Calculate maximum value (as many Aces as 11 as possible)
        max_value = min_value
        aces_as_eleven = 0
        
        # Try to make Aces worth 11, but only if it doesn't bust
        while aces_as_eleven < aces_count and max_value + 10 <= 21:
            max_value += 10
            aces_as_eleven += 1
        
        return (min_value, max_value)
    
    def get_value(self) -> int:
        """
        Get the optimal blackjack value for this hand.
        
        Returns the highest value that doesn't exceed 21, or the lowest value if all bust.
        
        Returns:
            The optimal blackjack value
        """
        min_val, max_val = self.get_values()
        
        # Return the highest value that doesn't bust
        if max_val <= 21:
            return max_val
        else:
            return min_val
    
    def is_soft(self) -> bool:
        """
        Check if this is a soft hand (contains an Ace counted as 11).
        
        Returns:
            True if hand is soft, False otherwise
        """
        if not any(card.is_ace for card in self.cards):
            return False
        
        min_val, max_val = self.get_values()
        return max_val != min_val and max_val <= 21
    
    def is_hard(self) -> bool:
        """
        Check if this is a hard hand (no Aces or all Aces counted as 1).
        
        Returns:
            True if hand is hard, False otherwise
        """
        return not self.is_soft()
    
    def is_blackjack(self) -> bool:
        """
        Check if this hand is a natural blackjack (21 with exactly 2 cards).
        
        Returns:
            True if natural blackjack, False otherwise
        """
        return len(self.cards) == 2 and self.get_value() == 21
    
    def is_bust(self) -> bool:
        """
        Check if this hand is bust (value exceeds 21).
        
        Returns:
            True if bust, False otherwise
        """
        return self.get_value() > 21
    
    def is_pair(self) -> bool:
        """
        Check if this hand is a pair (exactly 2 cards of same rank).
        
        Returns:
            True if pair, False otherwise
        """
        return (len(self.cards) == 2 and 
                self.cards[0].rank == self.cards[1].rank)
    
    def can_split(self) -> bool:
        """
        Check if this hand can be split.
        
        Returns:
            True if hand can be split, False otherwise
        """
        return self.is_pair() and not self._is_split_hand
    
    def can_double(self) -> bool:
        """
        Check if this hand can be doubled down.
        
        Returns:
            True if hand can be doubled, False otherwise
        """
        # Can double on first two cards, or after split (depending on rules)
        return len(self.cards) == 2
    
    def split(self) -> 'Hand':
        """
        Split this hand into two hands.
        
        Returns:
            New hand with the second card
            
        Raises:
            ValueError: If hand cannot be split
        """
        if not self.can_split():
            raise ValueError("Cannot split this hand")
        
        # Create new hand with second card
        second_card = self.cards.pop()
        new_hand = Hand([second_card])
        
        # Mark both hands as split hands
        self._is_split_hand = True
        new_hand._is_split_hand = True
        
        # Check if splitting Aces
        if self.cards[0].is_ace:
            self._split_from_aces = True
            new_hand._split_from_aces = True
        
        return new_hand
    
    def count_cards(self) -> int:
        """Get the number of cards in this hand."""
        return len(self.cards)
    
    def get_cards(self) -> List[Card]:
        """Get a copy of the cards in this hand."""
        return self.cards.copy()
    
    def __str__(self) -> str:
        """String representation of the hand."""
        if not self.cards:
            return "Empty hand"
        
        cards_str = " ".join(str(card) for card in self.cards)
        value = self.get_value()
        hand_type = "soft" if self.is_soft() else "hard"
        
        return f"{cards_str} ({hand_type} {value})"
    
    def __repr__(self) -> str:
        """Detailed string representation for debugging."""
        return f"Hand(cards={self.cards}, value={self.get_value()})"
    
    def __len__(self) -> int:
        """Return number of cards in hand."""
        return len(self.cards)
