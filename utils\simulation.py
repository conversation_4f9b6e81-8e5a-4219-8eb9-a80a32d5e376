"""
Simulation environment for BlackJack Bot ML.

This module provides a comprehensive simulation framework for testing
blackjack agents, collecting statistics, and analyzing performance.
"""

import time
import random
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from core.game_logic import Blackjack<PERSON>ame, GameResult, GameAction
from agents.base_agent import BaseAgent


@dataclass
class SimulationConfig:
    """Configuration for blackjack simulations."""
    num_hands: int = 1000
    min_bet: float = 1.0
    max_bet: float = 100.0
    num_decks: int = 6
    verbose: bool = False
    collect_detailed_stats: bool = True
    random_seed: Optional[int] = None


@dataclass
class HandResult:
    """Result of a single hand."""
    hand_number: int
    bet_amount: float
    player_hands: List[str]  # String representations of hands
    dealer_hand: str
    actions_taken: List[List[GameAction]]  # Actions for each player hand
    results: List[GameResult]
    winnings: float
    decision_times: List[float]


class BlackjackSimulator:
    """
    Comprehensive blackjack simulation environment.
    
    Provides tools for testing agents, collecting statistics,
    and analyzing performance over many hands.
    """
    
    def __init__(self, config: SimulationConfig = None):
        """
        Initialize the simulator.
        
        Args:
            config: Simulation configuration
        """
        self.config = config or SimulationConfig()
        
        if self.config.random_seed is not None:
            random.seed(self.config.random_seed)
        
        self.game = BlackjackGame(num_decks=self.config.num_decks)
        self.hand_results: List[HandResult] = []
    
    def run_simulation(self, agent: BaseAgent, 
                      progress_callback: Optional[Callable[[int, int], None]] = None) -> Dict[str, Any]:
        """
        Run a complete simulation with the given agent.
        
        Args:
            agent: The agent to test
            progress_callback: Optional callback for progress updates
            
        Returns:
            Comprehensive simulation results
        """
        self.hand_results = []
        agent.reset_stats()
        
        start_time = time.time()
        
        for hand_num in range(self.config.num_hands):
            if progress_callback and hand_num % 100 == 0:
                progress_callback(hand_num, self.config.num_hands)
            
            hand_result = self._play_single_hand(agent, hand_num)
            
            if self.config.collect_detailed_stats:
                self.hand_results.append(hand_result)
            
            if self.config.verbose and hand_num % 100 == 0:
                print(f"Hand {hand_num}: {agent.get_stats()['win_rate']:.3f} win rate")
        
        end_time = time.time()
        simulation_time = end_time - start_time
        
        # Compile final results
        results = {
            'agent_name': agent.name,
            'config': self.config,
            'agent_stats': agent.get_stats(),
            'simulation_time': simulation_time,
            'hands_per_second': self.config.num_hands / simulation_time,
        }
        
        if self.config.collect_detailed_stats:
            results['detailed_analysis'] = self._analyze_detailed_results()
            results['hand_results'] = self.hand_results
        
        return results
    
    def _play_single_hand(self, agent: BaseAgent, hand_number: int) -> HandResult:
        """
        Play a single hand and collect detailed results.
        
        Args:
            agent: The agent playing
            hand_number: Current hand number
            
        Returns:
            Detailed hand result
        """
        # Get bet amount
        bet_amount = agent.get_bet_amount(self.config.min_bet, self.config.max_bet)
        
        # Start new game
        game_state = self.game.start_new_game(bet_amount)
        
        # Track actions and timing
        all_actions = []
        decision_times = []
        
        # Play all player hands
        while not game_state.game_over:
            current_hand_actions = []
            
            # Get available actions
            available_actions = self.game.get_available_actions()
            
            if not available_actions:
                break
            
            # Time the decision
            start_time = time.time()
            action = agent.get_action(game_state, game_state.current_hand_index)
            decision_time = time.time() - start_time
            
            decision_times.append(decision_time)
            current_hand_actions.append(action)
            
            # Take the action
            game_state = self.game.take_action(action)
            
            # If we moved to next hand, save actions for current hand
            if (game_state.current_hand_index > len(all_actions) or 
                game_state.game_over):
                all_actions.append(current_hand_actions)
        
        # Calculate winnings
        winnings = self._calculate_winnings(game_state, bet_amount)
        
        # Update agent stats
        self._update_agent_stats(agent, game_state, bet_amount, winnings)
        
        # Create hand result
        hand_result = HandResult(
            hand_number=hand_number,
            bet_amount=bet_amount,
            player_hands=[str(hand) for hand in game_state.player_hands],
            dealer_hand=str(game_state.dealer_hand),
            actions_taken=all_actions,
            results=game_state.results,
            winnings=winnings,
            decision_times=decision_times
        )
        
        return hand_result
    
    def _calculate_winnings(self, game_state, bet_amount: float) -> float:
        """Calculate total winnings for all hands."""
        total_winnings = 0.0
        
        for result in game_state.results:
            if result == GameResult.PLAYER_WIN:
                total_winnings += bet_amount
            elif result == GameResult.PLAYER_BLACKJACK:
                total_winnings += bet_amount * 1.5  # 3:2 payout
            elif result == GameResult.DEALER_BUST:
                total_winnings += bet_amount
            elif result == GameResult.PLAYER_BUST:
                total_winnings -= bet_amount
            elif result == GameResult.DEALER_WIN:
                total_winnings -= bet_amount
            elif result == GameResult.DEALER_BLACKJACK:
                total_winnings -= bet_amount
            # PUSH = no change
        
        return total_winnings
    
    def _update_agent_stats(self, agent: BaseAgent, game_state, 
                           bet_amount: float, winnings: float) -> None:
        """Update agent statistics based on hand results."""
        for result in game_state.results:
            if result == GameResult.PLAYER_WIN:
                agent.update_stats('win', bet_amount, bet_amount)
            elif result == GameResult.PLAYER_BLACKJACK:
                agent.update_stats('blackjack', bet_amount, bet_amount * 1.5)
            elif result == GameResult.DEALER_BUST:
                agent.update_stats('win', bet_amount, bet_amount)
            elif result == GameResult.PLAYER_BUST:
                agent.update_stats('bust', bet_amount, -bet_amount)
            elif result == GameResult.DEALER_WIN:
                agent.update_stats('loss', bet_amount, -bet_amount)
            elif result == GameResult.DEALER_BLACKJACK:
                agent.update_stats('loss', bet_amount, -bet_amount)
            elif result == GameResult.PUSH:
                agent.update_stats('push', bet_amount, 0.0)
    
    def _analyze_detailed_results(self) -> Dict[str, Any]:
        """Analyze detailed simulation results."""
        if not self.hand_results:
            return {}
        
        analysis = {
            'total_hands': len(self.hand_results),
            'total_bet': sum(hr.bet_amount for hr in self.hand_results),
            'total_winnings': sum(hr.winnings for hr in self.hand_results),
            'average_decision_time': sum(
                sum(hr.decision_times) for hr in self.hand_results
            ) / sum(len(hr.decision_times) for hr in self.hand_results),
            'action_frequency': self._analyze_action_frequency(),
            'result_frequency': self._analyze_result_frequency(),
            'betting_patterns': self._analyze_betting_patterns(),
        }
        
        return analysis
    
    def _analyze_action_frequency(self) -> Dict[str, int]:
        """Analyze frequency of different actions."""
        action_counts = {}
        
        for hand_result in self.hand_results:
            for hand_actions in hand_result.actions_taken:
                for action in hand_actions:
                    action_name = action.value
                    action_counts[action_name] = action_counts.get(action_name, 0) + 1
        
        return action_counts
    
    def _analyze_result_frequency(self) -> Dict[str, int]:
        """Analyze frequency of different game results."""
        result_counts = {}
        
        for hand_result in self.hand_results:
            for result in hand_result.results:
                result_name = result.value
                result_counts[result_name] = result_counts.get(result_name, 0) + 1
        
        return result_counts
    
    def _analyze_betting_patterns(self) -> Dict[str, float]:
        """Analyze betting patterns."""
        bet_amounts = [hr.bet_amount for hr in self.hand_results]
        
        return {
            'min_bet': min(bet_amounts),
            'max_bet': max(bet_amounts),
            'average_bet': sum(bet_amounts) / len(bet_amounts),
            'bet_variance': self._calculate_variance(bet_amounts),
        }
    
    def _calculate_variance(self, values: List[float]) -> float:
        """Calculate variance of a list of values."""
        if len(values) < 2:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / (len(values) - 1)
        return variance
    
    def export_results(self, filepath: str, results: Dict[str, Any]) -> None:
        """Export simulation results to JSON file."""
        import json
        import os
        
        # Convert non-serializable objects to strings
        serializable_results = self._make_serializable(results)
        
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w') as f:
            json.dump(serializable_results, f, indent=2)
    
    def _make_serializable(self, obj) -> Any:
        """Convert objects to JSON-serializable format."""
        if hasattr(obj, '__dict__'):
            return {k: self._make_serializable(v) for k, v in obj.__dict__.items()}
        elif isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, 'value'):  # Enum
            return obj.value
        else:
            return obj
