# BlackJack Bot ML

A comprehensive machine learning blackjack bot with evasion capabilities, built in Python. This project implements a multi-phase approach from basic strategy to advanced reinforcement learning with detection avoidance.

## 🎯 Project Overview

BlackJack Bot ML is designed to create intelligent blackjack agents that can:
- Play perfect Basic Strategy
- Simulate human-like playing patterns
- Learn optimal play through Reinforcement Learning
- Avoid detection by casino surveillance systems

### Project Phases

1. **Phase 1: Basic Strategy Foundation** ✅ **COMPLETE**
   - Perfect blackjack game engine
   - Basic Strategy implementation
   - Simulation framework
   - Progress tracking system

2. **Phase 2: Human Simulation** ✅ **COMPLETE**
   - Three distinct personas: Cautious (95%), Aggressive (90%), Intuitive (70%)
   - Realistic decision timing with complexity factors
   - Dynamic persona switching for detection avoidance
   - Comprehensive behavioral analytics

3. **Phase 3: Reinforcement Learning** � *In Development*
   - Deep Q-Network (DQN) implementation
   - Evasion reward functions
   - Detection simulation
   - Adaptive learning with persona integration

4. **Phase 4: CLI Interface** 🔮 *Planned*
   - Command-line interface
   - Configuration management
   - Real-time monitoring

## 🏗️ Architecture

```
BlackJackBotML/
├── core/                    # Game engine
│   ├── card.py             # Card representation
│   ├── deck.py             # Deck management with CSM
│   ├── hand.py             # Hand logic with Ace handling
│   └── game_logic.py       # Complete blackjack rules
├── agents/                  # Playing agents
│   ├── base_agent.py       # Abstract agent interface
│   └── basic_strategy_agent.py  # Perfect BS player
├── personas/                # Human behavior simulation
│   ├── base_persona.py     # Persona framework
│   ├── cautious_persona.py # Conservative player (95% accuracy)
│   ├── aggressive_persona.py # Action-oriented player (90% accuracy)
│   ├── intuitive_persona.py # Emotional player (70% accuracy)
│   ├── persona_switcher.py # Dynamic switching system
│   └── human_persona_agent.py # Persona agent wrapper
├── utils/                   # Utilities and tools
│   ├── basic_strategy_charts.py  # BS lookup tables
│   ├── simulation.py       # Testing framework
│   └── progress_manager.py # Session tracking
├── data/                    # Generated data
│   ├── basic_strategy_data/ # BS charts (JSON)
│   ├── trained_models/     # Saved models
│   ├── logs/               # Application logs
│   └── simulation_results/ # Test results
└── tests/                   # Comprehensive tests
    ├── test_core/          # Core game tests
    ├── test_agents/        # Agent tests
    └── test_personas/      # Persona behavior tests
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- No external dependencies required (uses only standard library)

### Installation

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd BlackJackBotML
   ```

2. **Verify installation**
   ```bash
   python -c "from core.game_logic import BlackjackGame; print('Installation successful!')"
   ```

### Basic Usage

#### 1. Play a Single Hand

```python
from core.game_logic import BlackjackGame, GameAction
from agents.basic_strategy_agent import BasicStrategyAgent

# Create game and agent
game = BlackjackGame(num_decks=6)
agent = BasicStrategyAgent("My Bot")

# Start a new hand
game_state = game.start_new_game(bet_amount=10.0)

# Play the hand
while not game_state.game_over:
    available_actions = game.get_available_actions()
    if not available_actions:
        break
    
    action = agent.get_action(game_state)
    game_state = game.take_action(action)
    
    print(f"Player: {game_state.player_hands[0]}")
    print(f"Dealer: {game_state.dealer_hand}")
    print(f"Action taken: {action}")

print(f"Results: {game_state.results}")
```

#### 2. Run a Simulation

```python
from utils.simulation import BlackjackSimulator, SimulationConfig
from agents.basic_strategy_agent import BasicStrategyAgent

# Configure simulation
config = SimulationConfig(
    num_hands=1000,
    min_bet=1.0,
    max_bet=100.0,
    verbose=True
)

# Create simulator and agent
simulator = BlackjackSimulator(config)
agent = BasicStrategyAgent("Test Agent")

# Run simulation
results = simulator.run_simulation(agent)

# View results
print(f"Win Rate: {results['agent_stats']['win_rate']:.3f}")
print(f"ROI: {results['agent_stats']['return_on_investment']:.3f}")
print(f"Hands/sec: {results['hands_per_second']:.1f}")
```

#### 3. Track Progress

```python
from utils.progress_manager import ProgressManager
from agents.basic_strategy_agent import BasicStrategyAgent

# Create progress manager
progress = ProgressManager()

# Start training session
session_id = progress.start_session(
    agent_type="BasicStrategy",
    parameters={"bet_amount": 5.0}
)

# Simulate training with checkpoints
agent = BasicStrategyAgent("Training Agent")
for hand in range(0, 1000, 100):
    # Update progress
    progress.update_session(
        current_hand=hand,
        metrics={"win_rate": 0.45 + hand * 0.0001}
    )
    
    # Save checkpoint every 100 hands
    if hand % 100 == 0:
        progress.save_checkpoint(
            hand_number=hand,
            model_data=agent.get_stats(),
            metrics={"accuracy": 0.95}
        )

# End session
progress.end_session(total_hands=1000)
```

#### 4. Use Human Personas

```python
from personas import CautiousPersona, AggressivePersona, IntuitivePersona
from personas import HumanPersonaAgent, PersonaSwitcherAgent, SwitchConfig

# Create individual personas
cautious_agent = HumanPersonaAgent("Cautious Player", CautiousPersona())
aggressive_agent = HumanPersonaAgent("Aggressive Player", AggressivePersona())
intuitive_agent = HumanPersonaAgent("Intuitive Player", IntuitivePersona())

# Run simulation with persona
results = simulator.run_simulation(cautious_agent)
print(f"Persona: {results['agent_stats']['persona_name']}")
print(f"Accuracy: {results['agent_stats']['behavioral_stats']['accuracy']:.1%}")
print(f"Avg Decision Time: {results['agent_stats']['persona_metrics']['avg_decision_time']:.2f}s")

# Create persona switcher for detection avoidance
switch_config = SwitchConfig(
    min_hands_per_persona=50,
    max_hands_per_persona=150,
    switch_on_loss_streak=4,
    switch_on_win_streak=6
)
switcher_agent = PersonaSwitcherAgent("Adaptive Player", switch_config)

# Run with dynamic switching
results = simulator.run_simulation(switcher_agent)
switching_stats = results['agent_stats']['switching_stats']
print(f"Total Switches: {switching_stats['total_switches']}")
print(f"Final Persona: {switching_stats['current_persona']}")
```

## 🎮 Game Rules

The game implements standard 6-deck blackjack with these specific rules:

- **6 decks** with Continuous Shuffle Machine (CSM) simulation
- **Dealer hits soft 17** (H17)
- **Double after split allowed** (DAS)
- **No resplit of Aces**
- **3:2 blackjack payout**
- **Betting range**: 1-100 units

### Supported Actions

- **Hit**: Take another card
- **Stand**: Keep current hand
- **Double**: Double bet and take exactly one more card
- **Split**: Split pairs into separate hands

## 📊 Basic Strategy

The bot includes complete Basic Strategy charts for 6-deck H17 DAS rules:

### Hard Totals
- Optimal decisions for hands without Aces (or Aces counted as 1)
- Covers totals 5-21 vs all dealer upcards

### Soft Totals  
- Optimal decisions for hands with Aces counted as 11
- Covers A,2 through A,9 vs all dealer upcards

### Pairs
- Optimal splitting decisions for all pairs
- Accounts for DAS rules and game constraints

### Usage Example

```python
from utils.basic_strategy_charts import BasicStrategyCharts
from core.hand import Hand
from core.card import Card, Suit, Rank

# Load Basic Strategy charts
charts = BasicStrategyCharts()

# Create a hand (hard 16)
hand = Hand()
hand.add_card(Card(Suit.HEARTS, Rank.TEN))
hand.add_card(Card(Suit.SPADES, Rank.SIX))

# Get optimal action vs dealer 10
action = charts.get_action(hand, dealer_upcard=10)
print(f"Optimal action: {action}")  # Should be HIT
```

## 🎭 Human Personas

Phase 2 introduces realistic human behavior simulation with three distinct personas and dynamic switching capabilities.

### Persona Types

#### 🛡️ Cautious Basic Strategy (95% Accuracy)
- **Characteristics**: Conservative, methodical, risk-averse
- **Decision Speed**: Slow and deliberate (avg 4.5s)
- **Behavior**: Tends toward safe plays, avoids risky doubles/splits
- **Error Pattern**: Occasional over-conservative decisions

```python
from personas import CautiousPersona, HumanPersonaAgent

cautious_persona = CautiousPersona()
agent = HumanPersonaAgent("Careful Player", cautious_persona)

# Configuration details
config = cautious_persona.config
print(f"Base Accuracy: {config.decision_pattern.base_accuracy:.1%}")
print(f"Decision Speed: {config.decision_pattern.decision_speed:.1f}x")
print(f"Aggression Bias: {config.decision_pattern.aggression_bias:+.2f}")
```

#### ⚡ Slightly Aggressive (90% Accuracy)
- **Characteristics**: Confident, action-oriented, risk-taking
- **Decision Speed**: Fast and confident (avg 1.8s)
- **Behavior**: Prefers hitting/doubling, takes calculated risks
- **Error Pattern**: Occasional over-aggressive plays

```python
from personas import AggressivePersona, HumanPersonaAgent

aggressive_persona = AggressivePersona()
agent = HumanPersonaAgent("Bold Player", aggressive_persona)

# Demonstrate aggressive bias
stats = agent.get_stats()
print(f"Risk Tolerance: {aggressive_persona.config.decision_pattern.risk_tolerance:.1%}")
```

#### 🎲 Intuitive/Emotional (70% Accuracy)
- **Characteristics**: Emotional, inconsistent, superstitious
- **Decision Speed**: Highly variable (0.2-15.0s range)
- **Behavior**: Decisions based on "feel", streaks, hunches
- **Error Pattern**: Frequent emotional and random decisions

```python
from personas import IntuitivePersona, HumanPersonaAgent

intuitive_persona = IntuitivePersona()
agent = HumanPersonaAgent("Emotional Player", intuitive_persona)

# Check emotional volatility
volatility = intuitive_persona.config.decision_pattern.emotional_volatility
print(f"Emotional Volatility: {volatility:.1%}")
```

### Dynamic Persona Switching

The persona switcher provides detection avoidance through dynamic behavior changes:

```python
from personas import PersonaSwitcherAgent, SwitchConfig

# Configure switching behavior
switch_config = SwitchConfig(
    min_switch_time=300.0,      # 5 minutes minimum
    max_switch_time=1800.0,     # 30 minutes maximum
    min_hands_per_persona=50,   # Minimum hands per persona
    max_hands_per_persona=200,  # Maximum hands per persona
    switch_on_loss_streak=5,    # Switch after 5 losses
    switch_on_win_streak=8,     # Switch after 8 wins
    random_switch_probability=0.02,  # 2% random switch chance
    persona_weights={           # Persona selection weights
        "cautious": 0.3,
        "aggressive": 0.4,
        "intuitive": 0.3
    }
)

# Create switcher agent
switcher = PersonaSwitcherAgent("Adaptive Player", switch_config)

# Monitor switching behavior
results = simulator.run_simulation(switcher)
risk_assessment = switcher.get_detection_risk_assessment()
print(f"Detection Risk: {risk_assessment['overall_risk']}")
print(f"Recommendations: {risk_assessment['recommendations']}")
```

### Behavioral Analytics

Comprehensive tracking of human-like behavior patterns:

```python
# Get detailed behavioral statistics
stats = agent.get_stats()
behavioral = stats['behavioral_stats']

print(f"Accuracy: {behavioral['accuracy']:.1%}")
print(f"Error Count: {behavioral['error_count']}")
print(f"Avg Decision Time: {behavioral['avg_decision_time']:.2f}s")
print(f"Timing Variance: {behavioral['timing_variance']:.2f}s")

# Context distribution
context_dist = behavioral['context_distribution']
for context, percentage in context_dist.items():
    print(f"{context}: {percentage:.1%}")

# Export behavioral data for analysis
behavioral_data = agent.export_behavioral_data()
```

## 🧪 Testing

The project includes comprehensive test coverage:

```bash
# Run all tests
python -m pytest tests/ -v

# Run specific test modules
python tests/test_core/test_card_deck.py
python tests/test_core/test_hand.py
python tests/test_core/test_game_logic.py
python tests/test_core/test_basic_strategy.py
python tests/test_agents/test_basic_strategy_agent.py
python tests/test_core/test_simulation.py
python tests/test_core/test_progress_manager.py
```

### Test Coverage

- **79 total tests** with 100% pass rate
- **Core game logic**: Card handling, game rules, edge cases
- **Basic Strategy**: Chart accuracy, action validation
- **Agents**: Decision making, statistics tracking
- **Simulation**: Performance testing, result analysis
- **Progress**: Session management, data persistence

## 📈 Performance Analysis

### Basic Strategy Performance

Expected theoretical performance for perfect Basic Strategy:
- **Win Rate**: ~42-48% (varies with specific rules)
- **Return on Investment**: -0.5% to -1.0% (house edge)
- **Blackjack Rate**: ~4.7%
- **Bust Rate**: ~28%

### Simulation Metrics

The simulator tracks comprehensive metrics:
- Hand-by-hand results
- Action frequency analysis
- Decision timing
- Betting patterns
- Statistical significance

## 🔧 Configuration

### Game Configuration

```python
# Custom game rules
game = BlackjackGame(num_decks=8)  # 8-deck game

# Custom simulation
config = SimulationConfig(
    num_hands=10000,
    min_bet=5.0,
    max_bet=500.0,
    num_decks=6,
    verbose=True,
    random_seed=42  # For reproducible results
)
```

### Agent Configuration

```python
# Basic Strategy with custom bet
agent = BasicStrategyAgent("High Roller")
agent.set_bet_amount(25.0)

# Load custom Basic Strategy charts
agent = BasicStrategyAgent(
    name="Custom Bot",
    charts_file="data/basic_strategy_data/custom_rules.json"
)
```

## 📁 Data Management

### Basic Strategy Data

Charts are stored in JSON format:
```
data/basic_strategy_data/
└── six_deck_h17_das.json  # Complete BS charts
```

### Training Data

Progress and models are automatically saved:
```
data/
├── training_sessions/     # Session metadata
├── checkpoints/          # Model checkpoints
├── trained_models/       # Final models
└── logs/                # Application logs
```

### Simulation Results

Results can be exported for analysis:
```python
# Export simulation results
simulator.export_results("results.json", results)

# Export session summary
progress.export_session_summary(session_id, "session_report.json")
```

## 🐛 Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   # Ensure you're in the project root directory
   cd BlackJackBotML
   python -c "import core.game_logic"
   ```

2. **Test Failures**
   ```bash
   # Run tests individually to isolate issues
   python tests/test_core/test_game_logic.py
   ```

3. **Performance Issues**
   ```bash
   # Reduce simulation size for testing
   config = SimulationConfig(num_hands=100)
   ```

### Debug Mode

Enable verbose output for debugging:
```python
config = SimulationConfig(verbose=True)
simulator = BlackjackSimulator(config)
```

## 🤝 Contributing

### Code Style
- Follow PEP 8 guidelines
- Include comprehensive docstrings
- Add unit tests for new features
- Maintain type hints where possible

### Testing Requirements
- All new code must include tests
- Maintain 100% test pass rate
- Add integration tests for complex features

## 📚 Advanced Usage

### Custom Agents

Create your own playing agents:

```python
from agents.base_agent import BaseAgent
from core.game_logic import GameAction, GameState

class MyCustomAgent(BaseAgent):
    def get_action(self, game_state: GameState, hand_index: int = 0) -> GameAction:
        # Implement your strategy here
        return GameAction.HIT
    
    def get_bet_amount(self, min_bet: float = 1.0, max_bet: float = 100.0) -> float:
        # Implement your betting strategy
        return min_bet
```

### Batch Simulations

Run multiple simulations for statistical analysis:

```python
results_list = []
for seed in range(10):
    config = SimulationConfig(num_hands=1000, random_seed=seed)
    simulator = BlackjackSimulator(config)
    results = simulator.run_simulation(agent)
    results_list.append(results)

# Analyze variance across runs
win_rates = [r['agent_stats']['win_rate'] for r in results_list]
print(f"Mean win rate: {sum(win_rates)/len(win_rates):.3f}")
```

## 📄 License

This project is for educational and research purposes. Please ensure compliance with local laws and regulations regarding gambling and automated gaming systems.

## 🔮 Roadmap

### Phase 2: Human Simulation ✅ **COMPLETE**
- ✅ Three distinct personas with configurable accuracy levels
- ✅ Realistic decision timing with complexity factors
- ✅ Dynamic persona switching for detection avoidance
- ✅ Comprehensive behavioral analytics and pattern tracking

### Phase 3: Reinforcement Learning 🚧 **IN DEVELOPMENT**
- Deep Q-Network (DQN) implementation with persona integration
- Evasion reward functions and detection simulation
- Adaptive learning algorithms that maintain human-like characteristics
- Advanced training pipeline with curriculum learning

### Phase 4: Production Interface 🔮 **PLANNED**
- Command-line interface with persona selection
- Real-time monitoring and risk assessment
- Configuration management and deployment tools

---

## 📖 Detailed API Reference

### Core Classes

#### BlackjackGame
Main game engine that handles all blackjack logic.

```python
from core.game_logic import BlackjackGame, GameAction

game = BlackjackGame(num_decks=6)
state = game.start_new_game(bet_amount=10.0)
available_actions = game.get_available_actions()
new_state = game.take_action(GameAction.HIT)
```

**Key Methods:**
- `start_new_game(bet_amount)`: Initialize new hand
- `get_available_actions()`: Get valid actions for current situation
- `take_action(action)`: Execute player action
- `is_game_over()`: Check if hand is complete

#### BasicStrategyAgent
Perfect Basic Strategy player implementation.

```python
from agents.basic_strategy_agent import BasicStrategyAgent

agent = BasicStrategyAgent("Perfect Player")
agent.set_bet_amount(25.0)
action = agent.get_action(game_state)
stats = agent.get_stats()
```

**Key Methods:**
- `get_action(game_state, hand_index)`: Get optimal action
- `get_bet_amount(min_bet, max_bet)`: Get bet amount
- `get_stats()`: Get performance statistics
- `set_bet_amount(amount)`: Set flat bet amount

#### BlackjackSimulator
Comprehensive simulation framework for testing agents.

```python
from utils.simulation import BlackjackSimulator, SimulationConfig

config = SimulationConfig(num_hands=1000, verbose=True)
simulator = BlackjackSimulator(config)
results = simulator.run_simulation(agent)
```

**Key Methods:**
- `run_simulation(agent, progress_callback)`: Run complete simulation
- `export_results(filepath, results)`: Export results to JSON

#### ProgressManager
Session and checkpoint management system.

```python
from utils.progress_manager import ProgressManager

progress = ProgressManager()
session_id = progress.start_session("AgentType", parameters)
checkpoint_id = progress.save_checkpoint(hand_num, model_data, metrics)
```

**Key Methods:**
- `start_session(agent_type, parameters)`: Begin training session
- `save_checkpoint(hand_number, model_data, metrics)`: Save model state
- `list_sessions(agent_type)`: List all sessions
- `export_session_summary(session_id)`: Export session report

### Game State Objects

#### Hand
Represents a blackjack hand with proper Ace handling.

```python
from core.hand import Hand
from core.card import Card, Suit, Rank

hand = Hand()
hand.add_card(Card(Suit.HEARTS, Rank.ACE))
hand.add_card(Card(Suit.SPADES, Rank.KING))

print(f"Value: {hand.get_value()}")  # 21
print(f"Is blackjack: {hand.is_blackjack()}")  # True
print(f"Is soft: {hand.is_soft()}")  # True
```

**Key Properties:**
- `get_value()`: Optimal hand value
- `is_soft()`: Contains Ace counted as 11
- `is_blackjack()`: Natural 21 with 2 cards
- `is_bust()`: Value exceeds 21
- `can_split()`: Hand can be split

#### Card
Individual playing card representation.

```python
from core.card import Card, Suit, Rank

card = Card(Suit.HEARTS, Rank.ACE)
print(f"Card: {card}")  # A♥
print(f"Value: {card.get_value()}")  # 11 (default)
print(f"Is Ace: {card.is_ace}")  # True
```

## 🎯 Strategy Guide

### Basic Strategy Fundamentals

Basic Strategy is the mathematically optimal way to play blackjack based on:
- Your hand total
- Dealer's upcard
- Game rules (number of decks, dealer hits soft 17, etc.)

#### Key Principles

1. **Hard Hands (no Aces or Aces as 1)**
   - Hit on 8 or less
   - Stand on 17 or more
   - Hit 12-16 vs dealer 7-A
   - Stand 12-16 vs dealer 2-6

2. **Soft Hands (Ace as 11)**
   - More aggressive doubling
   - Hit soft 17 vs strong dealer cards
   - Stand on soft 19-20

3. **Pairs**
   - Always split Aces and 8s
   - Never split 5s or 10s
   - Split other pairs based on dealer upcard

#### Example Decisions

```python
# Hard 16 vs Dealer 10 → HIT
hand = Hand([Card(Suit.HEARTS, Rank.TEN), Card(Suit.SPADES, Rank.SIX)])
action = charts.get_action(hand, dealer_upcard=10)  # HIT

# Soft 18 vs Dealer 6 → DOUBLE
hand = Hand([Card(Suit.HEARTS, Rank.ACE), Card(Suit.SPADES, Rank.SEVEN)])
action = charts.get_action(hand, dealer_upcard=6)  # DOUBLE

# Pair of 8s vs Dealer A → SPLIT
hand = Hand([Card(Suit.HEARTS, Rank.EIGHT), Card(Suit.SPADES, Rank.EIGHT)])
action = charts.get_action(hand, dealer_upcard=11)  # SPLIT
```

### Performance Expectations

#### Theoretical Results (Perfect Basic Strategy)
- **House Edge**: 0.5-1.0% (depending on rules)
- **Win Rate**: 42-48% (pushes don't count as wins/losses)
- **Standard Deviation**: ~1.1 units per hand
- **Blackjack Frequency**: ~4.7%

#### Variance and Bankroll
- Short-term results can vary significantly
- Need adequate bankroll for variance (100+ betting units recommended)
- Long-term results converge to theoretical expectations

## 🔬 Research Applications

### Academic Use Cases

1. **Game Theory Research**
   - Optimal strategy analysis
   - Nash equilibrium studies
   - Multi-agent interactions

2. **Machine Learning Studies**
   - Reinforcement learning benchmarks
   - Strategy learning algorithms
   - Behavioral modeling

3. **Statistical Analysis**
   - Variance studies
   - Confidence interval analysis
   - Monte Carlo simulations

### Example Research Code

```python
# Study variance in Basic Strategy
import numpy as np

results = []
for trial in range(100):
    config = SimulationConfig(num_hands=1000, random_seed=trial)
    simulator = BlackjackSimulator(config)
    agent = BasicStrategyAgent(f"Trial_{trial}")

    result = simulator.run_simulation(agent)
    results.append(result['agent_stats']['return_on_investment'])

print(f"Mean ROI: {np.mean(results):.4f}")
print(f"Std Dev: {np.std(results):.4f}")
print(f"95% CI: [{np.percentile(results, 2.5):.4f}, {np.percentile(results, 97.5):.4f}]")
```

## 🛠️ Development Guide

### Project Structure Details

```
BlackJackBotML/
├── core/                    # Core game engine
│   ├── __init__.py         # Package initialization
│   ├── card.py             # Card and suit definitions
│   ├── deck.py             # Multi-deck with CSM simulation
│   ├── hand.py             # Hand logic with Ace handling
│   └── game_logic.py       # Complete blackjack implementation
├── agents/                  # AI agents
│   ├── __init__.py         # Agent package
│   ├── base_agent.py       # Abstract base class
│   └── basic_strategy_agent.py  # Perfect strategy player
├── personas/                # Human-like agents (Phase 2)
│   └── __init__.py         # Placeholder for Phase 2
├── rl/                      # Reinforcement learning (Phase 3)
│   └── __init__.py         # Placeholder for Phase 3
├── utils/                   # Utilities and tools
│   ├── __init__.py         # Utils package
│   ├── basic_strategy_charts.py  # Strategy lookup tables
│   ├── simulation.py       # Testing framework
│   └── progress_manager.py # Session management
├── configs/                 # Configuration files (Phase 4)
├── data/                    # Generated and saved data
│   ├── basic_strategy_data/ # Strategy charts (JSON)
│   ├── trained_models/     # Saved AI models
│   ├── logs/               # Application logs
│   └── simulation_results/ # Test outputs
├── notebooks/               # Jupyter analysis notebooks
├── tests/                   # Comprehensive test suite
│   ├── test_core/          # Core functionality tests
│   └── test_agents/        # Agent behavior tests
└── README.md               # This file
```

### Adding New Features

1. **Create the module** in appropriate directory
2. **Add comprehensive tests** in tests/ directory
3. **Update __init__.py** files for imports
4. **Add documentation** and examples
5. **Run full test suite** to ensure compatibility

### Code Quality Standards

- **Type hints** for all function parameters and returns
- **Docstrings** for all classes and public methods
- **Unit tests** with >95% coverage
- **Integration tests** for complex workflows
- **Performance tests** for simulation components

## 🔍 Debugging and Monitoring

### Logging

Enable detailed logging for debugging:

```python
import logging

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data/logs/debug.log'),
        logging.StreamHandler()
    ]
)

# Run simulation with logging
simulator = BlackjackSimulator(config)
results = simulator.run_simulation(agent)
```

### Performance Monitoring

Track simulation performance:

```python
import time
import psutil

# Monitor system resources
process = psutil.Process()
start_memory = process.memory_info().rss / 1024 / 1024  # MB

start_time = time.time()
results = simulator.run_simulation(agent)
end_time = time.time()

end_memory = process.memory_info().rss / 1024 / 1024  # MB

print(f"Execution time: {end_time - start_time:.2f} seconds")
print(f"Memory usage: {end_memory - start_memory:.2f} MB")
print(f"Hands per second: {results['hands_per_second']:.1f}")
```

### Common Debug Scenarios

1. **Unexpected Win Rates**
   ```python
   # Check Basic Strategy implementation
   charts = BasicStrategyCharts()

   # Verify specific decisions
   test_cases = [
       (Hand([Card(Suit.HEARTS, Rank.TEN), Card(Suit.SPADES, Rank.SIX)]), 10, "HIT"),
       (Hand([Card(Suit.HEARTS, Rank.ACE), Card(Suit.SPADES, Rank.SEVEN)]), 6, "DOUBLE"),
   ]

   for hand, dealer_up, expected in test_cases:
       action = charts.get_action(hand, dealer_up)
       print(f"Hand: {hand}, Dealer: {dealer_up}, Action: {action}, Expected: {expected}")
   ```

2. **Performance Issues**
   ```python
   # Profile simulation performance
   import cProfile

   profiler = cProfile.Profile()
   profiler.enable()

   results = simulator.run_simulation(agent)

   profiler.disable()
   profiler.print_stats(sort='cumulative')
   ```

## 📊 Data Analysis Examples

### Statistical Analysis

```python
import json
import matplotlib.pyplot as plt
import numpy as np

# Load simulation results
with open('data/simulation_results/results.json', 'r') as f:
    results = json.load(f)

# Analyze hand results
hand_results = results['hand_results']
winnings = [hr['winnings'] for hr in hand_results]

# Plot cumulative winnings
cumulative = np.cumsum(winnings)
plt.figure(figsize=(12, 6))
plt.plot(cumulative)
plt.title('Cumulative Winnings Over Time')
plt.xlabel('Hand Number')
plt.ylabel('Cumulative Winnings')
plt.grid(True)
plt.show()

# Calculate statistics
print(f"Total hands: {len(winnings)}")
print(f"Win rate: {sum(1 for w in winnings if w > 0) / len(winnings):.3f}")
print(f"Average per hand: {np.mean(winnings):.4f}")
print(f"Standard deviation: {np.std(winnings):.4f}")
```

### Strategy Validation

```python
# Validate Basic Strategy decisions
from collections import defaultdict

action_counts = defaultdict(int)
correct_decisions = 0
total_decisions = 0

for hand_result in hand_results:
    for actions in hand_result['actions_taken']:
        for action in actions:
            action_counts[action] += 1
            total_decisions += 1

print("Action Distribution:")
for action, count in action_counts.items():
    percentage = count / total_decisions * 100
    print(f"  {action}: {count} ({percentage:.1f}%)")
```

**Built with ❤️ for educational purposes**

---

*This project is designed for learning and research. Always comply with local laws and regulations regarding gambling and automated systems.*
