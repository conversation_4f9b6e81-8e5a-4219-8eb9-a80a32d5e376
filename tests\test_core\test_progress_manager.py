"""
Unit tests for Progress Manager.

Tests P1_T7 implementation: Local progress saving mechanism.
"""

import unittest
import sys
import os
import tempfile
import shutil

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from utils.progress_manager import ProgressManager, TrainingSession, ModelCheckpoint
from agents.basic_strategy_agent import BasicStrategyAgent


class TestProgressManager(unittest.TestCase):
    """Test cases for the ProgressManager class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create temporary directory for testing
        self.test_dir = tempfile.mkdtemp()
        self.progress_manager = ProgressManager(base_dir=self.test_dir)
        
    def tearDown(self):
        """Clean up test fixtures."""
        # Remove temporary directory
        shutil.rmtree(self.test_dir)
    
    def test_initialization(self):
        """Test progress manager initialization."""
        # Check that directories were created
        self.assertTrue(os.path.exists(self.progress_manager.sessions_dir))
        self.assertTrue(os.path.exists(self.progress_manager.checkpoints_dir))
        self.assertTrue(os.path.exists(self.progress_manager.models_dir))
        self.assertTrue(os.path.exists(self.progress_manager.logs_dir))
        
        # Check initial state
        self.assertIsNone(self.progress_manager.current_session)
    
    def test_session_lifecycle(self):
        """Test complete session lifecycle."""
        # Start session
        parameters = {"learning_rate": 0.001, "batch_size": 32}
        session_id = self.progress_manager.start_session("BasicStrategy", parameters)
        
        self.assertIsNotNone(session_id)
        self.assertIsNotNone(self.progress_manager.current_session)
        self.assertEqual(self.progress_manager.current_session.agent_type, "BasicStrategy")
        self.assertEqual(self.progress_manager.current_session.parameters, parameters)
        self.assertEqual(self.progress_manager.current_session.status, "running")
        
        # Update session
        metrics = {"win_rate": 0.45, "loss_rate": 0.55}
        self.progress_manager.update_session(current_hand=100, metrics=metrics)
        
        self.assertEqual(self.progress_manager.current_session.current_hand, 100)
        self.assertEqual(self.progress_manager.current_session.metrics, metrics)
        
        # End session
        final_metrics = {"final_win_rate": 0.47}
        self.progress_manager.end_session(total_hands=1000, final_metrics=final_metrics)
        
        self.assertIsNone(self.progress_manager.current_session)
    
    def test_checkpoint_management(self):
        """Test checkpoint saving and loading."""
        # Start a session first
        session_id = self.progress_manager.start_session("TestAgent")
        
        # Create test model data
        test_model = {"weights": [1, 2, 3], "bias": [0.1, 0.2]}
        metrics = {"accuracy": 0.85, "loss": 0.15}
        
        # Save checkpoint
        checkpoint_id = self.progress_manager.save_checkpoint(
            hand_number=500,
            model_data=test_model,
            metrics=metrics,
            notes="Test checkpoint"
        )
        
        self.assertIsNotNone(checkpoint_id)
        
        # Load checkpoint
        loaded_checkpoint, loaded_model = self.progress_manager.load_checkpoint(checkpoint_id)
        
        self.assertEqual(loaded_checkpoint.hand_number, 500)
        self.assertEqual(loaded_checkpoint.metrics, metrics)
        self.assertEqual(loaded_checkpoint.notes, "Test checkpoint")
        self.assertEqual(loaded_model, test_model)
    
    def test_agent_state_persistence(self):
        """Test saving and loading agent states."""
        # Create test agent
        agent = BasicStrategyAgent("Test Agent")
        agent.set_bet_amount(5.0)
        
        # Update some stats
        agent.update_stats('win', 10.0, 10.0)
        agent.update_stats('loss', 10.0, -10.0)
        
        original_stats = agent.get_stats()
        
        # Save agent
        saved_path = self.progress_manager.save_agent_state(agent)
        self.assertTrue(os.path.exists(saved_path))
        
        # Load agent
        loaded_agent = self.progress_manager.load_agent_state(saved_path)
        
        self.assertEqual(loaded_agent.name, agent.name)
        self.assertEqual(loaded_agent.bet_amount, agent.bet_amount)
        self.assertEqual(loaded_agent.get_stats(), original_stats)
    
    def test_session_listing(self):
        """Test listing sessions."""
        # Initially no sessions
        sessions = self.progress_manager.list_sessions()
        self.assertEqual(len(sessions), 0)
        
        # Create multiple sessions with slight delays to ensure different timestamps
        import time

        session1_id = self.progress_manager.start_session("BasicStrategy")
        self.progress_manager.end_session(total_hands=100)

        time.sleep(1)  # Ensure different timestamp
        session2_id = self.progress_manager.start_session("RLAgent")
        self.progress_manager.end_session(total_hands=200)

        time.sleep(1)  # Ensure different timestamp
        session3_id = self.progress_manager.start_session("BasicStrategy2")  # Different name
        self.progress_manager.end_session(total_hands=300)
        
        # List all sessions
        all_sessions = self.progress_manager.list_sessions()
        self.assertEqual(len(all_sessions), 3)
        
        # List sessions by type
        bs_sessions = self.progress_manager.list_sessions("BasicStrategy")
        self.assertEqual(len(bs_sessions), 1)

        rl_sessions = self.progress_manager.list_sessions("RLAgent")
        self.assertEqual(len(rl_sessions), 1)

        bs2_sessions = self.progress_manager.list_sessions("BasicStrategy2")
        self.assertEqual(len(bs2_sessions), 1)
    
    def test_checkpoint_listing(self):
        """Test listing checkpoints."""
        # Start session and create checkpoints
        session_id = self.progress_manager.start_session("TestAgent")
        
        test_model = {"data": "test"}
        metrics = {"score": 0.5}
        
        # Create multiple checkpoints
        cp1 = self.progress_manager.save_checkpoint(100, test_model, metrics)
        cp2 = self.progress_manager.save_checkpoint(200, test_model, metrics)
        cp3 = self.progress_manager.save_checkpoint(300, test_model, metrics)
        
        # List all checkpoints
        all_checkpoints = self.progress_manager.list_checkpoints()
        self.assertEqual(len(all_checkpoints), 3)
        
        # List checkpoints for specific session
        session_checkpoints = self.progress_manager.list_checkpoints(session_id)
        self.assertEqual(len(session_checkpoints), 3)
        
        # Check ordering (should be by hand number, descending)
        self.assertEqual(session_checkpoints[0].hand_number, 300)
        self.assertEqual(session_checkpoints[1].hand_number, 200)
        self.assertEqual(session_checkpoints[2].hand_number, 100)
    
    def test_session_export(self):
        """Test session summary export."""
        # Create session with checkpoints
        session_id = self.progress_manager.start_session("TestAgent", {"param": "value"})
        
        # Add some checkpoints
        test_model = {"weights": [1, 2, 3]}
        self.progress_manager.save_checkpoint(100, test_model, {"metric": 0.5})
        self.progress_manager.save_checkpoint(200, test_model, {"metric": 0.6})
        
        self.progress_manager.end_session(total_hands=1000, final_metrics={"final": 0.7})
        
        # Export session
        export_path = self.progress_manager.export_session_summary(session_id)
        
        self.assertTrue(os.path.exists(export_path))
        
        # Verify export content
        import json
        with open(export_path, 'r') as f:
            summary = json.load(f)
        
        self.assertIn('session', summary)
        self.assertIn('checkpoints', summary)
        self.assertIn('export_time', summary)
        
        self.assertEqual(summary['session']['session_id'], session_id)
        self.assertEqual(len(summary['checkpoints']), 2)
    
    def test_checkpoint_cleanup(self):
        """Test checkpoint cleanup functionality."""
        # Create session with many checkpoints
        session_id = self.progress_manager.start_session("TestAgent")
        
        test_model = {"data": "test"}
        metrics = {"score": 0.5}
        
        # Create 15 checkpoints
        for i in range(15):
            self.progress_manager.save_checkpoint((i + 1) * 100, test_model, metrics)
        
        # Verify all checkpoints exist
        checkpoints = self.progress_manager.list_checkpoints(session_id)
        self.assertEqual(len(checkpoints), 15)
        
        # Clean up, keeping only 5
        removed_count = self.progress_manager.cleanup_old_checkpoints(keep_count=5)
        
        self.assertEqual(removed_count, 10)
        
        # Verify only 5 remain
        remaining_checkpoints = self.progress_manager.list_checkpoints(session_id)
        self.assertEqual(len(remaining_checkpoints), 5)
        
        # Verify the most recent ones were kept
        hand_numbers = [cp.hand_number for cp in remaining_checkpoints]
        expected_numbers = [1500, 1400, 1300, 1200, 1100]  # Most recent 5
        self.assertEqual(sorted(hand_numbers, reverse=True), expected_numbers)
    
    def test_error_handling(self):
        """Test error handling for invalid operations."""
        # Test updating session without starting one
        with self.assertRaises(ValueError):
            self.progress_manager.update_session(current_hand=100)
        
        # Test ending session without starting one
        with self.assertRaises(ValueError):
            self.progress_manager.end_session()
        
        # Test saving checkpoint without session
        with self.assertRaises(ValueError):
            self.progress_manager.save_checkpoint(100, {}, {})
        
        # Test loading non-existent checkpoint
        with self.assertRaises(FileNotFoundError):
            self.progress_manager.load_checkpoint("non_existent")
        
        # Test exporting non-existent session
        with self.assertRaises(FileNotFoundError):
            self.progress_manager.export_session_summary("non_existent")
    
    def test_session_persistence(self):
        """Test that sessions persist across manager instances."""
        # Create session with first manager
        session_id = self.progress_manager.start_session("TestAgent")
        self.progress_manager.update_session(current_hand=500, metrics={"test": 0.5})
        
        # Create new manager instance with same directory
        new_manager = ProgressManager(base_dir=self.test_dir)
        
        # Should be able to list the session
        sessions = new_manager.list_sessions()
        self.assertEqual(len(sessions), 1)
        self.assertEqual(sessions[0].session_id, session_id)
        self.assertEqual(sessions[0].current_hand, 500)


if __name__ == '__main__':
    unittest.main()
