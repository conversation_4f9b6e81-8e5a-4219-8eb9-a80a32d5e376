"""
Unit tests for Evasion Strategies.

Tests P3_T2 implementation: Evasion Strategy Integration.
"""

import unittest
import sys
import os
import random

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from rl.evasion_strategies import (
    EvasionConfig, EvasionTechnique, BaseEvasionStrategy,
    PersonaSwitchingStrategy, BehavioralNoiseStrategy, TimingVariationStrategy,
    DecisionMaskingStrategy, PatternDisruptionStrategy, AdaptiveConsistencyStrategy
)
from rl.evasion_manager import EvasionManager, EvasionMetrics
from rl.evasive_dqn_agent import EvasiveDQNAgent
from rl.dqn_agent import DQNConfig
from rl.base_rl_agent import RLConfig
from personas.cautious_persona import Cautious<PERSON>ersona
from personas.persona_switcher import PersonaSwitcher, SwitchConfig
from core.card import Card, Suit, Rank
from core.hand import Hand
from core.game_logic import GameState, GameAction


class MockRLAgent:
    """Mock RL agent for testing evasion strategies."""
    
    def __init__(self):
        self.training_step = 100
        self.consistency_scores = [0.8, 0.85, 0.9, 0.95]
        self.decision_history = []
        self.persona = None
        
        # Add some mock decision history
        for i in range(50):
            self.decision_history.append({
                'episode': 1,
                'step': i,
                'rl_action': 'H' if i % 2 == 0 else 'S',
                'final_action': 'H' if i % 2 == 0 else 'S',
                'is_persona_influenced': False,
                'timestamp': i
            })


class TestEvasionConfig(unittest.TestCase):
    """Test cases for EvasionConfig."""
    
    def test_config_creation(self):
        """Test evasion config creation with defaults."""
        config = EvasionConfig()
        
        # Check default values
        self.assertEqual(config.consistency_threshold, 0.95)
        self.assertEqual(config.pattern_detection_window, 100)
        self.assertIn(EvasionTechnique.PERSONA_SWITCHING, config.technique_weights)
        self.assertEqual(config.noise_intensity, 0.1)
        self.assertEqual(config.target_consistency_range, (0.7, 0.85))
    
    def test_config_customization(self):
        """Test custom evasion config."""
        custom_weights = {
            EvasionTechnique.BEHAVIORAL_NOISE: 0.5,
            EvasionTechnique.TIMING_VARIATION: 0.3,
            EvasionTechnique.PATTERN_DISRUPTION: 0.2
        }
        
        config = EvasionConfig(
            consistency_threshold=0.9,
            noise_intensity=0.2,
            technique_weights=custom_weights
        )
        
        self.assertEqual(config.consistency_threshold, 0.9)
        self.assertEqual(config.noise_intensity, 0.2)
        self.assertEqual(config.technique_weights, custom_weights)


class TestBehavioralNoiseStrategy(unittest.TestCase):
    """Test cases for BehavioralNoiseStrategy."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = EvasionConfig(noise_intensity=0.3)
        self.strategy = BehavioralNoiseStrategy(self.config)
        self.agent = MockRLAgent()
        
    def test_strategy_initialization(self):
        """Test strategy initialization."""
        self.assertEqual(self.strategy.config, self.config)
        self.assertEqual(self.strategy.current_noise_level, 0.3)
        self.assertEqual(self.strategy.activation_count, 0)
    
    def test_should_activate(self):
        """Test activation conditions."""
        # High detection risk should increase activation probability
        high_risk_activations = sum(
            1 for _ in range(100) 
            if self.strategy.should_activate(self.agent, 0.9)
        )
        
        # Low detection risk should decrease activation probability
        low_risk_activations = sum(
            1 for _ in range(100) 
            if self.strategy.should_activate(self.agent, 0.1)
        )
        
        # High risk should activate more often
        self.assertGreater(high_risk_activations, low_risk_activations)
    
    def test_apply_evasion(self):
        """Test evasion application."""
        # Create test game state
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        game_state.player_hands = [hand]
        
        original_action = GameAction.HIT
        
        # Apply evasion multiple times
        results = []
        for _ in range(20):
            action, metadata = self.strategy.apply_evasion(self.agent, game_state, original_action)
            results.append((action, metadata))
        
        # Check that some actions were modified
        modified_count = sum(1 for action, meta in results if meta["was_modified"])
        self.assertGreater(modified_count, 0)
        
        # Check metadata structure
        _, metadata = results[0]
        self.assertIn("technique", metadata)
        self.assertIn("noise_level", metadata)
        self.assertIn("rl_action", metadata)
        self.assertIn("final_action", metadata)
        self.assertEqual(metadata["technique"], EvasionTechnique.BEHAVIORAL_NOISE)
    
    def test_noise_decay(self):
        """Test noise level decay."""
        initial_noise = self.strategy.current_noise_level
        
        # Apply evasion multiple times
        game_state = GameState()
        for _ in range(10):
            self.strategy.apply_evasion(self.agent, game_state, GameAction.HIT)
        
        # Noise level should have decayed
        self.assertLess(self.strategy.current_noise_level, initial_noise)
        self.assertGreaterEqual(self.strategy.current_noise_level, self.config.min_noise)


class TestTimingVariationStrategy(unittest.TestCase):
    """Test cases for TimingVariationStrategy."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = EvasionConfig(timing_noise_factor=0.3)
        self.strategy = TimingVariationStrategy(self.config)
        self.agent = MockRLAgent()
    
    def test_always_activates(self):
        """Test that timing variation always activates."""
        # Should always activate regardless of risk
        self.assertTrue(self.strategy.should_activate(self.agent, 0.0))
        self.assertTrue(self.strategy.should_activate(self.agent, 0.5))
        self.assertTrue(self.strategy.should_activate(self.agent, 1.0))
    
    def test_timing_variation(self):
        """Test timing variation generation."""
        game_state = GameState()
        
        # Apply timing variation multiple times
        timings = []
        for _ in range(50):
            action, metadata = self.strategy.apply_evasion(self.agent, game_state, GameAction.HIT)
            timings.append(metadata["final_timing"])
        
        # Check timing variation
        self.assertGreater(len(set(timings)), 10)  # Should have variety
        self.assertTrue(all(t > 0 for t in timings))  # All positive
        
        # Check bounds
        min_expected = 1.0 * self.config.min_timing_multiplier
        max_expected = 1.0 * self.config.max_timing_multiplier
        self.assertTrue(all(min_expected <= t <= max_expected for t in timings))
    
    def test_timing_history_tracking(self):
        """Test timing history tracking."""
        game_state = GameState()
        
        # Apply timing variation
        for _ in range(10):
            self.strategy.apply_evasion(self.agent, game_state, GameAction.HIT)
        
        # Should have timing history
        self.assertEqual(len(self.strategy.timing_history), 10)
        
        # Test history limit
        for _ in range(100):
            self.strategy.apply_evasion(self.agent, game_state, GameAction.HIT)
        
        self.assertLessEqual(len(self.strategy.timing_history), 100)


class TestPersonaSwitchingStrategy(unittest.TestCase):
    """Test cases for PersonaSwitchingStrategy."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = EvasionConfig()
        
        # Create persona switcher
        switch_config = SwitchConfig(min_hands_per_persona=5, max_hands_per_persona=10)
        self.persona_switcher = PersonaSwitcher(switch_config)
        
        self.strategy = PersonaSwitchingStrategy(self.config, self.persona_switcher)
        self.agent = MockRLAgent()
    
    def test_strategy_initialization(self):
        """Test strategy initialization."""
        self.assertEqual(self.strategy.persona_switcher, self.persona_switcher)
        self.assertEqual(self.strategy.switch_cooldown, 50)
    
    def test_activation_conditions(self):
        """Test activation conditions."""
        # Should not activate with low risk
        self.assertFalse(self.strategy.should_activate(self.agent, 0.5))
        
        # Should not activate with high risk but recent switch
        self.agent.training_step = 10
        self.strategy.last_activation = 5
        self.assertFalse(self.strategy.should_activate(self.agent, 0.9))
        
        # Should activate with high risk and cooldown passed
        self.agent.training_step = 100
        self.strategy.last_activation = 0
        self.assertTrue(self.strategy.should_activate(self.agent, 0.9))
    
    def test_persona_switching(self):
        """Test persona switching functionality."""
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        old_persona = self.persona_switcher.get_current_persona_name()
        
        # Apply persona switching
        action, metadata = self.strategy.apply_evasion(self.agent, game_state, GameAction.HIT)
        
        new_persona = self.persona_switcher.get_current_persona_name()
        
        # Check that switch occurred
        self.assertNotEqual(old_persona, new_persona)
        
        # Check metadata
        self.assertEqual(metadata["technique"], EvasionTechnique.PERSONA_SWITCHING)
        self.assertEqual(metadata["old_persona"], old_persona)
        self.assertEqual(metadata["new_persona"], new_persona)
        self.assertIn("rl_action", metadata)
        self.assertIn("final_action", metadata)


class TestEvasionManager(unittest.TestCase):
    """Test cases for EvasionManager."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = EvasionConfig()
        self.manager = EvasionManager(self.config)
        self.agent = MockRLAgent()
    
    def test_manager_initialization(self):
        """Test manager initialization."""
        self.assertEqual(self.manager.config, self.config)
        self.assertIsInstance(self.manager.metrics, EvasionMetrics)
        
        # Should have initialized strategies
        expected_strategies = [
            EvasionTechnique.BEHAVIORAL_NOISE,
            EvasionTechnique.TIMING_VARIATION,
            EvasionTechnique.DECISION_MASKING,
            EvasionTechnique.PATTERN_DISRUPTION,
            EvasionTechnique.ADAPTIVE_CONSISTENCY
        ]
        
        for technique in expected_strategies:
            self.assertIn(technique, self.manager.strategies)
    
    def test_evasion_application(self):
        """Test evasion application."""
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        game_state.player_hands = [hand]
        
        original_action = GameAction.HIT
        
        # Apply evasion
        final_action, metadata = self.manager.apply_evasion(self.agent, game_state, original_action)
        
        # Check return types
        self.assertIsInstance(final_action, GameAction)
        self.assertIsInstance(metadata, dict)
        
        # Check metadata structure
        self.assertIn("detection_risk", metadata)
        self.assertIn("original_action", metadata)
        self.assertIn("final_action", metadata)
        self.assertIn("applied_techniques", metadata)
        self.assertIn("was_modified", metadata)
    
    def test_detection_risk_assessment(self):
        """Test detection risk assessment."""
        # Test with high consistency agent
        self.agent.consistency_scores = [0.95, 0.96, 0.97, 0.98]
        risk = self.manager._assess_detection_risk(self.agent)
        self.assertGreater(risk, 0.5)
        
        # Test with low consistency agent
        self.agent.consistency_scores = [0.5, 0.6, 0.7, 0.8]
        risk = self.manager._assess_detection_risk(self.agent)
        self.assertLess(risk, 0.8)
    
    def test_metrics_tracking(self):
        """Test metrics tracking."""
        game_state = GameState()
        
        # Apply evasion multiple times
        for _ in range(10):
            self.manager.apply_evasion(self.agent, game_state, GameAction.HIT)
        
        # Check metrics
        metrics = self.manager.get_evasion_metrics()
        self.assertGreater(metrics.total_evasions, 0)
        self.assertGreater(len(metrics.detection_risk_history), 0)
    
    def test_strategy_stats(self):
        """Test strategy statistics."""
        stats = self.manager.get_strategy_stats()
        
        # Should have stats for all strategies
        for technique in self.manager.strategies:
            self.assertIn(technique.value, stats)
            
            strategy_stats = stats[technique.value]
            self.assertIn("activation_count", strategy_stats)
            self.assertIn("strategy_type", strategy_stats)


class TestEvasiveDQNAgent(unittest.TestCase):
    """Test cases for EvasiveDQNAgent."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.dqn_config = DQNConfig(
            hidden_layers=[16, 8],
            batch_size=4,
            min_buffer_size=5
        )
        self.evasion_config = EvasionConfig()
        
        self.agent = EvasiveDQNAgent(
            "Test Evasive Agent",
            self.dqn_config,
            self.evasion_config
        )
    
    def test_agent_initialization(self):
        """Test evasive agent initialization."""
        self.assertEqual(self.agent.name, "Test Evasive Agent")
        self.assertIsNotNone(self.agent.evasion_manager)
        self.assertEqual(self.agent.evasion_config, self.evasion_config)
        self.assertEqual(len(self.agent.evasion_history), 0)
    
    def test_action_with_evasion(self):
        """Test action selection with evasion."""
        # Create test game state
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Get action
        action = self.agent.get_action(game_state)
        
        # Should return valid action
        self.assertIsInstance(action, GameAction)
        
        # Should have recorded evasion event
        self.assertGreater(len(self.agent.evasion_history), 0)
        
        # Check evasion record structure
        evasion_record = self.agent.evasion_history[0]
        self.assertIn("training_step", evasion_record)
        self.assertIn("rl_action", evasion_record)
        self.assertIn("final_action", evasion_record)
        self.assertIn("evasion_metadata", evasion_record)
    
    def test_reward_adjustment(self):
        """Test reward adjustment for evasion."""
        base_reward = 10.0
        
        # Test with low detection risk
        self.agent.evasion_manager.current_risk_level = 0.3
        adjusted = self.agent._adjust_reward_for_evasion(base_reward)
        self.assertGreaterEqual(adjusted, base_reward)  # Should not be penalized
        
        # Test with high detection risk
        self.agent.evasion_manager.current_risk_level = 0.9
        adjusted = self.agent._adjust_reward_for_evasion(base_reward)
        self.assertLess(adjusted, base_reward)  # Should be penalized
    
    def test_comprehensive_stats(self):
        """Test comprehensive statistics."""
        # Make some decisions to generate data
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        for _ in range(5):
            self.agent.get_action(game_state)
        
        # Get comprehensive stats
        stats = self.agent.get_comprehensive_stats()
        
        # Check structure
        self.assertIn("evasion_metrics", stats)
        self.assertIn("strategy_stats", stats)
        self.assertIn("detection_assessment", stats)
        self.assertIn("performance_metrics", stats)
        
        # Check evasion metrics
        evasion_metrics = stats["evasion_metrics"]
        self.assertIn("total_evasions", evasion_metrics)
        self.assertIn("technique_usage", evasion_metrics)
        self.assertIn("evasion_effectiveness", evasion_metrics)
    
    def test_persona_integration(self):
        """Test persona integration with evasive agent."""
        # Create agent with persona
        persona = CautiousPersona()
        agent_with_persona = EvasiveDQNAgent(
            "Persona Evasive Agent",
            self.dqn_config,
            self.evasion_config,
            persona=persona
        )
        
        self.assertEqual(agent_with_persona.persona, persona)
        
        # Test action with persona
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        action = agent_with_persona.get_action(game_state)
        self.assertIsInstance(action, GameAction)


if __name__ == '__main__':
    unittest.main()
