"""
Unit tests for DQN Agent.

Tests P3_T1 implementation: DQN Agent with persona integration.
"""

import unittest
import sys
import os
import tempfile
import shutil
import numpy as np
import torch

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from rl.dqn_agent import DQNAgent, DQNConfig, DQNNetwork
from rl.base_rl_agent import Experience
from core.card import Card, Suit, Rank
from core.hand import Hand
from core.game_logic import GameState, GameAction
from personas.cautious_persona import CautiousPersona


class TestDQNNetwork(unittest.TestCase):
    """Test cases for DQN Network."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = DQNConfig(
            hidden_layers=[64, 32],
            dropout_rate=0.1,
            dueling_dqn=True
        )
        
        self.network = DQNNetwork(
            input_size=18,
            output_size=4,
            config=self.config
        )
    
    def test_network_initialization(self):
        """Test network initialization."""
        self.assertEqual(self.network.input_size, 18)
        self.assertEqual(self.network.output_size, 4)
        self.assertEqual(self.network.config, self.config)
        
        # Check that network has expected layers
        self.assertIsNotNone(self.network.feature_layers)
        
        if self.config.dueling_dqn:
            self.assertIsNotNone(self.network.value_stream)
            self.assertIsNotNone(self.network.advantage_stream)
        else:
            self.assertIsNotNone(self.network.output_layer)
    
    def test_network_forward_pass(self):
        """Test network forward pass."""
        # Create test input
        batch_size = 2
        input_tensor = torch.randn(batch_size, 18)
        
        # Forward pass
        output = self.network(input_tensor)
        
        # Check output shape
        self.assertEqual(output.shape, (batch_size, 4))
        self.assertIsInstance(output, torch.Tensor)
    
    def test_dueling_vs_standard_dqn(self):
        """Test difference between dueling and standard DQN."""
        # Standard DQN
        standard_config = DQNConfig(dueling_dqn=False, hidden_layers=[32])
        standard_network = DQNNetwork(18, 4, standard_config)
        
        # Dueling DQN
        dueling_config = DQNConfig(dueling_dqn=True, hidden_layers=[32])
        dueling_network = DQNNetwork(18, 4, dueling_config)
        
        # Test input
        input_tensor = torch.randn(1, 18)
        
        # Both should produce valid outputs
        standard_output = standard_network(input_tensor)
        dueling_output = dueling_network(input_tensor)
        
        self.assertEqual(standard_output.shape, (1, 4))
        self.assertEqual(dueling_output.shape, (1, 4))
    
    def test_activation_functions(self):
        """Test different activation functions."""
        activations = ["relu", "tanh", "leaky_relu"]
        
        for activation in activations:
            config = DQNConfig(activation=activation, hidden_layers=[16])
            network = DQNNetwork(18, 4, config)
            
            # Should create network without error
            self.assertIsNotNone(network)
            
            # Test forward pass
            input_tensor = torch.randn(1, 18)
            output = network(input_tensor)
            self.assertEqual(output.shape, (1, 4))


class TestDQNAgent(unittest.TestCase):
    """Test cases for DQN Agent."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = DQNConfig(
            learning_rate=0.001,
            hidden_layers=[32, 16],
            batch_size=4,
            min_buffer_size=10,
            target_update_frequency=5,
            epsilon_start=1.0,
            epsilon_end=0.1,
            epsilon_decay=0.9
        )
        
        self.agent = DQNAgent("Test DQN Agent", self.config)
        self.persona_agent = DQNAgent("Persona DQN Agent", self.config, CautiousPersona())
        
        # Create temporary directory for model saving
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_agent_initialization(self):
        """Test DQN agent initialization."""
        self.assertEqual(self.agent.name, "Test DQN Agent")
        self.assertEqual(self.agent.state_size, 18)
        self.assertEqual(self.agent.action_size, 4)
        
        # Check networks
        self.assertIsNotNone(self.agent.q_network)
        self.assertIsNotNone(self.agent.target_network)
        self.assertIsNotNone(self.agent.optimizer)
        
        # Check device
        self.assertIn(str(self.agent.device), ["cpu", "cuda:0"])
    
    def test_state_representation(self):
        """Test state representation generation."""
        # Create test game state
        game_state = GameState()
        
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.bet_amount = 10.0
        game_state.can_double = [True]
        game_state.can_split = [False]
        
        # Get state representation
        state = self.agent._get_state_representation(game_state)
        
        # Should be numpy array of correct size
        self.assertIsInstance(state, np.ndarray)
        self.assertEqual(len(state), 18)
        
        # Check some values
        self.assertAlmostEqual(state[0], 16.0 / 21.0, places=3)  # Player value
        self.assertEqual(state[1], 0.0)  # Not soft
        self.assertEqual(state[3], 1.0)  # Can double
        self.assertEqual(state[4], 0.0)  # Cannot split
    
    def test_action_selection(self):
        """Test action selection."""
        # Create test state
        state = np.random.rand(18)
        available_actions = [GameAction.HIT, GameAction.STAND]
        
        # Test with high epsilon (should be random)
        self.agent.epsilon = 1.0
        action = self.agent._select_action(state, available_actions, training=True)
        self.assertIn(action, available_actions)
        
        # Test with low epsilon (should be greedy)
        self.agent.epsilon = 0.0
        action = self.agent._select_action(state, available_actions, training=True)
        self.assertIn(action, available_actions)
        
        # Test non-training mode
        action = self.agent._select_action(state, available_actions, training=False)
        self.assertIn(action, available_actions)
    
    def test_q_values(self):
        """Test Q-value computation."""
        state = np.random.rand(18)
        q_values = self.agent.get_q_values(state)
        
        # Should return array of Q-values
        self.assertIsInstance(q_values, np.ndarray)
        self.assertEqual(len(q_values), 4)
        
        # All values should be finite
        self.assertTrue(np.all(np.isfinite(q_values)))
    
    def test_model_update(self):
        """Test model update with experience batch."""
        # Create mock experiences
        experiences = []
        for i in range(5):
            exp = Experience(
                state=np.random.rand(18),
                action=i % 4,
                reward=float(i),
                next_state=np.random.rand(18),
                done=i == 4
            )
            experiences.append(exp)
        
        # Update model
        loss = self.agent._update_model(experiences)
        
        # Should return finite loss
        self.assertIsInstance(loss, float)
        self.assertTrue(np.isfinite(loss))
        self.assertGreaterEqual(loss, 0.0)
    
    def test_target_network_update(self):
        """Test target network update."""
        # Get initial target network weights
        initial_target_params = list(self.agent.target_network.parameters())[0].clone()
        
        # Update main network (simulate training)
        for param in self.agent.q_network.parameters():
            param.data += 0.1
        
        # Force target network update
        self.agent.update_count = self.config.target_update_frequency
        
        # Create mock batch and update
        experiences = [
            Experience(
                state=np.random.rand(18),
                action=0,
                reward=1.0,
                next_state=np.random.rand(18),
                done=False
            )
        ]
        
        self.agent._update_model(experiences)
        
        # Target network should be updated
        updated_target_params = list(self.agent.target_network.parameters())[0]
        self.assertFalse(torch.equal(initial_target_params, updated_target_params))
    
    def test_epsilon_decay(self):
        """Test epsilon decay during training."""
        initial_epsilon = self.agent.epsilon
        
        # Simulate training steps
        for _ in range(10):
            self.agent._train_step()
        
        # Epsilon should have decayed
        self.assertLess(self.agent.epsilon, initial_epsilon)
        self.assertGreaterEqual(self.agent.epsilon, self.config.epsilon_end)
    
    def test_model_save_load(self):
        """Test model saving and loading."""
        # Train agent a bit to change weights
        experiences = [
            Experience(
                state=np.random.rand(18),
                action=0,
                reward=1.0,
                next_state=np.random.rand(18),
                done=False
            )
            for _ in range(5)
        ]
        
        for exp in experiences:
            self.agent.experience_buffer.add(exp)
        
        self.agent._train_step()
        
        # Get initial weights
        initial_weights = self.agent.q_network.state_dict()
        initial_epsilon = self.agent.epsilon
        
        # Save model
        model_path = os.path.join(self.temp_dir, "test_model.pth")
        self.agent.save_model(model_path)
        
        # Verify file exists
        self.assertTrue(os.path.exists(model_path))
        
        # Modify agent state
        for param in self.agent.q_network.parameters():
            param.data += 1.0
        self.agent.epsilon = 0.5
        
        # Load model
        self.agent.load_model(model_path)
        
        # Check that weights are restored
        loaded_weights = self.agent.q_network.state_dict()
        for key in initial_weights:
            self.assertTrue(torch.allclose(initial_weights[key], loaded_weights[key]))
        
        # Check that epsilon is restored
        self.assertEqual(self.agent.epsilon, initial_epsilon)
    
    def test_training_mode(self):
        """Test training mode setting."""
        # Set to training mode
        self.agent.set_training_mode(True)
        self.assertTrue(self.agent.q_network.training)
        
        # Set to evaluation mode
        self.agent.set_training_mode(False)
        self.assertFalse(self.agent.q_network.training)
    
    def test_network_info(self):
        """Test network information retrieval."""
        info = self.agent.get_network_info()
        
        # Check info structure
        self.assertIn('total_parameters', info)
        self.assertIn('trainable_parameters', info)
        self.assertIn('network_architecture', info)
        self.assertIn('device', info)
        self.assertIn('double_dqn', info)
        self.assertIn('dueling_dqn', info)
        
        # Check values
        self.assertIsInstance(info['total_parameters'], int)
        self.assertGreater(info['total_parameters'], 0)
        self.assertEqual(info['double_dqn'], self.config.double_dqn)
        self.assertEqual(info['dueling_dqn'], self.config.dueling_dqn)
    
    def test_persona_integration(self):
        """Test persona integration in DQN agent."""
        # Test persona agent
        self.assertIsNotNone(self.persona_agent.persona)
        self.assertEqual(self.persona_agent.persona.config.name, "Cautious Basic Strategy")
        
        # Create test game state
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.can_double = [False]
        game_state.can_split = [False]
        
        # Get action with persona integration
        action = self.persona_agent.get_action(game_state)
        self.assertIsInstance(action, GameAction)
        
        # Should have tracked decision
        self.assertGreater(len(self.persona_agent.decision_history), 0)
    
    def test_double_dqn_vs_standard(self):
        """Test Double DQN vs Standard DQN."""
        # Create agents with different configurations
        standard_config = DQNConfig(double_dqn=False, batch_size=2, min_buffer_size=2)
        double_config = DQNConfig(double_dqn=True, batch_size=2, min_buffer_size=2)
        
        standard_agent = DQNAgent("Standard", standard_config)
        double_agent = DQNAgent("Double", double_config)
        
        # Create experiences
        experiences = [
            Experience(
                state=np.random.rand(18),
                action=0,
                reward=1.0,
                next_state=np.random.rand(18),
                done=False
            ),
            Experience(
                state=np.random.rand(18),
                action=1,
                reward=-1.0,
                next_state=np.random.rand(18),
                done=True
            )
        ]
        
        # Both should be able to update
        standard_loss = standard_agent._update_model(experiences)
        double_loss = double_agent._update_model(experiences)
        
        self.assertIsInstance(standard_loss, float)
        self.assertIsInstance(double_loss, float)
        self.assertTrue(np.isfinite(standard_loss))
        self.assertTrue(np.isfinite(double_loss))
    
    def test_rl_state_creation(self):
        """Test RLState creation from game state."""
        # Create test game state
        game_state = GameState()
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.ACE))
        hand.add_card(Card(Suit.SPADES, Rank.SIX))
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.KING))
        game_state.player_hands = [hand]
        game_state.dealer_hand = dealer_hand
        game_state.bet_amount = 25.0
        game_state.can_double = [True]
        game_state.can_split = [False]
        
        # Create RLState
        rl_state = self.agent._create_rl_state(game_state, 0)
        
        # Check values
        self.assertEqual(rl_state.player_value, 17)  # Soft 17
        self.assertTrue(rl_state.player_soft)
        self.assertFalse(rl_state.player_pairs)
        self.assertTrue(rl_state.player_can_double)
        self.assertFalse(rl_state.player_can_split)
        self.assertEqual(rl_state.dealer_upcard, 10)  # King = 10
        self.assertEqual(rl_state.bet_amount, 25.0)


if __name__ == '__main__':
    unittest.main()
